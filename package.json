{"name": "audio-transcriber", "version": "0.1.0", "private": true, "dependencies": {"@testing-library/dom": "^10.4.1", "@testing-library/jest-dom": "^6.8.0", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^13.5.0", "cors": "^2.8.5", "dotenv": "^17.2.2", "express": "^5.1.0", "multer": "^2.0.2", "react": "^19.1.1", "react-dom": "^19.1.1", "react-scripts": "5.0.1", "web-vitals": "^2.1.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "server": "node server/server.js", "dev": "concurrently \"npm run server\" \"npm start\""}, "proxy": "http://localhost:5000", "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"autoprefixer": "^10.4.21", "concurrently": "^9.2.1", "postcss": "^8.5.6", "tailwindcss": "^4.1.13"}}