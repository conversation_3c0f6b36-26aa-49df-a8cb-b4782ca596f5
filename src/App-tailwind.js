import React, { useState, useCallback } from 'react';
import './App.css';

// File upload component
const FileUploader = ({ onFilesSelected }) => {
  const [isDragOver, setIsDragOver] = useState(false);

  const handleDragOver = useCallback((e) => {
    e.preventDefault();
    setIsDragOver(true);
  }, []);

  const handleDragLeave = useCallback((e) => {
    e.preventDefault();
    setIsDragOver(false);
  }, []);

  const handleDrop = useCallback((e) => {
    e.preventDefault();
    setIsDragOver(false);
    const files = Array.from(e.dataTransfer.files).filter(file =>
      file.type.startsWith('audio/') || /\.(mp3|wav|m4a|aac|ogg|flac)$/i.test(file.name)
    );
    if (files.length > 0) {
      onFilesSelected(files);
    }
  }, [onFilesSelected]);

  const handleFileInput = useCallback((e) => {
    const files = Array.from(e.target.files);
    if (files.length > 0) {
      onFilesSelected(files);
    }
  }, [onFilesSelected]);

  return (
    <div className="w-full max-w-4xl mx-auto p-6">
      <div
        className={`border-2 border-dashed rounded-lg p-8 text-center transition-colors ${
          isDragOver
            ? 'border-blue-500 bg-blue-50'
            : 'border-gray-300 hover:border-gray-400'
        }`}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
      >
        <div className="space-y-4">
          <div className="text-6xl text-gray-400">🎵</div>
          <div>
            <h3 className="text-lg font-medium text-gray-900">
              Drop audio files here or click to browse
            </h3>
            <p className="text-sm text-gray-500 mt-2">
              Supports MP3, WAV, M4A, AAC, OGG, FLAC (max 100MB each)
            </p>
          </div>
          <input
            type="file"
            multiple
            accept="audio/*,.mp3,.wav,.m4a,.aac,.ogg,.flac"
            onChange={handleFileInput}
            className="hidden"
            id="file-input"
          />
          <label
            htmlFor="file-input"
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 cursor-pointer"
          >
            Choose Files
          </label>
        </div>
      </div>
    </div>
  );
};

// File queue item component
const FileQueueItem = ({ file, status, progress, transcript, onTranscriptEdit }) => {
  const getStatusColor = () => {
    switch (status) {
      case 'queued': return 'text-gray-500';
      case 'uploading': return 'text-blue-500';
      case 'transcribing': return 'text-yellow-500';
      case 'completed': return 'text-green-500';
      case 'error': return 'text-red-500';
      default: return 'text-gray-500';
    }
  };

  const getStatusText = () => {
    switch (status) {
      case 'queued': return 'Queued';
      case 'uploading': return `Uploading... ${progress}%`;
      case 'transcribing': return 'Transcribing...';
      case 'completed': return 'Completed';
      case 'error': return 'Error';
      default: return 'Unknown';
    }
  };

  return (
    <div className="border rounded-lg p-4 mb-4 bg-white shadow-sm">
      <div className="flex items-center justify-between mb-2">
        <h4 className="font-medium text-gray-900 truncate">{file.name}</h4>
        <span className={`text-sm font-medium ${getStatusColor()}`}>
          {getStatusText()}
        </span>
      </div>

      {(status === 'uploading' || status === 'transcribing') && (
        <div className="w-full bg-gray-200 rounded-full h-2 mb-3">
          <div
            className="bg-blue-600 h-2 rounded-full transition-all duration-300"
            style={{ width: `${progress}%` }}
          ></div>
        </div>
      )}

      {transcript && (
        <div className="mt-4">
          <h5 className="font-medium text-gray-700 mb-2">Transcript:</h5>
          <div className="space-y-2 max-h-60 overflow-y-auto">
            {transcript.segments.map((segment, index) => (
              <div key={index} className="flex items-start space-x-3 p-2 bg-gray-50 rounded">
                <div className="text-xs text-gray-500 min-w-0 flex-shrink-0">
                  {Math.floor(segment.start)}s
                </div>
                <div className="text-xs font-medium text-blue-600 min-w-0 flex-shrink-0">
                  {segment.speaker}
                </div>
                <div
                  className="flex-1 text-sm text-gray-800 cursor-text"
                  contentEditable
                  suppressContentEditableWarning={true}
                  onBlur={(e) => onTranscriptEdit(file.name, index, e.target.textContent)}
                >
                  {segment.text}
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

// Export functionality
const ExportButtons = ({ transcripts }) => {
  const downloadJSON = (fileName, transcript) => {
    const dataStr = JSON.stringify(transcript, null, 2);
    const dataUri = 'data:application/json;charset=utf-8,'+ encodeURIComponent(dataStr);

    const exportFileDefaultName = `${fileName.replace(/\.[^/.]+$/, "")}_transcript.json`;

    const linkElement = document.createElement('a');
    linkElement.setAttribute('href', dataUri);
    linkElement.setAttribute('download', exportFileDefaultName);
    linkElement.click();
  };

  const downloadCombinedCSV = () => {
    const csvRows = ['File Name,Start Time,End Time,Speaker,Text'];

    Object.entries(transcripts).forEach(([fileName, transcript]) => {
      transcript.segments.forEach(segment => {
        const row = [
          fileName,
          segment.start,
          segment.end,
          segment.speaker,
          `"${segment.text.replace(/"/g, '""')}"`
        ].join(',');
        csvRows.push(row);
      });
    });

    const csvContent = csvRows.join('\n');
    const dataUri = 'data:text/csv;charset=utf-8,' + encodeURIComponent(csvContent);

    const linkElement = document.createElement('a');
    linkElement.setAttribute('href', dataUri);
    linkElement.setAttribute('download', 'combined_transcripts.csv');
    linkElement.click();
  };

  const hasTranscripts = Object.keys(transcripts).length > 0;

  if (!hasTranscripts) return null;

  return (
    <div className="w-full max-w-4xl mx-auto mt-8 p-6 bg-white rounded-lg shadow-sm">
      <h3 className="text-lg font-medium text-gray-900 mb-4">Export Options</h3>
      <div className="space-y-4">
        <div>
          <h4 className="text-sm font-medium text-gray-700 mb-2">Individual JSON Files:</h4>
          <div className="flex flex-wrap gap-2">
            {Object.entries(transcripts).map(([fileName, transcript]) => (
              <button
                key={fileName}
                onClick={() => downloadJSON(fileName, transcript)}
                className="px-3 py-1 text-sm bg-blue-100 text-blue-700 rounded hover:bg-blue-200"
              >
                {fileName}
              </button>
            ))}
          </div>
        </div>
        <div>
          <button
            onClick={downloadCombinedCSV}
            className="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700"
          >
            Download Combined CSV Report
          </button>
        </div>
      </div>
    </div>
  );
};

// Main App component
function App() {
  const [fileQueue, setFileQueue] = useState([]);
  const [transcripts, setTranscripts] = useState({});

  const updateFileStatus = (id, status, progress) => {
    setFileQueue(prev => prev.map(item =>
      item.id === id ? { ...item, status, progress } : item
    ));
  };

  const processFile = useCallback(async (fileItem) => {
    try {
      // Update status to uploading
      updateFileStatus(fileItem.id, 'uploading', 0);

      const formData = new FormData();
      formData.append('audio', fileItem.file);

      // Simulate upload progress
      for (let i = 0; i <= 100; i += 10) {
        updateFileStatus(fileItem.id, 'uploading', i);
        await new Promise(resolve => setTimeout(resolve, 100));
      }

      // Update status to transcribing
      updateFileStatus(fileItem.id, 'transcribing', 100);

      const response = await fetch('/api/transcribe', {
        method: 'POST',
        body: formData
      });

      if (!response.ok) {
        throw new Error('Transcription failed');
      }

      const transcript = await response.json();

      // Update transcripts and status
      setTranscripts(prev => ({
        ...prev,
        [fileItem.file.name]: transcript
      }));

      updateFileStatus(fileItem.id, 'completed', 100);

    } catch (error) {
      console.error('Error processing file:', error);
      updateFileStatus(fileItem.id, 'error', 0);
    }
  }, []);

  const handleFilesSelected = useCallback((files) => {
    const newFiles = files.map(file => ({
      file,
      id: Date.now() + Math.random(),
      status: 'queued',
      progress: 0
    }));

    setFileQueue(prev => [...prev, ...newFiles]);

    // Start processing files
    newFiles.forEach(fileItem => processFile(fileItem));
  }, [processFile]);

  const handleTranscriptEdit = useCallback((fileName, segmentIndex, newText) => {
    setTranscripts(prev => ({
      ...prev,
      [fileName]: {
        ...prev[fileName],
        segments: prev[fileName].segments.map((segment, index) =>
          index === segmentIndex ? { ...segment, text: newText } : segment
        )
      }
    }));
  }, []);

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto py-8">
        <header className="text-center mb-8">
          <h1 className="text-4xl font-bold text-gray-900 mb-2">
            🎙️ Audio Transcriber
          </h1>
          <p className="text-lg text-gray-600">
            Upload audio files for automatic transcription and speaker diarization
          </p>
        </header>

        <FileUploader onFilesSelected={handleFilesSelected} />

        {fileQueue.length > 0 && (
          <div className="w-full max-w-4xl mx-auto mt-8 p-6">
            <h2 className="text-2xl font-bold text-gray-900 mb-4">
              Processing Queue ({fileQueue.length} files)
            </h2>
            <div className="space-y-4">
              {fileQueue.map(fileItem => (
                <FileQueueItem
                  key={fileItem.id}
                  file={fileItem.file}
                  status={fileItem.status}
                  progress={fileItem.progress}
                  transcript={transcripts[fileItem.file.name]}
                  onTranscriptEdit={handleTranscriptEdit}
                />
              ))}
            </div>
          </div>
        )}

        <ExportButtons transcripts={transcripts} />
      </div>
    </div>
  );
}

export default App;
