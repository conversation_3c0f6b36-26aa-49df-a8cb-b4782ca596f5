import React, { useState, useCallback } from 'react';
import './App.css';

// Simple file upload component without TailwindCSS
const FileUploader = ({ onFilesSelected }) => {
  const [isDragOver, setIsDragOver] = useState(false);

  const handleDragOver = useCallback((e) => {
    e.preventDefault();
    setIsDragOver(true);
  }, []);

  const handleDragLeave = useCallback((e) => {
    e.preventDefault();
    setIsDragOver(false);
  }, []);

  const handleDrop = useCallback((e) => {
    e.preventDefault();
    setIsDragOver(false);
    const files = Array.from(e.dataTransfer.files).filter(file => 
      file.type.startsWith('audio/') || /\.(mp3|wav|m4a|aac|ogg|flac)$/i.test(file.name)
    );
    if (files.length > 0) {
      onFilesSelected(files);
    }
  }, [onFilesSelected]);

  const handleFileInput = useCallback((e) => {
    const files = Array.from(e.target.files);
    if (files.length > 0) {
      onFilesSelected(files);
    }
  }, [onFilesSelected]);

  return (
    <div style={{ maxWidth: '800px', margin: '0 auto', padding: '20px' }}>
      <div
        style={{
          border: isDragOver ? '2px dashed #007bff' : '2px dashed #ccc',
          borderRadius: '8px',
          padding: '40px',
          textAlign: 'center',
          backgroundColor: isDragOver ? '#f8f9fa' : 'white'
        }}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
      >
        <div style={{ fontSize: '48px', marginBottom: '20px' }}>🎵</div>
        <h3>Drop audio files here or click to browse</h3>
        <p style={{ color: '#666', marginTop: '10px' }}>
          Supports MP3, WAV, M4A, AAC, OGG, FLAC (max 100MB each)
        </p>
        <input
          type="file"
          multiple
          accept="audio/*,.mp3,.wav,.m4a,.aac,.ogg,.flac"
          onChange={handleFileInput}
          style={{ display: 'none' }}
          id="file-input"
        />
        <label
          htmlFor="file-input"
          style={{
            display: 'inline-block',
            padding: '10px 20px',
            backgroundColor: '#007bff',
            color: 'white',
            borderRadius: '4px',
            cursor: 'pointer',
            marginTop: '20px'
          }}
        >
          Choose Files
        </label>
      </div>
    </div>
  );
};

// Enhanced transcript card component
const TranscriptCard = ({ fileName, transcript, onTranscriptEdit, onDownloadJSON }) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const [selectedSegment, setSelectedSegment] = useState(null);

  const formatTime = (seconds) => {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const getWordCount = () => {
    return transcript.segments.reduce((count, segment) => {
      return count + segment.text.split(' ').length;
    }, 0);
  };

  const getTotalDuration = () => {
    if (transcript.segments.length === 0) return 0;
    return Math.max(...transcript.segments.map(s => s.end));
  };

  const getSpeakerStats = () => {
    const speakers = {};
    transcript.segments.forEach(segment => {
      if (!speakers[segment.speaker]) {
        speakers[segment.speaker] = { count: 0, duration: 0 };
      }
      speakers[segment.speaker].count++;
      speakers[segment.speaker].duration += (segment.end - segment.start);
    });
    return speakers;
  };

  return (
    <div style={{
      backgroundColor: 'white',
      borderRadius: '12px',
      boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',
      marginBottom: '20px',
      overflow: 'hidden',
      border: '1px solid #e5e7eb'
    }}>
      {/* Card Header */}
      <div style={{
        padding: '20px',
        borderBottom: '1px solid #f3f4f6',
        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        color: 'white'
      }}>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <div>
            <h3 style={{ margin: 0, fontSize: '18px', fontWeight: 'bold' }}>
              🎵 {fileName}
            </h3>
            <div style={{ marginTop: '8px', fontSize: '14px', opacity: 0.9 }}>
              {getWordCount()} words • {formatTime(getTotalDuration())} duration • {transcript.segments.length} segments
            </div>
          </div>
          <div style={{ display: 'flex', gap: '10px' }}>
            <button
              onClick={() => onDownloadJSON(fileName, transcript)}
              style={{
                padding: '8px 16px',
                backgroundColor: 'rgba(255, 255, 255, 0.2)',
                color: 'white',
                border: '1px solid rgba(255, 255, 255, 0.3)',
                borderRadius: '6px',
                cursor: 'pointer',
                fontSize: '12px'
              }}
            >
              📄 JSON
            </button>
            <button
              onClick={() => setIsExpanded(!isExpanded)}
              style={{
                padding: '8px 16px',
                backgroundColor: 'rgba(255, 255, 255, 0.2)',
                color: 'white',
                border: '1px solid rgba(255, 255, 255, 0.3)',
                borderRadius: '6px',
                cursor: 'pointer',
                fontSize: '12px'
              }}
            >
              {isExpanded ? '📖 Collapse' : '📖 Expand'}
            </button>
          </div>
        </div>
      </div>

      {/* Speaker Statistics */}
      <div style={{ padding: '16px', backgroundColor: '#f9fafb', borderBottom: '1px solid #f3f4f6' }}>
        <h4 style={{ margin: '0 0 12px 0', fontSize: '14px', color: '#374151' }}>Speaker Breakdown:</h4>
        <div style={{ display: 'flex', gap: '16px', flexWrap: 'wrap' }}>
          {Object.entries(getSpeakerStats()).map(([speaker, stats]) => (
            <div key={speaker} style={{
              display: 'flex',
              alignItems: 'center',
              gap: '8px',
              padding: '6px 12px',
              backgroundColor: 'white',
              borderRadius: '20px',
              border: '1px solid #e5e7eb'
            }}>
              <div style={{
                width: '12px',
                height: '12px',
                borderRadius: '50%',
                backgroundColor: speaker === 'Speaker 1' ? '#3b82f6' : speaker === 'Speaker 2' ? '#10b981' : '#f59e0b'
              }}></div>
              <span style={{ fontSize: '12px', fontWeight: '500' }}>
                {speaker}: {stats.count} segments ({formatTime(stats.duration)})
              </span>
            </div>
          ))}
        </div>
      </div>

      {/* Full Transcript Preview */}
      <div style={{ padding: '16px' }}>
        <h4 style={{ margin: '0 0 12px 0', fontSize: '14px', color: '#374151' }}>Full Transcript:</h4>
        <div style={{
          backgroundColor: '#f8fafc',
          padding: '16px',
          borderRadius: '8px',
          fontSize: '14px',
          lineHeight: '1.6',
          color: '#1f2937',
          maxHeight: isExpanded ? 'none' : '100px',
          overflow: 'hidden',
          position: 'relative'
        }}>
          {transcript.transcript}
          {!isExpanded && transcript.transcript.length > 200 && (
            <div style={{
              position: 'absolute',
              bottom: 0,
              left: 0,
              right: 0,
              height: '40px',
              background: 'linear-gradient(transparent, #f8fafc)',
              display: 'flex',
              alignItems: 'end',
              justifyContent: 'center',
              paddingBottom: '8px'
            }}>
              <button
                onClick={() => setIsExpanded(true)}
                style={{
                  padding: '4px 12px',
                  backgroundColor: '#3b82f6',
                  color: 'white',
                  border: 'none',
                  borderRadius: '4px',
                  fontSize: '12px',
                  cursor: 'pointer'
                }}
              >
                Read More
              </button>
            </div>
          )}
        </div>
      </div>

      {/* Detailed Segments */}
      {isExpanded && (
        <div style={{ padding: '16px', borderTop: '1px solid #f3f4f6' }}>
          <h4 style={{ margin: '0 0 16px 0', fontSize: '14px', color: '#374151' }}>Detailed Timeline:</h4>
          <div style={{ maxHeight: '400px', overflowY: 'auto' }}>
            {transcript.segments.map((segment, index) => (
              <div
                key={index}
                style={{
                  display: 'flex',
                  gap: '12px',
                  padding: '12px',
                  marginBottom: '8px',
                  backgroundColor: selectedSegment === index ? '#eff6ff' : '#ffffff',
                  border: selectedSegment === index ? '2px solid #3b82f6' : '1px solid #e5e7eb',
                  borderRadius: '8px',
                  cursor: 'pointer',
                  transition: 'all 0.2s ease'
                }}
                onClick={() => setSelectedSegment(selectedSegment === index ? null : index)}
              >
                <div style={{
                  minWidth: '60px',
                  fontSize: '12px',
                  color: '#6b7280',
                  fontWeight: '500',
                  textAlign: 'center',
                  padding: '4px 8px',
                  backgroundColor: '#f3f4f6',
                  borderRadius: '4px'
                }}>
                  {formatTime(segment.start)}
                </div>
                <div style={{
                  minWidth: '80px',
                  fontSize: '12px',
                  fontWeight: 'bold',
                  color: segment.speaker === 'Speaker 1' ? '#3b82f6' : segment.speaker === 'Speaker 2' ? '#10b981' : '#f59e0b',
                  display: 'flex',
                  alignItems: 'center',
                  gap: '6px'
                }}>
                  <div style={{
                    width: '8px',
                    height: '8px',
                    borderRadius: '50%',
                    backgroundColor: segment.speaker === 'Speaker 1' ? '#3b82f6' : segment.speaker === 'Speaker 2' ? '#10b981' : '#f59e0b'
                  }}></div>
                  {segment.speaker}
                </div>
                <div
                  style={{
                    flex: 1,
                    fontSize: '14px',
                    lineHeight: '1.5',
                    color: '#1f2937'
                  }}
                  contentEditable
                  suppressContentEditableWarning={true}
                  onBlur={(e) => onTranscriptEdit(fileName, index, e.target.textContent)}
                >
                  {segment.text}
                </div>
                {selectedSegment === index && (
                  <div style={{
                    fontSize: '12px',
                    color: '#6b7280',
                    alignSelf: 'flex-start',
                    padding: '2px 6px',
                    backgroundColor: '#f9fafb',
                    borderRadius: '4px'
                  }}>
                    Click to edit
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

// Simple file queue item component
const FileQueueItem = ({ file, status, progress, transcript, onTranscriptEdit, onDownloadJSON }) => {
  const getStatusColor = () => {
    switch (status) {
      case 'queued': return '#666';
      case 'uploading': return '#007bff';
      case 'transcribing': return '#ffc107';
      case 'completed': return '#28a745';
      case 'error': return '#dc3545';
      default: return '#666';
    }
  };

  const getStatusText = () => {
    switch (status) {
      case 'queued': return 'Queued';
      case 'uploading': return `Uploading... ${progress}%`;
      case 'transcribing': return 'Transcribing...';
      case 'completed': return 'Completed';
      case 'error': return 'Error';
      default: return 'Unknown';
    }
  };

  return (
    <div style={{ border: '1px solid #ddd', borderRadius: '8px', padding: '20px', marginBottom: '20px', backgroundColor: 'white' }}>
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '10px' }}>
        <h4 style={{ margin: 0, overflow: 'hidden', textOverflow: 'ellipsis' }}>{file.name}</h4>
        <span style={{ color: getStatusColor(), fontWeight: 'bold' }}>
          {getStatusText()}
        </span>
      </div>
      
      {(status === 'uploading' || status === 'transcribing') && (
        <div style={{ width: '100%', backgroundColor: '#e9ecef', borderRadius: '4px', height: '8px', marginBottom: '15px' }}>
          <div 
            style={{ 
              backgroundColor: '#007bff', 
              height: '8px', 
              borderRadius: '4px',
              width: `${progress}%`,
              transition: 'width 0.3s ease'
            }}
          ></div>
        </div>
      )}

      {transcript && status === 'completed' && (
        <div style={{ marginTop: '20px' }}>
          <TranscriptCard
            fileName={file.name}
            transcript={transcript}
            onTranscriptEdit={onTranscriptEdit}
            onDownloadJSON={onDownloadJSON}
          />
        </div>
      )}
    </div>
  );
};

// Enhanced Export functionality
const ExportButtons = ({ transcripts }) => {
  const downloadCombinedCSV = () => {
    const csvRows = ['File Name,Start Time,End Time,Speaker,Text'];

    Object.entries(transcripts).forEach(([fileName, transcript]) => {
      transcript.segments.forEach(segment => {
        const row = [
          fileName,
          segment.start,
          segment.end,
          segment.speaker,
          `"${segment.text.replace(/"/g, '""')}"`
        ].join(',');
        csvRows.push(row);
      });
    });

    const csvContent = csvRows.join('\n');
    const dataUri = 'data:text/csv;charset=utf-8,' + encodeURIComponent(csvContent);

    const linkElement = document.createElement('a');
    linkElement.setAttribute('href', dataUri);
    linkElement.setAttribute('download', 'combined_transcripts.csv');
    linkElement.click();
  };

  const downloadAllJSON = () => {
    const allTranscripts = {
      exportDate: new Date().toISOString(),
      totalFiles: Object.keys(transcripts).length,
      transcripts: transcripts
    };

    const dataStr = JSON.stringify(allTranscripts, null, 2);
    const dataUri = 'data:application/json;charset=utf-8,'+ encodeURIComponent(dataStr);

    const linkElement = document.createElement('a');
    linkElement.setAttribute('href', dataUri);
    linkElement.setAttribute('download', 'all_transcripts.json');
    linkElement.click();
  };

  const hasTranscripts = Object.keys(transcripts).length > 0;

  if (!hasTranscripts) return null;

  return (
    <div style={{
      maxWidth: '800px',
      margin: '20px auto',
      padding: '24px',
      backgroundColor: 'white',
      borderRadius: '12px',
      boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',
      border: '1px solid #e5e7eb'
    }}>
      <div style={{ marginBottom: '20px' }}>
        <h3 style={{ margin: '0 0 8px 0', fontSize: '18px', color: '#1f2937' }}>📊 Export All Transcripts</h3>
        <p style={{ margin: 0, fontSize: '14px', color: '#6b7280' }}>
          Download all {Object.keys(transcripts).length} transcript{Object.keys(transcripts).length !== 1 ? 's' : ''} in your preferred format
        </p>
      </div>

      <div style={{ display: 'flex', gap: '12px', flexWrap: 'wrap' }}>
        <button
          onClick={downloadCombinedCSV}
          style={{
            padding: '12px 24px',
            backgroundColor: '#10b981',
            color: 'white',
            border: 'none',
            borderRadius: '8px',
            cursor: 'pointer',
            fontSize: '14px',
            fontWeight: '500',
            display: 'flex',
            alignItems: 'center',
            gap: '8px',
            boxShadow: '0 2px 4px rgba(16, 185, 129, 0.2)'
          }}
        >
          📊 Combined CSV Report
        </button>

        <button
          onClick={downloadAllJSON}
          style={{
            padding: '12px 24px',
            backgroundColor: '#3b82f6',
            color: 'white',
            border: 'none',
            borderRadius: '8px',
            cursor: 'pointer',
            fontSize: '14px',
            fontWeight: '500',
            display: 'flex',
            alignItems: 'center',
            gap: '8px',
            boxShadow: '0 2px 4px rgba(59, 130, 246, 0.2)'
          }}
        >
          📄 All JSON Data
        </button>
      </div>

      <div style={{
        marginTop: '16px',
        padding: '12px',
        backgroundColor: '#f8fafc',
        borderRadius: '6px',
        fontSize: '12px',
        color: '#64748b'
      }}>
        💡 Tip: Individual JSON files can be downloaded from each transcript card above
      </div>
    </div>
  );
};

// Main App component
function App() {
  const [fileQueue, setFileQueue] = useState([]);
  const [transcripts, setTranscripts] = useState({});

  const updateFileStatus = (id, status, progress) => {
    setFileQueue(prev => prev.map(item => 
      item.id === id ? { ...item, status, progress } : item
    ));
  };

  const processFile = useCallback(async (fileItem) => {
    try {
      // Update status to uploading
      updateFileStatus(fileItem.id, 'uploading', 0);
      
      const formData = new FormData();
      formData.append('audio', fileItem.file);
      
      // Simulate upload progress
      for (let i = 0; i <= 100; i += 10) {
        updateFileStatus(fileItem.id, 'uploading', i);
        await new Promise(resolve => setTimeout(resolve, 100));
      }
      
      // Update status to transcribing
      updateFileStatus(fileItem.id, 'transcribing', 100);
      
      const response = await fetch('/api/transcribe', {
        method: 'POST',
        body: formData
      });
      
      if (!response.ok) {
        throw new Error('Transcription failed');
      }
      
      const transcript = await response.json();
      
      // Update transcripts and status
      setTranscripts(prev => ({
        ...prev,
        [fileItem.file.name]: transcript
      }));
      
      updateFileStatus(fileItem.id, 'completed', 100);
      
    } catch (error) {
      console.error('Error processing file:', error);
      updateFileStatus(fileItem.id, 'error', 0);
    }
  }, []);

  const handleFilesSelected = useCallback((files) => {
    const newFiles = files.map(file => ({
      file,
      id: Date.now() + Math.random(),
      status: 'queued',
      progress: 0
    }));
    
    setFileQueue(prev => [...prev, ...newFiles]);
    
    // Start processing files
    newFiles.forEach(fileItem => processFile(fileItem));
  }, [processFile]);

  const handleTranscriptEdit = useCallback((fileName, segmentIndex, newText) => {
    setTranscripts(prev => ({
      ...prev,
      [fileName]: {
        ...prev[fileName],
        segments: prev[fileName].segments.map((segment, index) =>
          index === segmentIndex ? { ...segment, text: newText } : segment
        )
      }
    }));
  }, []);

  const downloadJSON = useCallback((fileName, transcript) => {
    const dataStr = JSON.stringify(transcript, null, 2);
    const dataUri = 'data:application/json;charset=utf-8,'+ encodeURIComponent(dataStr);

    const exportFileDefaultName = `${fileName.replace(/\.[^/.]+$/, "")}_transcript.json`;

    const linkElement = document.createElement('a');
    linkElement.setAttribute('href', dataUri);
    linkElement.setAttribute('download', exportFileDefaultName);
    linkElement.click();
  }, []);

  return (
    <div style={{ minHeight: '100vh', backgroundColor: '#f8f9fa' }}>
      <div style={{ padding: '40px 20px' }}>
        <header style={{ textAlign: 'center', marginBottom: '40px' }}>
          <h1 style={{ fontSize: '36px', marginBottom: '10px' }}>
            🎙️ Audio Transcriber
          </h1>
          <p style={{ fontSize: '18px', color: '#666' }}>
            Upload audio files for automatic transcription and speaker diarization
          </p>
        </header>

        <FileUploader onFilesSelected={handleFilesSelected} />

        {fileQueue.length > 0 && (
          <div style={{ maxWidth: '800px', margin: '40px auto 0', padding: '0 20px' }}>
            <h2 style={{ marginBottom: '20px' }}>
              Processing Queue ({fileQueue.length} files)
            </h2>
            <div>
              {fileQueue.map(fileItem => (
                <FileQueueItem
                  key={fileItem.id}
                  file={fileItem.file}
                  status={fileItem.status}
                  progress={fileItem.progress}
                  transcript={transcripts[fileItem.file.name]}
                  onTranscriptEdit={handleTranscriptEdit}
                  onDownloadJSON={downloadJSON}
                />
              ))}
            </div>
          </div>
        )}

        {/* Completed Transcripts Section */}
        {Object.keys(transcripts).length > 0 && (
          <div style={{ maxWidth: '800px', margin: '40px auto 0', padding: '0 20px' }}>
            <h2 style={{ marginBottom: '20px', fontSize: '24px', color: '#1f2937' }}>
              📚 Completed Transcripts ({Object.keys(transcripts).length})
            </h2>
            <div>
              {Object.entries(transcripts).map(([fileName, transcript]) => (
                <TranscriptCard
                  key={fileName}
                  fileName={fileName}
                  transcript={transcript}
                  onTranscriptEdit={handleTranscriptEdit}
                  onDownloadJSON={downloadJSON}
                />
              ))}
            </div>
          </div>
        )}

        <ExportButtons transcripts={transcripts} />
      </div>
    </div>
  );
}

export default App;
