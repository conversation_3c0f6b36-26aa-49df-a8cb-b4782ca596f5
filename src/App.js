import React, { useState, useCallback } from 'react';
import './App.css';

// Simple file upload component without TailwindCSS
const FileUploader = ({ onFilesSelected }) => {
  const [isDragOver, setIsDragOver] = useState(false);

  const handleDragOver = useCallback((e) => {
    e.preventDefault();
    setIsDragOver(true);
  }, []);

  const handleDragLeave = useCallback((e) => {
    e.preventDefault();
    setIsDragOver(false);
  }, []);

  const handleDrop = useCallback((e) => {
    e.preventDefault();
    setIsDragOver(false);
    const files = Array.from(e.dataTransfer.files).filter(file => 
      file.type.startsWith('audio/') || /\.(mp3|wav|m4a|aac|ogg|flac)$/i.test(file.name)
    );
    if (files.length > 0) {
      onFilesSelected(files);
    }
  }, [onFilesSelected]);

  const handleFileInput = useCallback((e) => {
    const files = Array.from(e.target.files);
    if (files.length > 0) {
      onFilesSelected(files);
    }
  }, [onFilesSelected]);

  return (
    <div style={{ maxWidth: '800px', margin: '0 auto', padding: '20px' }}>
      <div
        style={{
          border: isDragOver ? '2px dashed #007bff' : '2px dashed #ccc',
          borderRadius: '8px',
          padding: '40px',
          textAlign: 'center',
          backgroundColor: isDragOver ? '#f8f9fa' : 'white'
        }}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
      >
        <div style={{ fontSize: '48px', marginBottom: '20px' }}>🎵</div>
        <h3>Drop audio files here or click to browse</h3>
        <p style={{ color: '#666', marginTop: '10px' }}>
          Supports MP3, WAV, M4A, AAC, OGG, FLAC (max 100MB each)
        </p>
        <input
          type="file"
          multiple
          accept="audio/*,.mp3,.wav,.m4a,.aac,.ogg,.flac"
          onChange={handleFileInput}
          style={{ display: 'none' }}
          id="file-input"
        />
        <label
          htmlFor="file-input"
          style={{
            display: 'inline-block',
            padding: '10px 20px',
            backgroundColor: '#007bff',
            color: 'white',
            borderRadius: '4px',
            cursor: 'pointer',
            marginTop: '20px'
          }}
        >
          Choose Files
        </label>
      </div>
    </div>
  );
};

// Simple file queue item component
const FileQueueItem = ({ file, status, progress, transcript, onTranscriptEdit }) => {
  const getStatusColor = () => {
    switch (status) {
      case 'queued': return '#666';
      case 'uploading': return '#007bff';
      case 'transcribing': return '#ffc107';
      case 'completed': return '#28a745';
      case 'error': return '#dc3545';
      default: return '#666';
    }
  };

  const getStatusText = () => {
    switch (status) {
      case 'queued': return 'Queued';
      case 'uploading': return `Uploading... ${progress}%`;
      case 'transcribing': return 'Transcribing...';
      case 'completed': return 'Completed';
      case 'error': return 'Error';
      default: return 'Unknown';
    }
  };

  return (
    <div style={{ border: '1px solid #ddd', borderRadius: '8px', padding: '20px', marginBottom: '20px', backgroundColor: 'white' }}>
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '10px' }}>
        <h4 style={{ margin: 0, overflow: 'hidden', textOverflow: 'ellipsis' }}>{file.name}</h4>
        <span style={{ color: getStatusColor(), fontWeight: 'bold' }}>
          {getStatusText()}
        </span>
      </div>
      
      {(status === 'uploading' || status === 'transcribing') && (
        <div style={{ width: '100%', backgroundColor: '#e9ecef', borderRadius: '4px', height: '8px', marginBottom: '15px' }}>
          <div 
            style={{ 
              backgroundColor: '#007bff', 
              height: '8px', 
              borderRadius: '4px',
              width: `${progress}%`,
              transition: 'width 0.3s ease'
            }}
          ></div>
        </div>
      )}

      {transcript && (
        <div style={{ marginTop: '20px' }}>
          <h5 style={{ marginBottom: '10px' }}>Transcript:</h5>
          <div style={{ maxHeight: '240px', overflowY: 'auto' }}>
            {transcript.segments.map((segment, index) => (
              <div key={index} style={{ display: 'flex', alignItems: 'flex-start', gap: '10px', padding: '8px', backgroundColor: '#f8f9fa', borderRadius: '4px', marginBottom: '8px' }}>
                <div style={{ fontSize: '12px', color: '#666', minWidth: '40px' }}>
                  {Math.floor(segment.start)}s
                </div>
                <div style={{ fontSize: '12px', fontWeight: 'bold', color: '#007bff', minWidth: '80px' }}>
                  {segment.speaker}
                </div>
                <div 
                  style={{ flex: 1, fontSize: '14px', cursor: 'text' }}
                  contentEditable
                  suppressContentEditableWarning={true}
                  onBlur={(e) => onTranscriptEdit(file.name, index, e.target.textContent)}
                >
                  {segment.text}
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

// Export functionality
const ExportButtons = ({ transcripts }) => {
  const downloadJSON = (fileName, transcript) => {
    const dataStr = JSON.stringify(transcript, null, 2);
    const dataUri = 'data:application/json;charset=utf-8,'+ encodeURIComponent(dataStr);
    
    const exportFileDefaultName = `${fileName.replace(/\.[^/.]+$/, "")}_transcript.json`;
    
    const linkElement = document.createElement('a');
    linkElement.setAttribute('href', dataUri);
    linkElement.setAttribute('download', exportFileDefaultName);
    linkElement.click();
  };

  const downloadCombinedCSV = () => {
    const csvRows = ['File Name,Start Time,End Time,Speaker,Text'];
    
    Object.entries(transcripts).forEach(([fileName, transcript]) => {
      transcript.segments.forEach(segment => {
        const row = [
          fileName,
          segment.start,
          segment.end,
          segment.speaker,
          `"${segment.text.replace(/"/g, '""')}"`
        ].join(',');
        csvRows.push(row);
      });
    });
    
    const csvContent = csvRows.join('\n');
    const dataUri = 'data:text/csv;charset=utf-8,' + encodeURIComponent(csvContent);
    
    const linkElement = document.createElement('a');
    linkElement.setAttribute('href', dataUri);
    linkElement.setAttribute('download', 'combined_transcripts.csv');
    linkElement.click();
  };

  const hasTranscripts = Object.keys(transcripts).length > 0;

  if (!hasTranscripts) return null;

  return (
    <div style={{ maxWidth: '800px', margin: '20px auto', padding: '20px', backgroundColor: 'white', borderRadius: '8px', border: '1px solid #ddd' }}>
      <h3>Export Options</h3>
      <div style={{ marginBottom: '20px' }}>
        <h4 style={{ fontSize: '14px', marginBottom: '10px' }}>Individual JSON Files:</h4>
        <div style={{ display: 'flex', flexWrap: 'wrap', gap: '8px' }}>
          {Object.entries(transcripts).map(([fileName, transcript]) => (
            <button
              key={fileName}
              onClick={() => downloadJSON(fileName, transcript)}
              style={{
                padding: '6px 12px',
                fontSize: '12px',
                backgroundColor: '#e3f2fd',
                color: '#1976d2',
                border: 'none',
                borderRadius: '4px',
                cursor: 'pointer'
              }}
            >
              {fileName}
            </button>
          ))}
        </div>
      </div>
      <div>
        <button
          onClick={downloadCombinedCSV}
          style={{
            padding: '10px 20px',
            backgroundColor: '#28a745',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            cursor: 'pointer'
          }}
        >
          Download Combined CSV Report
        </button>
      </div>
    </div>
  );
};

// Main App component
function App() {
  const [fileQueue, setFileQueue] = useState([]);
  const [transcripts, setTranscripts] = useState({});

  const updateFileStatus = (id, status, progress) => {
    setFileQueue(prev => prev.map(item => 
      item.id === id ? { ...item, status, progress } : item
    ));
  };

  const processFile = useCallback(async (fileItem) => {
    try {
      // Update status to uploading
      updateFileStatus(fileItem.id, 'uploading', 0);
      
      const formData = new FormData();
      formData.append('audio', fileItem.file);
      
      // Simulate upload progress
      for (let i = 0; i <= 100; i += 10) {
        updateFileStatus(fileItem.id, 'uploading', i);
        await new Promise(resolve => setTimeout(resolve, 100));
      }
      
      // Update status to transcribing
      updateFileStatus(fileItem.id, 'transcribing', 100);
      
      const response = await fetch('/api/transcribe', {
        method: 'POST',
        body: formData
      });
      
      if (!response.ok) {
        throw new Error('Transcription failed');
      }
      
      const transcript = await response.json();
      
      // Update transcripts and status
      setTranscripts(prev => ({
        ...prev,
        [fileItem.file.name]: transcript
      }));
      
      updateFileStatus(fileItem.id, 'completed', 100);
      
    } catch (error) {
      console.error('Error processing file:', error);
      updateFileStatus(fileItem.id, 'error', 0);
    }
  }, []);

  const handleFilesSelected = useCallback((files) => {
    const newFiles = files.map(file => ({
      file,
      id: Date.now() + Math.random(),
      status: 'queued',
      progress: 0
    }));
    
    setFileQueue(prev => [...prev, ...newFiles]);
    
    // Start processing files
    newFiles.forEach(fileItem => processFile(fileItem));
  }, [processFile]);

  const handleTranscriptEdit = useCallback((fileName, segmentIndex, newText) => {
    setTranscripts(prev => ({
      ...prev,
      [fileName]: {
        ...prev[fileName],
        segments: prev[fileName].segments.map((segment, index) =>
          index === segmentIndex ? { ...segment, text: newText } : segment
        )
      }
    }));
  }, []);

  return (
    <div style={{ minHeight: '100vh', backgroundColor: '#f8f9fa' }}>
      <div style={{ padding: '40px 20px' }}>
        <header style={{ textAlign: 'center', marginBottom: '40px' }}>
          <h1 style={{ fontSize: '36px', marginBottom: '10px' }}>
            🎙️ Audio Transcriber
          </h1>
          <p style={{ fontSize: '18px', color: '#666' }}>
            Upload audio files for automatic transcription and speaker diarization
          </p>
        </header>

        <FileUploader onFilesSelected={handleFilesSelected} />

        {fileQueue.length > 0 && (
          <div style={{ maxWidth: '800px', margin: '40px auto 0', padding: '0 20px' }}>
            <h2 style={{ marginBottom: '20px' }}>
              Processing Queue ({fileQueue.length} files)
            </h2>
            <div>
              {fileQueue.map(fileItem => (
                <FileQueueItem
                  key={fileItem.id}
                  file={fileItem.file}
                  status={fileItem.status}
                  progress={fileItem.progress}
                  transcript={transcripts[fileItem.file.name]}
                  onTranscriptEdit={handleTranscriptEdit}
                />
              ))}
            </div>
          </div>
        )}

        <ExportButtons transcripts={transcripts} />
      </div>
    </div>
  );
}

export default App;
