import { render, screen } from '@testing-library/react';
import App from './App';

test('renders audio transcriber title', () => {
  render(<App />);
  const titleElement = screen.getByText(/Audio Transcriber/i);
  expect(titleElement).toBeInTheDocument();
});

test('renders file upload area', () => {
  render(<App />);
  const uploadText = screen.getByText(/Drop audio files here or click to browse/i);
  expect(uploadText).toBeInTheDocument();
});

test('renders supported formats text', () => {
  render(<App />);
  const formatsText = screen.getByText(/Supports MP3, WAV, M4A/i);
  expect(formatsText).toBeInTheDocument();
});
