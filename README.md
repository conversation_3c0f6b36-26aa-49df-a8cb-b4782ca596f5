# 🎙️ Audio Transcriber

A React + Node.js application for audio transcription with speaker diarization. Upload single or multiple audio files and get line-by-line transcripts with timestamps and speaker labels.

## Features

- ✅ Single & bulk audio file upload (drag & drop support)
- ✅ Real-time processing queue with status tracking
- ✅ Line-by-line transcript display with timestamps
- ✅ Speaker diarization (Speaker 1, Speaker 2, etc.)
- ✅ Inline transcript editing
- ✅ Export individual JSON files per audio file
- ✅ Export combined CSV report with all transcripts
- ✅ Clean, responsive UI with TailwindCSS

## Supported Audio Formats

MP3, WAV, M4A, AAC, OGG, FLAC (max 100MB per file)

## Tech Stack

- **Frontend**: React 19, TailwindCSS
- **Backend**: Node.js, Express, Multer
- **ASR Provider**: **Gemini AI 2.0 Flash** with real audio transcription and speaker diarization
- **Styling**: Currently using inline styles (TailwindCSS version available in `src/App-tailwind.js`)

## Quick Start

1. **Install dependencies:**
   ```bash
   npm install
   ```

2. **Set up environment variables:**
   ```bash
   # Add your Gemini API key to .env file
   GEMINI_API_KEY=your_actual_gemini_api_key_here
   ```
   Get your API key from: https://makersuite.google.com/app/apikey

3. **Run the application:**
   ```bash
   # Run both frontend and backend
   npm run dev

   # Or run separately:
   npm run server  # Backend only (port 5000)
   npm start       # Frontend only (port 3000)
   ```

4. **Open your browser:**
   - Frontend: [http://localhost:5001](http://localhost:5001) (or the port shown in terminal)
   - Backend API: [http://localhost:5000/api/health](http://localhost:5000/api/health)

## Available Scripts

### `npm run dev`
Runs both frontend and backend concurrently in development mode.

### `npm start`
Runs the React frontend in development mode (will auto-select available port, typically 5001 if backend is running).

### `npm run server`
Runs the Express backend server on port 5000.

### `npm test`

Launches the test runner in the interactive watch mode.\
See the section about [running tests](https://facebook.github.io/create-react-app/docs/running-tests) for more information.

### `npm run build`

Builds the app for production to the `build` folder.\
It correctly bundles React in production mode and optimizes the build for the best performance.

The build is minified and the filenames include the hashes.\
Your app is ready to be deployed!

See the section about [deployment](https://facebook.github.io/create-react-app/docs/deployment) for more information.

### `npm run eject`

**Note: this is a one-way operation. Once you `eject`, you can't go back!**

If you aren't satisfied with the build tool and configuration choices, you can `eject` at any time. This command will remove the single build dependency from your project.

Instead, it will copy all the configuration files and the transitive dependencies (webpack, Babel, ESLint, etc) right into your project so you have full control over them. All of the commands except `eject` will still work, but they will point to the copied scripts so you can tweak them. At this point you're on your own.

You don't have to ever use `eject`. The curated feature set is suitable for small and middle deployments, and you shouldn't feel obligated to use this feature. However we understand that this tool wouldn't be useful if you couldn't customize it when you are ready for it.

## Learn More

You can learn more in the [Create React App documentation](https://facebook.github.io/create-react-app/docs/getting-started).

To learn React, check out the [React documentation](https://reactjs.org/).

### Code Splitting

This section has moved here: [https://facebook.github.io/create-react-app/docs/code-splitting](https://facebook.github.io/create-react-app/docs/code-splitting)

### Analyzing the Bundle Size

This section has moved here: [https://facebook.github.io/create-react-app/docs/analyzing-the-bundle-size](https://facebook.github.io/create-react-app/docs/analyzing-the-bundle-size)

### Making a Progressive Web App

This section has moved here: [https://facebook.github.io/create-react-app/docs/making-a-progressive-web-app](https://facebook.github.io/create-react-app/docs/making-a-progressive-web-app)

### Advanced Configuration

This section has moved here: [https://facebook.github.io/create-react-app/docs/advanced-configuration](https://facebook.github.io/create-react-app/docs/advanced-configuration)

### Deployment

This section has moved here: [https://facebook.github.io/create-react-app/docs/deployment](https://facebook.github.io/create-react-app/docs/deployment)

## API Endpoints

### `GET /api/health`
Health check endpoint that returns server status.

**Response:**
```json
{
  "status": "OK",
  "message": "Audio Transcriber API is running"
}
```

### `POST /api/transcribe`
Upload an audio file for transcription.

**Request:**
- Method: POST
- Content-Type: multipart/form-data
- Body: Form data with 'audio' field containing the audio file

**Response:**
```json
{
  "fileName": "audio.mp3",
  "transcript": "Full transcript text here...",
  "segments": [
    {
      "start": 0,
      "end": 2.5,
      "speaker": "Speaker 1",
      "text": "Hello, how are you?"
    },
    {
      "start": 2.5,
      "end": 5.0,
      "speaker": "Speaker 2",
      "text": "I'm doing well, thank you!"
    }
  ]
}
```

## Testing

Run the test suite:
```bash
npm test
```

Test the API endpoints:
```bash
node test-api.js
```

## Project Structure

```
audio-transcriber/
├── public/                 # Static files
├── src/                   # React frontend source
│   ├── App.js            # Main application component
│   ├── App.test.js       # Component tests
│   └── index.js          # React entry point
├── server/               # Backend server
│   └── server.js         # Express server with API endpoints
├── .env                  # Environment variables
├── package.json          # Dependencies and scripts
└── README.md            # This file
```

## Future Enhancements

- [ ] **Real ASR Integration**: Replace mock transcription with actual Gemini/AssemblyAI API
- [ ] **WebSocket Support**: Real-time progress updates during transcription
- [ ] **Authentication**: User accounts and private file storage
- [ ] **Cloud Storage**: Archive audio files and transcripts
- [ ] **Advanced Diarization**: Custom speaker names and voice recognition
- [ ] **Multiple Languages**: Support for different language transcription
- [ ] **Batch Processing**: Queue management for large file uploads

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Run the test suite
6. Submit a pull request

## License

This project is open source and available under the [MIT License](LICENSE).
