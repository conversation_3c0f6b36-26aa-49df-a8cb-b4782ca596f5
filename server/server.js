const express = require('express');
const multer = require('multer');
const cors = require('cors');
const path = require('path');
const fs = require('fs');
const { GoogleGenerativeAI } = require('@google/generative-ai');
require('dotenv').config();

const app = express();
const PORT = process.env.PORT || 5000;

// Initialize Gemini AI
const genAI = new GoogleGenerativeAI(process.env.GEMINI_API_KEY);

// Middleware
app.use(cors());
app.use(express.json());

// Configure multer for file uploads
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    const uploadDir = 'uploads/';
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }
    cb(null, uploadDir);
  },
  filename: (req, file, cb) => {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    cb(null, file.fieldname + '-' + uniqueSuffix + path.extname(file.originalname));
  }
});

const upload = multer({
  storage: storage,
  fileFilter: (req, file, cb) => {
    // Accept audio files
    const allowedTypes = /\.(mp3|wav|m4a|aac|ogg|flac)$/i;
    if (allowedTypes.test(file.originalname)) {
      cb(null, true);
    } else {
      cb(new Error('Only audio files are allowed!'), false);
    }
  },
  limits: {
    fileSize: 100 * 1024 * 1024 // 100MB limit
  }
});

// Real transcription function using Gemini AI
async function transcribeAudio(filePath, originalName) {
  try {
    console.log(`Starting transcription for: ${originalName}`);

    // Read the audio file
    const audioData = fs.readFileSync(filePath);
    const audioBase64 = audioData.toString('base64');

    // Get the file extension to determine MIME type
    const ext = path.extname(originalName).toLowerCase();
    const mimeTypeMap = {
      '.mp3': 'audio/mp3',
      '.wav': 'audio/wav',
      '.m4a': 'audio/mp4',
      '.aac': 'audio/aac',
      '.ogg': 'audio/ogg',
      '.flac': 'audio/flac'
    };
    const mimeType = mimeTypeMap[ext] || 'audio/mp3';

    // Initialize Gemini model
    const model = genAI.getGenerativeModel({ model: "gemini-2.0-flash-exp" });

    const prompt = `Please transcribe this audio file and provide a detailed transcript with speaker diarization.
    Format the response as a JSON object with the following structure:
    {
      "transcript": "full transcript text",
      "segments": [
        {
          "start": 0,
          "end": 5.2,
          "speaker": "Speaker 1",
          "text": "segment text"
        }
      ]
    }

    Try to identify different speakers and provide timestamps. If you cannot determine exact timestamps, provide reasonable estimates based on the content flow.`;

    const result = await model.generateContent([
      {
        inlineData: {
          data: audioBase64,
          mimeType: mimeType
        }
      },
      prompt
    ]);

    const response = await result.response;
    const text = response.text();

    console.log('Gemini response:', text);

    // Try to parse JSON from the response
    let transcriptData;
    try {
      // Extract JSON from the response (in case it's wrapped in markdown)
      const jsonMatch = text.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        transcriptData = JSON.parse(jsonMatch[0]);
      } else {
        throw new Error('No JSON found in response');
      }
    } catch (parseError) {
      console.log('Failed to parse JSON, creating structured response from text');
      // If JSON parsing fails, create a structured response
      const cleanText = text.replace(/```json|```/g, '').trim();
      transcriptData = {
        transcript: cleanText,
        segments: [
          {
            start: 0,
            end: 10,
            speaker: "Speaker 1",
            text: cleanText
          }
        ]
      };
    }

    // Ensure the response has the correct structure
    const finalTranscript = {
      fileName: originalName,
      transcript: transcriptData.transcript || text,
      segments: transcriptData.segments || [
        {
          start: 0,
          end: 10,
          speaker: "Speaker 1",
          text: transcriptData.transcript || text
        }
      ]
    };

    console.log(`Transcription completed for: ${originalName}`);
    return finalTranscript;

  } catch (error) {
    console.error('Transcription error:', error);

    // Return error response with some basic info
    return {
      fileName: originalName,
      transcript: `Error transcribing audio: ${error.message}`,
      segments: [
        {
          start: 0,
          end: 5,
          speaker: "System",
          text: `Error transcribing audio: ${error.message}`
        }
      ]
    };
  }
}

// Routes
app.post('/api/transcribe', upload.single('audio'), async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({ error: 'No audio file provided' });
    }

    console.log(`Processing file: ${req.file.originalname}`);
    
    // Transcribe the audio file
    const transcript = await transcribeAudio(req.file.path, req.file.originalname);
    
    // Clean up uploaded file
    fs.unlink(req.file.path, (err) => {
      if (err) console.error('Error deleting file:', err);
    });
    
    res.json(transcript);
  } catch (error) {
    console.error('Transcription error:', error);
    res.status(500).json({ error: 'Failed to transcribe audio' });
  }
});

// Health check endpoint
app.get('/api/health', (req, res) => {
  res.json({ status: 'OK', message: 'Audio Transcriber API is running' });
});

// Serve static files in production
if (process.env.NODE_ENV === 'production' && fs.existsSync(path.join(__dirname, '../build'))) {
  app.use(express.static(path.join(__dirname, '../build')));

  app.get('*', (req, res) => {
    res.sendFile(path.join(__dirname, '../build', 'index.html'));
  });
}

app.listen(PORT, () => {
  console.log(`Server running on port ${PORT}`);
  console.log(`Health check: http://localhost:${PORT}/api/health`);
});
