const express = require('express');
const multer = require('multer');
const cors = require('cors');
const path = require('path');
const fs = require('fs');
require('dotenv').config();

const app = express();
const PORT = process.env.PORT || 5000;

// Middleware
app.use(cors());
app.use(express.json());

// Configure multer for file uploads
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    const uploadDir = 'uploads/';
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }
    cb(null, uploadDir);
  },
  filename: (req, file, cb) => {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    cb(null, file.fieldname + '-' + uniqueSuffix + path.extname(file.originalname));
  }
});

const upload = multer({
  storage: storage,
  fileFilter: (req, file, cb) => {
    // Accept audio files
    const allowedTypes = /\.(mp3|wav|m4a|aac|ogg|flac)$/i;
    if (allowedTypes.test(file.originalname)) {
      cb(null, true);
    } else {
      cb(new Error('Only audio files are allowed!'), false);
    }
  },
  limits: {
    fileSize: 100 * 1024 * 1024 // 100MB limit
  }
});

// Mock transcription function (replace with actual ASR provider)
async function transcribeAudio(filePath, originalName) {
  // Simulate processing time
  await new Promise(resolve => setTimeout(resolve, 2000));
  
  // Mock transcript data - replace with actual ASR API call
  const mockTranscript = {
    fileName: originalName,
    transcript: "This is a mock transcript. Hello, how are you today? I'm doing well, thank you for asking.",
    segments: [
      {
        start: 0,
        end: 2.5,
        speaker: "Speaker 1",
        text: "This is a mock transcript."
      },
      {
        start: 2.5,
        end: 5.0,
        speaker: "Speaker 1", 
        text: "Hello, how are you today?"
      },
      {
        start: 5.0,
        end: 8.5,
        speaker: "Speaker 2",
        text: "I'm doing well, thank you for asking."
      }
    ]
  };
  
  return mockTranscript;
}

// Routes
app.post('/api/transcribe', upload.single('audio'), async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({ error: 'No audio file provided' });
    }

    console.log(`Processing file: ${req.file.originalname}`);
    
    // Transcribe the audio file
    const transcript = await transcribeAudio(req.file.path, req.file.originalname);
    
    // Clean up uploaded file
    fs.unlink(req.file.path, (err) => {
      if (err) console.error('Error deleting file:', err);
    });
    
    res.json(transcript);
  } catch (error) {
    console.error('Transcription error:', error);
    res.status(500).json({ error: 'Failed to transcribe audio' });
  }
});

// Health check endpoint
app.get('/api/health', (req, res) => {
  res.json({ status: 'OK', message: 'Audio Transcriber API is running' });
});

// Serve static files in production
if (process.env.NODE_ENV === 'production' && fs.existsSync(path.join(__dirname, '../build'))) {
  app.use(express.static(path.join(__dirname, '../build')));

  app.get('*', (req, res) => {
    res.sendFile(path.join(__dirname, '../build', 'index.html'));
  });
}

app.listen(PORT, () => {
  console.log(`Server running on port ${PORT}`);
  console.log(`Health check: http://localhost:${PORT}/api/health`);
});
