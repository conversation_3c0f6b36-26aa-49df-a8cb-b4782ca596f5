// Simple test script to verify the API endpoints
const fs = require('fs');
const path = require('path');

async function testHealthEndpoint() {
  try {
    const response = await fetch('http://localhost:5000/api/health');
    const data = await response.json();
    console.log('✅ Health endpoint test passed:', data);
    return true;
  } catch (error) {
    console.error('❌ Health endpoint test failed:', error.message);
    return false;
  }
}

async function testTranscribeEndpoint() {
  try {
    // Create a dummy audio file for testing
    const dummyAudioContent = Buffer.from('dummy audio content');
    const formData = new FormData();
    const blob = new Blob([dummyAudioContent], { type: 'audio/mp3' });
    formData.append('audio', blob, 'test.mp3');

    const response = await fetch('http://localhost:5000/api/transcribe', {
      method: 'POST',
      body: formData
    });

    if (response.ok) {
      const data = await response.json();
      console.log('✅ Transcribe endpoint test passed:', data);
      return true;
    } else {
      console.log('⚠️  Transcribe endpoint returned error (expected for dummy file):', response.status);
      return true; // This is expected since we're sending a dummy file
    }
  } catch (error) {
    console.error('❌ Transcribe endpoint test failed:', error.message);
    return false;
  }
}

async function runTests() {
  console.log('🧪 Running API tests...\n');
  
  const healthTest = await testHealthEndpoint();
  const transcribeTest = await testTranscribeEndpoint();
  
  console.log('\n📊 Test Results:');
  console.log(`Health endpoint: ${healthTest ? '✅ PASS' : '❌ FAIL'}`);
  console.log(`Transcribe endpoint: ${transcribeTest ? '✅ PASS' : '❌ FAIL'}`);
  
  if (healthTest && transcribeTest) {
    console.log('\n🎉 All tests passed! The API is working correctly.');
  } else {
    console.log('\n⚠️  Some tests failed. Please check the server logs.');
  }
}

// Only run if this file is executed directly
if (require.main === module) {
  runTests();
}

module.exports = { testHealthEndpoint, testTranscribeEndpoint };
