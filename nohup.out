2025-09-23 09:30:50,374 [    497]   WARN - #c.i.o.v.n.p.FSRecords - Records storage [C:\Users\<USER>\AppData\Local\JetBrains\IdeaIC2025.2\caches\records.dat] was in use by process [OwnershipInfo{owner pid: 41708, acquired at: 1757584871708}] which is not exist now (wasn't closed properly/crashed?) -> re-acquiring forcibly
2025-09-23 09:30:50,394 [    517]   WARN - #c.i.o.v.n.p.PersistentFSLoader - [VFS load problem]: VFS wasn't safely shut down: records.wasClosedProperly is false
2025-09-23 09:30:51,490 [   1613]   WARN - #c.i.o.f.i.FileTypeManagerImpl - 
ua.t3hnar.plugins.cmdsupport.CmdFileType$@14b74d56 from 'PluginMainDescriptor(name=CMD Support, id=CMD Support, version=1.0.5, isBundled=false, path=~\AppData\Roaming\JetBrains\IdeaIC2025.2\plugins\cmdsupport)' (class ua.t3hnar.plugins.cmdsupport.CmdFileType$) and
ua.t3hnar.plugins.cmdsupport.BatFileType$@6ce9fca from 'PluginMainDescriptor(name=CMD Support, id=CMD Support, version=1.0.5, isBundled=false, path=~\AppData\Roaming\JetBrains\IdeaIC2025.2\plugins\cmdsupport)' (class ua.t3hnar.plugins.cmdsupport.BatFileType$)
 both have the same .getDisplayName(): 'Cmd'. Please override either one's getDisplayName() to something unique. [Plugin: CMD Support]
com.intellij.diagnostic.PluginException: 
ua.t3hnar.plugins.cmdsupport.CmdFileType$@14b74d56 from 'PluginMainDescriptor(name=CMD Support, id=CMD Support, version=1.0.5, isBundled=false, path=~\AppData\Roaming\JetBrains\IdeaIC2025.2\plugins\cmdsupport)' (class ua.t3hnar.plugins.cmdsupport.CmdFileType$) and
ua.t3hnar.plugins.cmdsupport.BatFileType$@6ce9fca from 'PluginMainDescriptor(name=CMD Support, id=CMD Support, version=1.0.5, isBundled=false, path=~\AppData\Roaming\JetBrains\IdeaIC2025.2\plugins\cmdsupport)' (class ua.t3hnar.plugins.cmdsupport.BatFileType$)
 both have the same .getDisplayName(): 'Cmd'. Please override either one's getDisplayName() to something unique. [Plugin: CMD Support]
	at com.intellij.openapi.fileTypes.impl.FileTypeManagerImpl.checkUnique(FileTypeManagerImpl.java:1625)
	at com.intellij.openapi.fileTypes.impl.FileTypeManagerImpl.checkUnique(FileTypeManagerImpl.java:1598)
	at com.intellij.openapi.fileTypes.impl.FileTypeManagerImpl.checkUnique(FileTypeManagerImpl.java:1588)
	at com.intellij.openapi.fileTypes.impl.AlarmAdapterKt$singleAlarm$1$1.invokeSuspend(AlarmAdapter.kt:24)
	at com.intellij.openapi.fileTypes.impl.AlarmAdapterKt$singleAlarm$1$1.invoke(AlarmAdapter.kt)
	at com.intellij.openapi.fileTypes.impl.AlarmAdapterKt$singleAlarm$1$1.invoke(AlarmAdapter.kt)
	at kotlinx.coroutines.flow.FlowKt__MergeKt$mapLatest$1.invokeSuspend(Merge.kt:213)
	at kotlinx.coroutines.flow.FlowKt__MergeKt$mapLatest$1.invoke(Merge.kt)
	at kotlinx.coroutines.flow.FlowKt__MergeKt$mapLatest$1.invoke(Merge.kt)
	at kotlinx.coroutines.flow.internal.ChannelFlowTransformLatest$flowCollect$3$1$2.invokeSuspend(Merge.kt:30)
	at kotlinx.coroutines.flow.internal.ChannelFlowTransformLatest$flowCollect$3$1$2.invoke(Merge.kt)
	at kotlinx.coroutines.flow.internal.ChannelFlowTransformLatest$flowCollect$3$1$2.invoke(Merge.kt)
	at kotlinx.coroutines.intrinsics.UndispatchedKt.startCoroutineUndispatched(Undispatched.kt:20)
	at kotlinx.coroutines.CoroutineStart.invoke(CoroutineStart.kt:360)
	at kotlinx.coroutines.AbstractCoroutine.start(AbstractCoroutine.kt:134)
	at kotlinx.coroutines.BuildersKt__Builders_commonKt.launch(Builders.common.kt:52)
	at kotlinx.coroutines.BuildersKt.launch(Unknown Source)
	at kotlinx.coroutines.BuildersKt__Builders_commonKt.launch$default(Builders.common.kt:43)
	at kotlinx.coroutines.BuildersKt.launch$default(Unknown Source)
	at kotlinx.coroutines.flow.internal.ChannelFlowTransformLatest$flowCollect$3$1.emit(Merge.kt:29)
	at kotlinx.coroutines.flow.internal.FlowValueWrapperInternalKt.emitInternal(FlowValueWrapperInternal.kt:39)
	at kotlinx.coroutines.flow.FlowKt__DelayKt$debounceInternal$1$3$1.invokeSuspend(Delay.kt:226)
	at kotlinx.coroutines.flow.FlowKt__DelayKt$debounceInternal$1$3$1.invoke(Delay.kt)
	at kotlinx.coroutines.flow.FlowKt__DelayKt$debounceInternal$1$3$1.invoke(Delay.kt)
	at kotlinx.coroutines.selects.SelectImplementation$ClauseData.invokeBlock(Select.kt:847)
	at kotlinx.coroutines.selects.SelectImplementation$complete$3.invokeSuspend(Select.kt:718)
	at kotlinx.coroutines.selects.SelectImplementation$complete$3.invoke(Select.kt)
	at kotlinx.coroutines.selects.SelectImplementation$complete$3.invoke(Select.kt)
	at kotlinx.coroutines.flow.internal.FlowValueWrapperInternalKt.debuggerCapture(FlowValueWrapperInternal.kt:44)
	at kotlinx.coroutines.selects.SelectImplementation.complete(Select.kt:712)
	at kotlinx.coroutines.selects.SelectImplementation.doSelectSuspend(Select.kt:458)
	at kotlinx.coroutines.selects.SelectImplementation.access$doSelectSuspend(Select.kt:253)
	at kotlinx.coroutines.selects.SelectImplementation$doSelectSuspend$1.invokeSuspend(Select.kt)
	at kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)
	at kotlinx.coroutines.DispatchedTask.run(DispatchedTask.kt:100)
	at kotlinx.coroutines.scheduling.CoroutineScheduler.runSafely(CoroutineScheduler.kt:613)
	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.executeTask(CoroutineScheduler.kt:1189)
	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.runWorker(CoroutineScheduler.kt:778)
	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.run(CoroutineScheduler.kt:765)
2025-09-23 09:30:51,549 [   1672]   WARN - #c.i.o.v.n.p.PersistentFSLoader - [VFS load problem]: NOT_CLOSED_PROPERLY recovered, no problems remain
2025-09-23 09:30:51,623 [   1746]   WARN - #c.i.i.s.p.i.BundledSharedIndexProvider - Bundled shared index is not found at: C:\Program Files\JetBrains\IntelliJ IDEA Community Edition 2025.2.2\jdk-shared-indexes
2025-09-23 09:30:52,077 [   2200]   WARN - #c.i.e.wsl - Failed to get WSL mount root for Ubuntu: DOS working directory is expected, but got \\wsl.localhost\Ubuntu\home\oni\audio-transcriber
2025-09-23 09:30:54,036 [   4159]   WARN - #c.i.s.ComponentManagerImpl - com.augmentcode.intellij.syncing.AugmentRemoteSyncingManagerImpl PluginClassLoader(plugin=PluginMainDescriptor(name=Augment: AI coding assistant for professionals, id=com.augmentcode, version=0.288.0, isBundled=false, path=~\AppData\Roaming\JetBrains\IdeaIC2025.2\plugins\intellij-augment), packagePrefix=null, state=active, parents=ContentModuleDescriptor(moduleName=intellij.platform.vcs.impl) <- PluginMainDescriptor(name=IDEA CORE, id=com.intellij, version=252.26199.169, isBundled=true, path=C:\Program Files\JetBrains\IntelliJ IDEA Community Edition 2025.2.2\lib), ContentModuleDescriptor(moduleName=intellij.platform.vcs.log.impl) <- PluginMainDescriptor(name=IDEA CORE, id=com.intellij, version=252.26199.169, isBundled=true, path=C:\Program Files\JetBrains\IntelliJ IDEA Community Edition 2025.2.2\lib), ContentModuleDescriptor(moduleName=intellij.platform.vcs.dvcs.impl) <- PluginMainDescriptor(name=IDEA CORE, id=com.intellij, version=252.26199.169, isBundled=true, path=C:\Program Files\JetBrains\IntelliJ IDEA Community Edition 2025.2.2\lib), ContentModuleDescriptor(moduleName=intellij.platform.collaborationTools) <- PluginMainDescriptor(name=IDEA CORE, id=com.intellij, version=252.26199.169, isBundled=true, path=C:\Program Files\JetBrains\IntelliJ IDEA Community Edition 2025.2.2\lib), PluginMainDescriptor(name=Terminal, id=org.jetbrains.plugins.terminal, version=252.26199.169, package=org.jetbrains.plugins.terminal, isBundled=true, path=C:\Program Files\JetBrains\IntelliJ IDEA Community Edition 2025.2.2\plugins\terminal), PluginMainDescriptor(name=Shell Script, id=com.jetbrains.sh, version=252.26199.169, package=com.intellij.sh, isBundled=true, path=C:\Program Files\JetBrains\IntelliJ IDEA Community Edition 2025.2.2\plugins\sh), )
java.lang.ClassNotFoundException: com.augmentcode.intellij.syncing.AugmentRemoteSyncingManagerImpl PluginClassLoader(plugin=PluginMainDescriptor(name=Augment: AI coding assistant for professionals, id=com.augmentcode, version=0.288.0, isBundled=false, path=~\AppData\Roaming\JetBrains\IdeaIC2025.2\plugins\intellij-augment), packagePrefix=null, state=active, parents=ContentModuleDescriptor(moduleName=intellij.platform.vcs.impl) <- PluginMainDescriptor(name=IDEA CORE, id=com.intellij, version=252.26199.169, isBundled=true, path=C:\Program Files\JetBrains\IntelliJ IDEA Community Edition 2025.2.2\lib), ContentModuleDescriptor(moduleName=intellij.platform.vcs.log.impl) <- PluginMainDescriptor(name=IDEA CORE, id=com.intellij, version=252.26199.169, isBundled=true, path=C:\Program Files\JetBrains\IntelliJ IDEA Community Edition 2025.2.2\lib), ContentModuleDescriptor(moduleName=intellij.platform.vcs.dvcs.impl) <- PluginMainDescriptor(name=IDEA CORE, id=com.intellij, version=252.26199.169, isBundled=true, path=C:\Program Files\JetBrains\IntelliJ IDEA Community Edition 2025.2.2\lib), ContentModuleDescriptor(moduleName=intellij.platform.collaborationTools) <- PluginMainDescriptor(name=IDEA CORE, id=com.intellij, version=252.26199.169, isBundled=true, path=C:\Program Files\JetBrains\IntelliJ IDEA Community Edition 2025.2.2\lib), PluginMainDescriptor(name=Terminal, id=org.jetbrains.plugins.terminal, version=252.26199.169, package=org.jetbrains.plugins.terminal, isBundled=true, path=C:\Program Files\JetBrains\IntelliJ IDEA Community Edition 2025.2.2\plugins\terminal), PluginMainDescriptor(name=Shell Script, id=com.jetbrains.sh, version=252.26199.169, package=com.intellij.sh, isBundled=true, path=C:\Program Files\JetBrains\IntelliJ IDEA Community Edition 2025.2.2\plugins\sh), )
	at com.intellij.serviceContainer.ComponentManagerImplKt.doLoadClass(ComponentManagerImpl.kt:1433)
	at com.intellij.serviceContainer.ComponentManagerImplKt.doLoadClass$default(ComponentManagerImpl.kt:1429)
	at com.intellij.serviceContainer.ServiceDescriptorInstanceInitializer.loadInstanceClass(ServiceInstanceInitializer.kt:97)
	at com.intellij.platform.instanceContainer.internal.LazyInstanceHolder.instanceClass(LazyInstanceHolder.kt:55)
	at com.intellij.serviceContainer.ComponentManagerImpl.processAllHolders$process(ComponentManagerImpl.kt:1245)
	at com.intellij.serviceContainer.ComponentManagerImpl.processAllHolders(ComponentManagerImpl.kt:1264)
	at com.intellij.openapi.project.impl.DefaultProject.processAllHolders(DefaultProject.kt:193)
	at com.intellij.configurationStore.DefaultProjectElementNormalizerKt.moveComponentConfiguration(defaultProjectElementNormalizer.kt:125)
	at com.intellij.configurationStore.DefaultProjectElementNormalizerKt.normalizeDefaultProjectElement(defaultProjectElementNormalizer.kt:60)
	at com.intellij.configurationStore.ProjectStoreImpl.loadProjectFromTemplate(ProjectStoreImpl.kt:190)
	at com.intellij.configurationStore.ProjectStoreImpl.setPath(ProjectStoreImpl.kt:158)
	at com.intellij.openapi.project.impl.ProjectManagerImplKt.initProject(ProjectManagerImpl.kt:1329)
	at com.intellij.openapi.project.impl.ProjectManagerImplKt.access$initProject(ProjectManagerImpl.kt:1)
	at com.intellij.openapi.project.impl.ProjectManagerImplKt$initProject$1.invokeSuspend(ProjectManagerImpl.kt)
	at kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)
	at kotlinx.coroutines.UndispatchedCoroutine.afterResume(CoroutineContext.kt:277)
	at kotlinx.coroutines.AbstractCoroutine.resumeWith(AbstractCoroutine.kt:101)
	at kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:46)
	at kotlinx.coroutines.UndispatchedCoroutine.afterResume(CoroutineContext.kt:277)
	at kotlinx.coroutines.AbstractCoroutine.resumeWith(AbstractCoroutine.kt:101)
	at kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:46)
	at kotlinx.coroutines.DispatchedTask.run(DispatchedTask.kt:100)
	at kotlinx.coroutines.scheduling.CoroutineScheduler.runSafely(CoroutineScheduler.kt:613)
	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.executeTask(CoroutineScheduler.kt:1189)
	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.runWorker(CoroutineScheduler.kt:778)
	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.run(CoroutineScheduler.kt:765)
SLF4J: Failed to load class "org.slf4j.impl.StaticLoggerBinder".
SLF4J: Defaulting to no-operation (NOP) logger implementation
SLF4J: See http://www.slf4j.org/codes.html#StaticLoggerBinder for further details.
2025-09-23 09:30:56,052 [   6175]   WARN - #c.i.s.c.i.StationSocketConnectionLoop - RecentProjects connection failed with AnnotatedConnectException(ConnectException) (Connection refused: connect: C:\Users\<USER>\AppData\Local\Temp\jb.station.Onesmus.sock)
2025-09-23 09:30:56,112 [   6235]   WARN - #c.i.s.c.i.StationSocketConnectionLoop - Discovery connection failed with AnnotatedConnectException(ConnectException) (Connection refused: connect: C:\Users\<USER>\AppData\Local\Temp\jb.station.Onesmus.sock)
JCEF_V(30:56:563): Found cef_server binary 'C:\Program Files\JetBrains\IntelliJ IDEA Community Edition 2025.2.2\jbr\bin\cef_server.exe' via System.getProperty('java.home')=C:\Program Files\JetBrains\IntelliJ IDEA Community Edition 2025.2.2\jbr
JCEF_V(30:56:564): Java is started via native launcher. Found cef_server path C:\Program Files\JetBrains\IntelliJ IDEA Community Edition 2025.2.2\jbr\bin\cef_server.exe (via system propety)
2025-09-23 09:30:56,991 [   7114]   WARN - #c.i.u.i.FileBasedIndexImpl - Suppressed a frequent exception logged for the 2nd time: null
2025-09-23 09:30:57,523 [   7646]   WARN - #com.augmentcode.intellij.sentry.SentryMetadataCollector - Git ls-files command failed with exit code: 128
2025-09-23 09:30:58,147 [   8270]   WARN - #c.i.e.wsl - Failed to get WSL mount root for Ubuntu: DOS working directory is expected, but got \\wsl.localhost\Ubuntu\home\oni\audio-transcriber
JCEF_I(30:59:574): Found free tcp-port 6188 for server.
JCEF_I(30:59:574): Found free tcp-port 6189 for java-handlers.
JCEF_V(30:59:593): Created CefServer instance. Transport port=6188 (backward port=6189). Params:
[--disable-features=SpareRendererForSitePerProcess, --disable-gpu-process-crash-limit, --disable-component-update, --autoplay-policy=no-user-gesture-required]| log_file=C:\Users\<USER>\AppData\Local\JetBrains\IdeaIC2025.2\log\jcef_28448.log, log_severity=LOGSEVERITY_DISABLE
2025-09-23 09:31:03,369 [  13492]   WARN - #c.i.u.j.JBCefBrowserBuilder - Trying to create windowed browser when remote-mode is enabled. Settings isOffScreenRendering=false will be ignored.
2025-09-23 09:31:04,919 [  15042]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_ERROR]: Failed to restore state SyntaxError: Unexpected end of JSON input [http://augment.localhost/main-panel.html:17]
2025-09-23 09:31:05,239 [  15362]   WARN - #org.intellij.plugins.markdown.ui.preview.jcef.impl.JcefBrowserPipeImpl - No subscribers for documentReady!
Attached data: 
2025-09-23 09:31:06,409 [  16532]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: The PerformanceObserver does not support buffered flag with the entryTypes argument. [http://augment.localhost/assets/initialize-DVX7iRDU.js:15]
2025-09-23 09:31:06,560 [  16683]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_ERROR]: Host not initialized [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:31:06,646 [  16769]   WARN - #com.augmentcode.intellij.sidecar.SidecarService - Using first project base directory as workspace root from all candidates: [file:////wsl.localhost/Ubuntu/home/<USER>/audio-transcriber]
2025-09-23 09:31:06,649 [  16772]   WARN - #com.augmentcode.intellij.webviews.AbstractWebviewMessagingService - Failed to parse webview message as protobuf.
Exception: Missing type url when parsing: {"type":"get-remote-agent-notification-enabled-request","data":{"agentIds":[]}}
Message: {"type":"async-wrapper","requestId":"17675b96-d853-4269-95ff-3e55e875f11f","error":null,"baseMsg":{"type":"get-remote-agent-notification-enabled-request","data":{"agentIds":[]}},"destination":"host"}
2025-09-23 09:31:06,649 [  16772]   WARN - #com.augmentcode.intellij.webviews.AbstractWebviewMessagingService - Failed to parse webview message as protobuf.
Exception: Missing type url when parsing: {"type":"get-remote-agent-pinned-status-request","data":{}}
Message: {"type":"async-wrapper","requestId":"48279a48-6ee1-48e7-b8f1-37b7dacdf2cd","error":null,"baseMsg":{"type":"get-remote-agent-pinned-status-request","data":{}},"destination":"host"}
2025-09-23 09:31:06,650 [  16773]   WARN - #com.augmentcode.intellij.webviews.AbstractWebviewMessagingService - Failed to parse webview message as protobuf.
Exception: Missing type url when parsing: {"type":"get-remote-agent-status"}
Message: {"type":"async-wrapper","requestId":"513d7d88-3d8f-469e-b1a1-5848af38f1bf","error":null,"baseMsg":{"type":"get-remote-agent-status"},"destination":"host"}
2025-09-23 09:31:06,663 [  16786]   WARN - #com.augmentcode.intellij.sidecar.SidecarService - Using first project base directory as workspace root from all candidates: [file:////wsl.localhost/Ubuntu/home/<USER>/audio-transcriber]
2025-09-23 09:31:06,722 [  16845]   WARN - #com.augmentcode.intellij.webviews.AbstractWebviewMessagingService - Failed to parse webview message as protobuf.
Exception: Missing type url when parsing: {"type":"get-orientation-status"}
Message: {"type":"get-orientation-status"}
2025-09-23 09:31:06,759 [  16882]   WARN - #com.augmentcode.intellij.webviews.AbstractWebviewMessagingService - Failed to parse webview message as protobuf.
Exception: Missing type url when parsing: {"type":"update-shared-webview-state","data":{"agentOverviews":[],"activeWebviews":[],"pinnedAgents":{}},"id":"remoteAgentStore"}
Message: {"type":"update-shared-webview-state","data":{"agentOverviews":[],"activeWebviews":[],"pinnedAgents":{}},"id":"remoteAgentStore"}
2025-09-23 09:31:06,780 [  16903]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_ERROR]: Failed to get pinned agents from store: Error: Unable to parse webview message: {"type":"async-wrapper","requestId":"48279a48-6ee1-48e7-b8f1-37b7dacdf2cd","error":null,"baseMsg":{"type":"get-remote-agent-pinned-status-request","data":{}},"destination":"host"} [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:31:06,781 [  16904]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_ERROR]: Uncaught (in promise) Error: Unable to parse webview message: {"type":"async-wrapper","requestId":"17675b96-d853-4269-95ff-3e55e875f11f","error":null,"baseMsg":{"type":"get-remote-agent-notification-enabled-request","data":{"agentIds":[]}},"destination":"host"} [http://augment.localhost/assets/GuardedIcon-CE9Pu5ez.js:7]
2025-09-23 09:31:06,781 [  16904]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_ERROR]: Uncaught (in promise) Error: Unable to parse webview message: {"type":"async-wrapper","requestId":"513d7d88-3d8f-469e-b1a1-5848af38f1bf","error":null,"baseMsg":{"type":"get-remote-agent-status"},"destination":"host"} [http://augment.localhost/assets/GuardedIcon-CE9Pu5ez.js:7]
2025-09-23 09:31:07,031 [  17154]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: IntersectionObserverManager: disabled [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:31:13,070 [  23193]   WARN - #c.i.e.wsl - Failed to get WSL mount root for Ubuntu: DOS working directory is expected, but got \\wsl.localhost\Ubuntu\home\oni\audio-transcriber
2025-09-23 09:31:23,121 [  33244]   WARN - #c.i.e.wsl - Failed to get WSL mount root for Ubuntu: DOS working directory is expected, but got \\wsl.localhost\Ubuntu\home\oni\audio-transcriber
2025-09-23 09:31:30,559 [  40682]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_ERROR]: ResizeObserver loop completed with undelivered notifications. [http://augment.localhost/main-panel.html:0]
2025-09-23 09:31:32,571 [  42694]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: IntersectionObserverManager: disabled [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:31:34,631 [  44754]   WARN - #com.augmentcode.intellij.chat.ChatWebviewMessageClient$Companion - Workspace guidelines are empty, not including in chat request. 
2025-09-23 09:31:36,019 [  46142]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: No exchange with this request ID found. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:31:36,083 [  46206]   WARN - #com.augmentcode.intellij.chat.ChatWebviewMessageClient$Companion - Workspace guidelines are empty, not including in chat request. 
2025-09-23 09:31:39,269 [  49392]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: No exchange with this request ID found. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:31:42,599 [  52722]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 3160a1d4e3f0ccc1fa8b2779ffe879b3616b82386c8ed6e958b30b7046cbc3a3 -> d04d206b96ab69c473234440e5cd420d9d9ef834642b88aae161c4862bcf5e6f
2025-09-23 09:31:44,189 [  54312]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: IntersectionDisplay context not found.  Call setIntersectionContext first. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:31:45,881 [  56004]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: IntersectionDisplay context not found.  Call setIntersectionContext first. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:31:48,390 [  58513]   WARN - #com.augmentcode.intellij.chat.ChatWebviewMessageClient$Companion - Workspace guidelines are empty, not including in chat request. 
2025-09-23 09:31:49,076 [  59199]   WARN - #kotlinx.coroutines.CoroutineScope - Sending initial guidelines state on EDT from dumb service runWhenSmart
2025-09-23 09:31:51,441 [  61564]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: No exchange with this request ID found. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:31:52,787 [  62910]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: IntersectionDisplay context not found.  Call setIntersectionContext first. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:31:52,821 [  62944]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: IntersectionDisplay context not found.  Call setIntersectionContext first. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:31:52,863 [  62986]   WARN - #com.augmentcode.intellij.chat.ChatWebviewMessageClient$Companion - Workspace guidelines are empty, not including in chat request. 
2025-09-23 09:31:56,858 [  66981]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: No exchange with this request ID found. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:31:57,719 [  67842]   WARN - #com.augmentcode.intellij.sentry.SentryMetadataCollector - Git ls-files command failed with exit code: 128
2025-09-23 09:32:07,252 [  77375]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: IntersectionDisplay context not found.  Call setIntersectionContext first. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:32:07,483 [  77606]   WARN - #com.augmentcode.intellij.chat.ChatWebviewMessageClient$Companion - Workspace guidelines are empty, not including in chat request. 
2025-09-23 09:32:11,827 [  81950]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: No exchange with this request ID found. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:32:13,486 [  83609]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: IntersectionDisplay context not found.  Call setIntersectionContext first. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:32:13,591 [  83714]   WARN - #com.augmentcode.intellij.chat.ChatWebviewMessageClient$Companion - Workspace guidelines are empty, not including in chat request. 
2025-09-23 09:32:17,312 [  87435]   WARN - #com.augmentcode.intellij.api.AugmentHttpClient$Companion - Failed to call record-session-events (4ebd07bc-a46b-4e21-bb14-82f9dd2e2175): 400 Bad Request
2025-09-23 09:32:17,313 [  87436]   WARN - #com.augmentcode.intellij.api.AugmentAPI$Companion - Failed to record session events: 400 Bad Request
2025-09-23 09:32:18,292 [  88415]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: No exchange with this request ID found. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:32:20,711 [  90834]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: IntersectionDisplay context not found.  Call setIntersectionContext first. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:32:21,004 [  91127]   WARN - #com.augmentcode.intellij.webviews.AbstractWebviewMessagingService - Failed to parse webview message as protobuf.
Exception: Missing type url when parsing: {"type":"can-show-terminal","data":{"command":"npm install express multer cors dotenv"}}
Message: {"type":"async-wrapper","requestId":"8b1e52ea-bf8e-48be-9534-23bad1f9fab0","error":null,"baseMsg":{"type":"can-show-terminal","data":{"command":"npm install express multer cors dotenv"}},"destination":"host"}
2025-09-23 09:32:21,008 [  91131]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_ERROR]: Failed to check if terminal can be shown: Error: Unable to parse webview message: {"type":"async-wrapper","requestId":"8b1e52ea-bf8e-48be-9534-23bad1f9fab0","error":null,"baseMsg":{"type":"can-show-terminal","data":{"command":"npm install express multer cors dotenv"}},"destination":"host"} [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:32:27,745 [  97868]   WARN - #c.i.e.wsl - Failed to get WSL mount root for Ubuntu: DOS working directory is expected, but got \\wsl.localhost\Ubuntu\home\oni\audio-transcriber
2025-09-23 09:32:47,702 [ 117825]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Non of the blobs were indexed, backing off and retrying...
2025-09-23 09:32:57,891 [ 128014]   WARN - #com.augmentcode.intellij.sentry.SentryMetadataCollector - Git ls-files command failed with exit code: 128
2025-09-23 09:32:59,962 [ 130085]   WARN - #com.augmentcode.intellij.webviews.AbstractWebviewMessagingService - Failed to parse webview message as protobuf.
Exception: Missing type url when parsing: {"type":"can-show-terminal","data":{"command":"npm install express multer cors dotenv"}}
Message: {"type":"async-wrapper","requestId":"0e0abbec-bf6f-4363-95d2-1a7b73e876a3","error":null,"baseMsg":{"type":"can-show-terminal","data":{"command":"npm install express multer cors dotenv"}},"destination":"host"}
2025-09-23 09:32:59,982 [ 130105]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_ERROR]: Failed to check if terminal can be shown: Error: Unable to parse webview message: {"type":"async-wrapper","requestId":"0e0abbec-bf6f-4363-95d2-1a7b73e876a3","error":null,"baseMsg":{"type":"can-show-terminal","data":{"command":"npm install express multer cors dotenv"}},"destination":"host"} [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:33:00,058 [ 130181] SEVERE - #c.i.o.progress - This method is forbidden on EDT because it does not pump the event queue. Switch to a BGT, or use com.intellij.openapi.progress.TasksKt.runWithModalProgressBlocking. 
java.lang.IllegalStateException: This method is forbidden on EDT because it does not pump the event queue. Switch to a BGT, or use com.intellij.openapi.progress.TasksKt.runWithModalProgressBlocking. 
	at com.intellij.openapi.progress.CoroutinesKt.assertBackgroundThreadAndNoWriteAction(coroutines.kt:577)
	at com.intellij.openapi.progress.CoroutinesKt.runBlockingCancellable(coroutines.kt:130)
	at com.intellij.openapi.progress.CoroutinesKt.runBlockingMaybeCancellable(coroutines.kt:180)
	at com.intellij.platform.eel.provider.EelProviderUtil.toEelApiBlocking(EelProvider.kt:94)
	at com.intellij.sh.run.ShConfigurationType.trivialDefaultShellDetection(ShConfigurationType.java:66)
	at com.intellij.sh.run.ShConfigurationType.getDefaultShell(ShConfigurationType.java:61)
	at com.intellij.sh.run.ShRunConfigurationProfileState.createCommandLineForScript(ShRunConfigurationProfileState.java:97)
	at com.intellij.sh.run.ShRunConfigurationProfileState.buildExecutionResult(ShRunConfigurationProfileState.java:73)
	at com.intellij.sh.run.ShRunConfigurationProfileState.execute(ShRunConfigurationProfileState.java:59)
	at com.intellij.sh.run.ShRunProgramRunner.lambda$execute$0(ShRunProgramRunner.java:33)
	at com.intellij.execution.ExecutionManager.startRunProfile$lambda$0(ExecutionManager.kt:75)
	at com.intellij.execution.impl.ExecutionManagerImpl.startRunProfile$lambda$6(ExecutionManagerImpl.kt:229)
	at com.intellij.execution.impl.ExecutionManagerImpl.doStartRunProfile$lambda$15(ExecutionManagerImpl.kt:296)
	at com.intellij.openapi.application.TransactionGuardImpl.runWithWritingAllowed(TransactionGuardImpl.java:240)
	at com.intellij.openapi.application.TransactionGuardImpl.access$100(TransactionGuardImpl.java:26)
	at com.intellij.openapi.application.TransactionGuardImpl$1.run(TransactionGuardImpl.java:202)
	at com.intellij.openapi.application.impl.AppImplKt$runnableUnitFunction$1.invoke(appImpl.kt:104)
	at com.intellij.openapi.application.impl.AppImplKt$runnableUnitFunction$1.invoke(appImpl.kt:104)
	at com.intellij.platform.locking.impl.NestedLocksThreadingSupport.doRunWriteIntentReadAction(NestedLocksThreadingSupport.kt:666)
	at com.intellij.platform.locking.impl.NestedLocksThreadingSupport.runPreventiveWriteIntentReadAction(NestedLocksThreadingSupport.kt:640)
	at com.intellij.platform.locking.impl.NestedLocksThreadingSupport.runWriteIntentReadAction(NestedLocksThreadingSupport.kt:633)
	at com.intellij.openapi.application.impl.ApplicationImpl.runIntendedWriteActionOnCurrentThread(ApplicationImpl.java:1022)
	at com.intellij.openapi.application.impl.ApplicationImpl$6.run(ApplicationImpl.java:574)
	at com.intellij.util.concurrency.ChildContext$runInChildContext$1.invoke(propagation.kt:167)
	at com.intellij.util.concurrency.ChildContext$runInChildContext$1.invoke(propagation.kt:167)
	at com.intellij.util.concurrency.ChildContext.runInChildContext(propagation.kt:173)
	at com.intellij.util.concurrency.ChildContext.runInChildContext(propagation.kt:167)
	at com.intellij.util.concurrency.ContextRunnable.run(ContextRunnable.java:27)
	at com.intellij.openapi.application.impl.FlushQueue.runNextEvent(FlushQueue.java:122)
	at com.intellij.openapi.application.impl.FlushQueue.flushNow(FlushQueue.java:43)
	at java.desktop/java.awt.event.InvocationEvent.dispatch(InvocationEvent.java:318)
	at java.desktop/java.awt.EventQueue.dispatchEventImpl(EventQueue.java:781)
	at java.desktop/java.awt.EventQueue$4.run(EventQueue.java:728)
	at java.desktop/java.awt.EventQueue$4.run(EventQueue.java:722)
	at java.base/java.security.AccessController.doPrivileged(AccessController.java:400)
	at java.base/java.security.ProtectionDomain$JavaSecurityAccessImpl.doIntersectionPrivilege(ProtectionDomain.java:87)
	at java.desktop/java.awt.EventQueue.dispatchEvent(EventQueue.java:750)
	at com.intellij.ide.IdeEventQueue.defaultDispatchEvent(IdeEventQueue.kt:595)
	at com.intellij.ide.IdeEventQueue._dispatchEvent(IdeEventQueue.kt:488)
	at com.intellij.ide.IdeEventQueue.dispatchEvent$lambda$12$lambda$11$lambda$10$lambda$9(IdeEventQueue.kt:313)
	at com.intellij.openapi.progress.impl.CoreProgressManager.computePrioritized(CoreProgressManager.java:865)
	at com.intellij.ide.IdeEventQueue.dispatchEvent$lambda$12$lambda$11$lambda$10(IdeEventQueue.kt:312)
	at com.intellij.ide.IdeEventQueueKt.performActivity$lambda$3(IdeEventQueue.kt:974)
	at com.intellij.openapi.application.TransactionGuardImpl.performActivity(TransactionGuardImpl.java:110)
	at com.intellij.ide.IdeEventQueueKt.performActivity(IdeEventQueue.kt:974)
	at com.intellij.ide.IdeEventQueue.dispatchEvent$lambda$12(IdeEventQueue.kt:307)
	at com.intellij.ide.IdeEventQueue.dispatchEvent(IdeEventQueue.kt:347)
	at java.desktop/java.awt.EventDispatchThread.pumpOneEventForFilters(EventDispatchThread.java:207)
	at java.desktop/java.awt.EventDispatchThread.pumpEventsForFilter(EventDispatchThread.java:128)
	at java.desktop/java.awt.EventDispatchThread.pumpEventsForHierarchy(EventDispatchThread.java:117)
	at java.desktop/java.awt.EventDispatchThread.pumpEvents(EventDispatchThread.java:113)
	at java.desktop/java.awt.EventDispatchThread.pumpEvents(EventDispatchThread.java:105)
	at java.desktop/java.awt.EventDispatchThread.run(EventDispatchThread.java:92)
2025-09-23 09:33:00,059 [ 130182] SEVERE - #c.i.o.progress - IntelliJ IDEA 2025.2.2  Build #IC-252.26199.169
2025-09-23 09:33:00,059 [ 130182] SEVERE - #c.i.o.progress - JDK: 21.0.8; VM: OpenJDK 64-Bit Server VM; Vendor: JetBrains s.r.o.
2025-09-23 09:33:00,059 [ 130182] SEVERE - #c.i.o.progress - OS: Windows 11
2025-09-23 09:33:00,059 [ 130182] SEVERE - #c.i.o.progress - Plugin to blame: Shell Script version: 252.26199.169
2025-09-23 09:33:00,061 [ 130184] SEVERE - #c.i.o.progress - This method is forbidden on EDT because it does not pump the event queue. Switch to a BGT, or use com.intellij.openapi.progress.TasksKt.runWithModalProgressBlocking. 
java.lang.IllegalStateException: This method is forbidden on EDT because it does not pump the event queue. Switch to a BGT, or use com.intellij.openapi.progress.TasksKt.runWithModalProgressBlocking. 
	at com.intellij.openapi.progress.CoroutinesKt.assertBackgroundThreadAndNoWriteAction(coroutines.kt:577)
	at com.intellij.openapi.progress.CoroutinesKt.runBlockingCancellable(coroutines.kt:130)
	at com.intellij.openapi.progress.CoroutinesKt.runBlockingMaybeCancellable(coroutines.kt:180)
	at com.intellij.platform.eel.provider.utils.EelUtilsKt.fetchLoginShellEnvVariablesBlocking(eelUtils.kt:20)
	at com.intellij.sh.run.ShConfigurationType.trivialDefaultShellDetection(ShConfigurationType.java:67)
	at com.intellij.sh.run.ShConfigurationType.getDefaultShell(ShConfigurationType.java:61)
	at com.intellij.sh.run.ShRunConfigurationProfileState.createCommandLineForScript(ShRunConfigurationProfileState.java:97)
	at com.intellij.sh.run.ShRunConfigurationProfileState.buildExecutionResult(ShRunConfigurationProfileState.java:73)
	at com.intellij.sh.run.ShRunConfigurationProfileState.execute(ShRunConfigurationProfileState.java:59)
	at com.intellij.sh.run.ShRunProgramRunner.lambda$execute$0(ShRunProgramRunner.java:33)
	at com.intellij.execution.ExecutionManager.startRunProfile$lambda$0(ExecutionManager.kt:75)
	at com.intellij.execution.impl.ExecutionManagerImpl.startRunProfile$lambda$6(ExecutionManagerImpl.kt:229)
	at com.intellij.execution.impl.ExecutionManagerImpl.doStartRunProfile$lambda$15(ExecutionManagerImpl.kt:296)
	at com.intellij.openapi.application.TransactionGuardImpl.runWithWritingAllowed(TransactionGuardImpl.java:240)
	at com.intellij.openapi.application.TransactionGuardImpl.access$100(TransactionGuardImpl.java:26)
	at com.intellij.openapi.application.TransactionGuardImpl$1.run(TransactionGuardImpl.java:202)
	at com.intellij.openapi.application.impl.AppImplKt$runnableUnitFunction$1.invoke(appImpl.kt:104)
	at com.intellij.openapi.application.impl.AppImplKt$runnableUnitFunction$1.invoke(appImpl.kt:104)
	at com.intellij.platform.locking.impl.NestedLocksThreadingSupport.doRunWriteIntentReadAction(NestedLocksThreadingSupport.kt:666)
	at com.intellij.platform.locking.impl.NestedLocksThreadingSupport.runPreventiveWriteIntentReadAction(NestedLocksThreadingSupport.kt:640)
	at com.intellij.platform.locking.impl.NestedLocksThreadingSupport.runWriteIntentReadAction(NestedLocksThreadingSupport.kt:633)
	at com.intellij.openapi.application.impl.ApplicationImpl.runIntendedWriteActionOnCurrentThread(ApplicationImpl.java:1022)
	at com.intellij.openapi.application.impl.ApplicationImpl$6.run(ApplicationImpl.java:574)
	at com.intellij.util.concurrency.ChildContext$runInChildContext$1.invoke(propagation.kt:167)
	at com.intellij.util.concurrency.ChildContext$runInChildContext$1.invoke(propagation.kt:167)
	at com.intellij.util.concurrency.ChildContext.runInChildContext(propagation.kt:173)
	at com.intellij.util.concurrency.ChildContext.runInChildContext(propagation.kt:167)
	at com.intellij.util.concurrency.ContextRunnable.run(ContextRunnable.java:27)
	at com.intellij.openapi.application.impl.FlushQueue.runNextEvent(FlushQueue.java:122)
	at com.intellij.openapi.application.impl.FlushQueue.flushNow(FlushQueue.java:43)
	at java.desktop/java.awt.event.InvocationEvent.dispatch(InvocationEvent.java:318)
	at java.desktop/java.awt.EventQueue.dispatchEventImpl(EventQueue.java:781)
	at java.desktop/java.awt.EventQueue$4.run(EventQueue.java:728)
	at java.desktop/java.awt.EventQueue$4.run(EventQueue.java:722)
	at java.base/java.security.AccessController.doPrivileged(AccessController.java:400)
	at java.base/java.security.ProtectionDomain$JavaSecurityAccessImpl.doIntersectionPrivilege(ProtectionDomain.java:87)
	at java.desktop/java.awt.EventQueue.dispatchEvent(EventQueue.java:750)
	at com.intellij.ide.IdeEventQueue.defaultDispatchEvent(IdeEventQueue.kt:595)
	at com.intellij.ide.IdeEventQueue._dispatchEvent(IdeEventQueue.kt:488)
	at com.intellij.ide.IdeEventQueue.dispatchEvent$lambda$12$lambda$11$lambda$10$lambda$9(IdeEventQueue.kt:313)
	at com.intellij.openapi.progress.impl.CoreProgressManager.computePrioritized(CoreProgressManager.java:865)
	at com.intellij.ide.IdeEventQueue.dispatchEvent$lambda$12$lambda$11$lambda$10(IdeEventQueue.kt:312)
	at com.intellij.ide.IdeEventQueueKt.performActivity$lambda$3(IdeEventQueue.kt:974)
	at com.intellij.openapi.application.TransactionGuardImpl.performActivity(TransactionGuardImpl.java:110)
	at com.intellij.ide.IdeEventQueueKt.performActivity(IdeEventQueue.kt:974)
	at com.intellij.ide.IdeEventQueue.dispatchEvent$lambda$12(IdeEventQueue.kt:307)
	at com.intellij.ide.IdeEventQueue.dispatchEvent(IdeEventQueue.kt:347)
	at java.desktop/java.awt.EventDispatchThread.pumpOneEventForFilters(EventDispatchThread.java:207)
	at java.desktop/java.awt.EventDispatchThread.pumpEventsForFilter(EventDispatchThread.java:128)
	at java.desktop/java.awt.EventDispatchThread.pumpEventsForHierarchy(EventDispatchThread.java:117)
	at java.desktop/java.awt.EventDispatchThread.pumpEvents(EventDispatchThread.java:113)
	at java.desktop/java.awt.EventDispatchThread.pumpEvents(EventDispatchThread.java:105)
	at java.desktop/java.awt.EventDispatchThread.run(EventDispatchThread.java:92)
2025-09-23 09:33:00,062 [ 130185] SEVERE - #c.i.o.progress - IntelliJ IDEA 2025.2.2  Build #IC-252.26199.169
2025-09-23 09:33:00,062 [ 130185] SEVERE - #c.i.o.progress - JDK: 21.0.8; VM: OpenJDK 64-Bit Server VM; Vendor: JetBrains s.r.o.
2025-09-23 09:33:00,064 [ 130187] SEVERE - #c.i.o.progress - OS: Windows 11
2025-09-23 09:33:00,064 [ 130187] SEVERE - #c.i.o.progress - Plugin to blame: Shell Script version: 252.26199.169
2025-09-23 09:33:00,068 [ 130191] SEVERE - #c.i.o.progress - This method is forbidden on EDT because it does not pump the event queue. Switch to a BGT, or use com.intellij.openapi.progress.TasksKt.runWithModalProgressBlocking. 
java.lang.IllegalStateException: This method is forbidden on EDT because it does not pump the event queue. Switch to a BGT, or use com.intellij.openapi.progress.TasksKt.runWithModalProgressBlocking. 
	at com.intellij.openapi.progress.CoroutinesKt.assertBackgroundThreadAndNoWriteAction(coroutines.kt:577)
	at com.intellij.openapi.progress.CoroutinesKt.runBlockingCancellable(coroutines.kt:130)
	at com.intellij.openapi.progress.CoroutinesKt.runBlockingMaybeCancellable(coroutines.kt:180)
	at com.intellij.platform.eel.provider.EelProviderUtil.toEelApiBlocking(EelProvider.kt:94)
	at com.intellij.execution.configurations.GeneralCommandLine.tryGetEel(GeneralCommandLine.java:493)
	at com.intellij.execution.configurations.GeneralCommandLine.startProcess(GeneralCommandLine.java:429)
	at com.intellij.execution.configurations.GeneralCommandLine.createProcess(GeneralCommandLine.java:402)
	at com.intellij.execution.process.OSProcessHandler.startProcess(OSProcessHandler.java:85)
	at com.intellij.execution.process.OSProcessHandler.<init>(OSProcessHandler.java:45)
	at com.intellij.execution.process.KillableProcessHandler.<init>(KillableProcessHandler.java:40)
	at com.intellij.sh.run.ShRunConfigurationProfileState$1.<init>(ShRunConfigurationProfileState.java:83)
	at com.intellij.sh.run.ShRunConfigurationProfileState.createProcessHandler(ShRunConfigurationProfileState.java:83)
	at com.intellij.sh.run.ShRunConfigurationProfileState.buildExecutionResult(ShRunConfigurationProfileState.java:75)
	at com.intellij.sh.run.ShRunConfigurationProfileState.execute(ShRunConfigurationProfileState.java:59)
	at com.intellij.sh.run.ShRunProgramRunner.lambda$execute$0(ShRunProgramRunner.java:33)
	at com.intellij.execution.ExecutionManager.startRunProfile$lambda$0(ExecutionManager.kt:75)
	at com.intellij.execution.impl.ExecutionManagerImpl.startRunProfile$lambda$6(ExecutionManagerImpl.kt:229)
	at com.intellij.execution.impl.ExecutionManagerImpl.doStartRunProfile$lambda$15(ExecutionManagerImpl.kt:296)
	at com.intellij.openapi.application.TransactionGuardImpl.runWithWritingAllowed(TransactionGuardImpl.java:240)
	at com.intellij.openapi.application.TransactionGuardImpl.access$100(TransactionGuardImpl.java:26)
	at com.intellij.openapi.application.TransactionGuardImpl$1.run(TransactionGuardImpl.java:202)
	at com.intellij.openapi.application.impl.AppImplKt$runnableUnitFunction$1.invoke(appImpl.kt:104)
	at com.intellij.openapi.application.impl.AppImplKt$runnableUnitFunction$1.invoke(appImpl.kt:104)
	at com.intellij.platform.locking.impl.NestedLocksThreadingSupport.doRunWriteIntentReadAction(NestedLocksThreadingSupport.kt:666)
	at com.intellij.platform.locking.impl.NestedLocksThreadingSupport.runPreventiveWriteIntentReadAction(NestedLocksThreadingSupport.kt:640)
	at com.intellij.platform.locking.impl.NestedLocksThreadingSupport.runWriteIntentReadAction(NestedLocksThreadingSupport.kt:633)
	at com.intellij.openapi.application.impl.ApplicationImpl.runIntendedWriteActionOnCurrentThread(ApplicationImpl.java:1022)
	at com.intellij.openapi.application.impl.ApplicationImpl$6.run(ApplicationImpl.java:574)
	at com.intellij.util.concurrency.ChildContext$runInChildContext$1.invoke(propagation.kt:167)
	at com.intellij.util.concurrency.ChildContext$runInChildContext$1.invoke(propagation.kt:167)
	at com.intellij.util.concurrency.ChildContext.runInChildContext(propagation.kt:173)
	at com.intellij.util.concurrency.ChildContext.runInChildContext(propagation.kt:167)
	at com.intellij.util.concurrency.ContextRunnable.run(ContextRunnable.java:27)
	at com.intellij.openapi.application.impl.FlushQueue.runNextEvent(FlushQueue.java:122)
	at com.intellij.openapi.application.impl.FlushQueue.flushNow(FlushQueue.java:43)
	at java.desktop/java.awt.event.InvocationEvent.dispatch(InvocationEvent.java:318)
	at java.desktop/java.awt.EventQueue.dispatchEventImpl(EventQueue.java:781)
	at java.desktop/java.awt.EventQueue$4.run(EventQueue.java:728)
	at java.desktop/java.awt.EventQueue$4.run(EventQueue.java:722)
	at java.base/java.security.AccessController.doPrivileged(AccessController.java:400)
	at java.base/java.security.ProtectionDomain$JavaSecurityAccessImpl.doIntersectionPrivilege(ProtectionDomain.java:87)
	at java.desktop/java.awt.EventQueue.dispatchEvent(EventQueue.java:750)
	at com.intellij.ide.IdeEventQueue.defaultDispatchEvent(IdeEventQueue.kt:595)
	at com.intellij.ide.IdeEventQueue._dispatchEvent(IdeEventQueue.kt:488)
	at com.intellij.ide.IdeEventQueue.dispatchEvent$lambda$12$lambda$11$lambda$10$lambda$9(IdeEventQueue.kt:313)
	at com.intellij.openapi.progress.impl.CoreProgressManager.computePrioritized(CoreProgressManager.java:865)
	at com.intellij.ide.IdeEventQueue.dispatchEvent$lambda$12$lambda$11$lambda$10(IdeEventQueue.kt:312)
	at com.intellij.ide.IdeEventQueueKt.performActivity$lambda$3(IdeEventQueue.kt:974)
	at com.intellij.openapi.application.TransactionGuardImpl.performActivity(TransactionGuardImpl.java:110)
	at com.intellij.ide.IdeEventQueueKt.performActivity(IdeEventQueue.kt:974)
	at com.intellij.ide.IdeEventQueue.dispatchEvent$lambda$12(IdeEventQueue.kt:307)
	at com.intellij.ide.IdeEventQueue.dispatchEvent(IdeEventQueue.kt:347)
	at java.desktop/java.awt.EventDispatchThread.pumpOneEventForFilters(EventDispatchThread.java:207)
	at java.desktop/java.awt.EventDispatchThread.pumpEventsForFilter(EventDispatchThread.java:128)
	at java.desktop/java.awt.EventDispatchThread.pumpEventsForHierarchy(EventDispatchThread.java:117)
	at java.desktop/java.awt.EventDispatchThread.pumpEvents(EventDispatchThread.java:113)
	at java.desktop/java.awt.EventDispatchThread.pumpEvents(EventDispatchThread.java:105)
	at java.desktop/java.awt.EventDispatchThread.run(EventDispatchThread.java:92)
2025-09-23 09:33:00,069 [ 130192] SEVERE - #c.i.o.progress - IntelliJ IDEA 2025.2.2  Build #IC-252.26199.169
2025-09-23 09:33:00,069 [ 130192] SEVERE - #c.i.o.progress - JDK: 21.0.8; VM: OpenJDK 64-Bit Server VM; Vendor: JetBrains s.r.o.
2025-09-23 09:33:00,069 [ 130192] SEVERE - #c.i.o.progress - OS: Windows 11
2025-09-23 09:33:00,069 [ 130192] SEVERE - #c.i.o.progress - Plugin to blame: Shell Script version: 252.26199.169
2025-09-23 09:33:00,071 [ 130194] SEVERE - #c.i.o.progress - This method is forbidden on EDT because it does not pump the event queue. Switch to a BGT, or use com.intellij.openapi.progress.TasksKt.runWithModalProgressBlocking. 
java.lang.IllegalStateException: This method is forbidden on EDT because it does not pump the event queue. Switch to a BGT, or use com.intellij.openapi.progress.TasksKt.runWithModalProgressBlocking. 
	at com.intellij.openapi.progress.CoroutinesKt.assertBackgroundThreadAndNoWriteAction(coroutines.kt:577)
	at com.intellij.openapi.progress.CoroutinesKt.runBlockingCancellable(coroutines.kt:130)
	at com.intellij.openapi.progress.CoroutinesKt.runBlockingMaybeCancellable(coroutines.kt:180)
	at com.intellij.execution.util.ExecUtil.startProcessBlockingUsingEel(ExecUtil.kt:217)
	at com.intellij.execution.configurations.GeneralCommandLine.createProcess(GeneralCommandLine.java:584)
	at com.intellij.execution.configurations.PtyCommandLine.createProcess(PtyCommandLine.java:123)
	at com.intellij.execution.configurations.GeneralCommandLine.startProcess(GeneralCommandLine.java:590)
	at com.intellij.execution.configurations.GeneralCommandLine.startProcess(GeneralCommandLine.java:432)
	at com.intellij.execution.configurations.GeneralCommandLine.createProcess(GeneralCommandLine.java:402)
	at com.intellij.execution.process.OSProcessHandler.startProcess(OSProcessHandler.java:85)
	at com.intellij.execution.process.OSProcessHandler.<init>(OSProcessHandler.java:45)
	at com.intellij.execution.process.KillableProcessHandler.<init>(KillableProcessHandler.java:40)
	at com.intellij.sh.run.ShRunConfigurationProfileState$1.<init>(ShRunConfigurationProfileState.java:83)
	at com.intellij.sh.run.ShRunConfigurationProfileState.createProcessHandler(ShRunConfigurationProfileState.java:83)
	at com.intellij.sh.run.ShRunConfigurationProfileState.buildExecutionResult(ShRunConfigurationProfileState.java:75)
	at com.intellij.sh.run.ShRunConfigurationProfileState.execute(ShRunConfigurationProfileState.java:59)
	at com.intellij.sh.run.ShRunProgramRunner.lambda$execute$0(ShRunProgramRunner.java:33)
	at com.intellij.execution.ExecutionManager.startRunProfile$lambda$0(ExecutionManager.kt:75)
	at com.intellij.execution.impl.ExecutionManagerImpl.startRunProfile$lambda$6(ExecutionManagerImpl.kt:229)
	at com.intellij.execution.impl.ExecutionManagerImpl.doStartRunProfile$lambda$15(ExecutionManagerImpl.kt:296)
	at com.intellij.openapi.application.TransactionGuardImpl.runWithWritingAllowed(TransactionGuardImpl.java:240)
	at com.intellij.openapi.application.TransactionGuardImpl.access$100(TransactionGuardImpl.java:26)
	at com.intellij.openapi.application.TransactionGuardImpl$1.run(TransactionGuardImpl.java:202)
	at com.intellij.openapi.application.impl.AppImplKt$runnableUnitFunction$1.invoke(appImpl.kt:104)
	at com.intellij.openapi.application.impl.AppImplKt$runnableUnitFunction$1.invoke(appImpl.kt:104)
	at com.intellij.platform.locking.impl.NestedLocksThreadingSupport.doRunWriteIntentReadAction(NestedLocksThreadingSupport.kt:666)
	at com.intellij.platform.locking.impl.NestedLocksThreadingSupport.runPreventiveWriteIntentReadAction(NestedLocksThreadingSupport.kt:640)
	at com.intellij.platform.locking.impl.NestedLocksThreadingSupport.runWriteIntentReadAction(NestedLocksThreadingSupport.kt:633)
	at com.intellij.openapi.application.impl.ApplicationImpl.runIntendedWriteActionOnCurrentThread(ApplicationImpl.java:1022)
	at com.intellij.openapi.application.impl.ApplicationImpl$6.run(ApplicationImpl.java:574)
	at com.intellij.util.concurrency.ChildContext$runInChildContext$1.invoke(propagation.kt:167)
	at com.intellij.util.concurrency.ChildContext$runInChildContext$1.invoke(propagation.kt:167)
	at com.intellij.util.concurrency.ChildContext.runInChildContext(propagation.kt:173)
	at com.intellij.util.concurrency.ChildContext.runInChildContext(propagation.kt:167)
	at com.intellij.util.concurrency.ContextRunnable.run(ContextRunnable.java:27)
	at com.intellij.openapi.application.impl.FlushQueue.runNextEvent(FlushQueue.java:122)
	at com.intellij.openapi.application.impl.FlushQueue.flushNow(FlushQueue.java:43)
	at java.desktop/java.awt.event.InvocationEvent.dispatch(InvocationEvent.java:318)
	at java.desktop/java.awt.EventQueue.dispatchEventImpl(EventQueue.java:781)
	at java.desktop/java.awt.EventQueue$4.run(EventQueue.java:728)
	at java.desktop/java.awt.EventQueue$4.run(EventQueue.java:722)
	at java.base/java.security.AccessController.doPrivileged(AccessController.java:400)
	at java.base/java.security.ProtectionDomain$JavaSecurityAccessImpl.doIntersectionPrivilege(ProtectionDomain.java:87)
	at java.desktop/java.awt.EventQueue.dispatchEvent(EventQueue.java:750)
	at com.intellij.ide.IdeEventQueue.defaultDispatchEvent(IdeEventQueue.kt:595)
	at com.intellij.ide.IdeEventQueue._dispatchEvent(IdeEventQueue.kt:488)
	at com.intellij.ide.IdeEventQueue.dispatchEvent$lambda$12$lambda$11$lambda$10$lambda$9(IdeEventQueue.kt:313)
	at com.intellij.openapi.progress.impl.CoreProgressManager.computePrioritized(CoreProgressManager.java:865)
	at com.intellij.ide.IdeEventQueue.dispatchEvent$lambda$12$lambda$11$lambda$10(IdeEventQueue.kt:312)
	at com.intellij.ide.IdeEventQueueKt.performActivity$lambda$3(IdeEventQueue.kt:974)
	at com.intellij.openapi.application.TransactionGuardImpl.performActivity(TransactionGuardImpl.java:110)
	at com.intellij.ide.IdeEventQueueKt.performActivity(IdeEventQueue.kt:974)
	at com.intellij.ide.IdeEventQueue.dispatchEvent$lambda$12(IdeEventQueue.kt:307)
	at com.intellij.ide.IdeEventQueue.dispatchEvent(IdeEventQueue.kt:347)
	at java.desktop/java.awt.EventDispatchThread.pumpOneEventForFilters(EventDispatchThread.java:207)
	at java.desktop/java.awt.EventDispatchThread.pumpEventsForFilter(EventDispatchThread.java:128)
	at java.desktop/java.awt.EventDispatchThread.pumpEventsForHierarchy(EventDispatchThread.java:117)
	at java.desktop/java.awt.EventDispatchThread.pumpEvents(EventDispatchThread.java:113)
	at java.desktop/java.awt.EventDispatchThread.pumpEvents(EventDispatchThread.java:105)
	at java.desktop/java.awt.EventDispatchThread.run(EventDispatchThread.java:92)
2025-09-23 09:33:00,072 [ 130195] SEVERE - #c.i.o.progress - IntelliJ IDEA 2025.2.2  Build #IC-252.26199.169
2025-09-23 09:33:00,072 [ 130195] SEVERE - #c.i.o.progress - JDK: 21.0.8; VM: OpenJDK 64-Bit Server VM; Vendor: JetBrains s.r.o.
2025-09-23 09:33:00,072 [ 130195] SEVERE - #c.i.o.progress - OS: Windows 11
2025-09-23 09:33:00,072 [ 130195] SEVERE - #c.i.o.progress - Plugin to blame: Shell Script version: 252.26199.169
2025-09-23 09:33:00,077 [ 130200] SEVERE - #c.i.o.progress - This method is forbidden on EDT because it does not pump the event queue. Switch to a BGT, or use com.intellij.openapi.progress.TasksKt.runWithModalProgressBlocking. 
java.lang.IllegalStateException: This method is forbidden on EDT because it does not pump the event queue. Switch to a BGT, or use com.intellij.openapi.progress.TasksKt.runWithModalProgressBlocking. 
	at com.intellij.openapi.progress.CoroutinesKt.assertBackgroundThreadAndNoWriteAction(coroutines.kt:577)
	at com.intellij.openapi.progress.CoroutinesKt.runBlockingCancellable(coroutines.kt:130)
	at com.intellij.openapi.progress.CoroutinesKt.runBlockingMaybeCancellable(coroutines.kt:180)
	at com.intellij.execution.util.ExecUtil.startProcessBlockingUsingEel(ExecUtil.kt:226)
	at com.intellij.execution.configurations.GeneralCommandLine.createProcess(GeneralCommandLine.java:584)
	at com.intellij.execution.configurations.PtyCommandLine.createProcess(PtyCommandLine.java:123)
	at com.intellij.execution.configurations.GeneralCommandLine.startProcess(GeneralCommandLine.java:590)
	at com.intellij.execution.configurations.GeneralCommandLine.startProcess(GeneralCommandLine.java:432)
	at com.intellij.execution.configurations.GeneralCommandLine.createProcess(GeneralCommandLine.java:402)
	at com.intellij.execution.process.OSProcessHandler.startProcess(OSProcessHandler.java:85)
	at com.intellij.execution.process.OSProcessHandler.<init>(OSProcessHandler.java:45)
	at com.intellij.execution.process.KillableProcessHandler.<init>(KillableProcessHandler.java:40)
	at com.intellij.sh.run.ShRunConfigurationProfileState$1.<init>(ShRunConfigurationProfileState.java:83)
	at com.intellij.sh.run.ShRunConfigurationProfileState.createProcessHandler(ShRunConfigurationProfileState.java:83)
	at com.intellij.sh.run.ShRunConfigurationProfileState.buildExecutionResult(ShRunConfigurationProfileState.java:75)
	at com.intellij.sh.run.ShRunConfigurationProfileState.execute(ShRunConfigurationProfileState.java:59)
	at com.intellij.sh.run.ShRunProgramRunner.lambda$execute$0(ShRunProgramRunner.java:33)
	at com.intellij.execution.ExecutionManager.startRunProfile$lambda$0(ExecutionManager.kt:75)
	at com.intellij.execution.impl.ExecutionManagerImpl.startRunProfile$lambda$6(ExecutionManagerImpl.kt:229)
	at com.intellij.execution.impl.ExecutionManagerImpl.doStartRunProfile$lambda$15(ExecutionManagerImpl.kt:296)
	at com.intellij.openapi.application.TransactionGuardImpl.runWithWritingAllowed(TransactionGuardImpl.java:240)
	at com.intellij.openapi.application.TransactionGuardImpl.access$100(TransactionGuardImpl.java:26)
	at com.intellij.openapi.application.TransactionGuardImpl$1.run(TransactionGuardImpl.java:202)
	at com.intellij.openapi.application.impl.AppImplKt$runnableUnitFunction$1.invoke(appImpl.kt:104)
	at com.intellij.openapi.application.impl.AppImplKt$runnableUnitFunction$1.invoke(appImpl.kt:104)
	at com.intellij.platform.locking.impl.NestedLocksThreadingSupport.doRunWriteIntentReadAction(NestedLocksThreadingSupport.kt:666)
	at com.intellij.platform.locking.impl.NestedLocksThreadingSupport.runPreventiveWriteIntentReadAction(NestedLocksThreadingSupport.kt:640)
	at com.intellij.platform.locking.impl.NestedLocksThreadingSupport.runWriteIntentReadAction(NestedLocksThreadingSupport.kt:633)
	at com.intellij.openapi.application.impl.ApplicationImpl.runIntendedWriteActionOnCurrentThread(ApplicationImpl.java:1022)
	at com.intellij.openapi.application.impl.ApplicationImpl$6.run(ApplicationImpl.java:574)
	at com.intellij.util.concurrency.ChildContext$runInChildContext$1.invoke(propagation.kt:167)
	at com.intellij.util.concurrency.ChildContext$runInChildContext$1.invoke(propagation.kt:167)
	at com.intellij.util.concurrency.ChildContext.runInChildContext(propagation.kt:173)
	at com.intellij.util.concurrency.ChildContext.runInChildContext(propagation.kt:167)
	at com.intellij.util.concurrency.ContextRunnable.run(ContextRunnable.java:27)
	at com.intellij.openapi.application.impl.FlushQueue.runNextEvent(FlushQueue.java:122)
	at com.intellij.openapi.application.impl.FlushQueue.flushNow(FlushQueue.java:43)
	at java.desktop/java.awt.event.InvocationEvent.dispatch(InvocationEvent.java:318)
	at java.desktop/java.awt.EventQueue.dispatchEventImpl(EventQueue.java:781)
	at java.desktop/java.awt.EventQueue$4.run(EventQueue.java:728)
	at java.desktop/java.awt.EventQueue$4.run(EventQueue.java:722)
	at java.base/java.security.AccessController.doPrivileged(AccessController.java:400)
	at java.base/java.security.ProtectionDomain$JavaSecurityAccessImpl.doIntersectionPrivilege(ProtectionDomain.java:87)
	at java.desktop/java.awt.EventQueue.dispatchEvent(EventQueue.java:750)
	at com.intellij.ide.IdeEventQueue.defaultDispatchEvent(IdeEventQueue.kt:595)
	at com.intellij.ide.IdeEventQueue._dispatchEvent(IdeEventQueue.kt:488)
	at com.intellij.ide.IdeEventQueue.dispatchEvent$lambda$12$lambda$11$lambda$10$lambda$9(IdeEventQueue.kt:313)
	at com.intellij.openapi.progress.impl.CoreProgressManager.computePrioritized(CoreProgressManager.java:865)
	at com.intellij.ide.IdeEventQueue.dispatchEvent$lambda$12$lambda$11$lambda$10(IdeEventQueue.kt:312)
	at com.intellij.ide.IdeEventQueueKt.performActivity$lambda$3(IdeEventQueue.kt:974)
	at com.intellij.openapi.application.TransactionGuardImpl.performActivity(TransactionGuardImpl.java:110)
	at com.intellij.ide.IdeEventQueueKt.performActivity(IdeEventQueue.kt:974)
	at com.intellij.ide.IdeEventQueue.dispatchEvent$lambda$12(IdeEventQueue.kt:307)
	at com.intellij.ide.IdeEventQueue.dispatchEvent(IdeEventQueue.kt:347)
	at java.desktop/java.awt.EventDispatchThread.pumpOneEventForFilters(EventDispatchThread.java:207)
	at java.desktop/java.awt.EventDispatchThread.pumpEventsForFilter(EventDispatchThread.java:128)
	at java.desktop/java.awt.EventDispatchThread.pumpEventsForHierarchy(EventDispatchThread.java:117)
	at java.desktop/java.awt.EventDispatchThread.pumpEvents(EventDispatchThread.java:113)
	at java.desktop/java.awt.EventDispatchThread.pumpEvents(EventDispatchThread.java:105)
	at java.desktop/java.awt.EventDispatchThread.run(EventDispatchThread.java:92)
2025-09-23 09:33:00,078 [ 130201] SEVERE - #c.i.o.progress - IntelliJ IDEA 2025.2.2  Build #IC-252.26199.169
2025-09-23 09:33:00,078 [ 130201] SEVERE - #c.i.o.progress - JDK: 21.0.8; VM: OpenJDK 64-Bit Server VM; Vendor: JetBrains s.r.o.
2025-09-23 09:33:00,078 [ 130201] SEVERE - #c.i.o.progress - OS: Windows 11
2025-09-23 09:33:00,078 [ 130201] SEVERE - #c.i.o.progress - Plugin to blame: Shell Script version: 252.26199.169
2025-09-23 09:33:00,657 [ 130780]   WARN - #c.i.e.wsl - Failed to get WSL mount root for Ubuntu: DOS working directory is expected, but got \\wsl.localhost\Ubuntu\home\oni\audio-transcriber
2025-09-23 09:33:04,445 [ 134568]   WARN - #c.i.o.v.i.w.W.Ubuntu - table error: collision at 9056 (new /home/<USER>/audio-transcriber/node_modules/.cookie-signature-ZYp1MFv7, existing /home/<USER>/audio-transcriber/node_modules/cookie-signature)
2025-09-23 09:33:04,459 [ 134582]   WARN - #c.i.o.v.i.w.W.Ubuntu - Watcher terminated with exit code 3
2025-09-23 09:33:06,036 [ 136159]   WARN - #com.augmentcode.intellij.chat.ChatWebviewMessageClient$Companion - Workspace guidelines are empty, not including in chat request. 
2025-09-23 09:33:07,290 [ 137413]   WARN - #c.i.e.wsl - Failed to get WSL mount root for Ubuntu: DOS working directory is expected, but got \\wsl.localhost\Ubuntu\home\oni\audio-transcriber
2025-09-23 09:33:09,337 [ 139460]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: No exchange with this request ID found. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:33:10,843 [ 140966]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: IntersectionDisplay context not found.  Call setIntersectionContext first. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:33:11,126 [ 141249]   WARN - #com.augmentcode.intellij.webviews.AbstractWebviewMessagingService - Failed to parse webview message as protobuf.
Exception: Missing type url when parsing: {"type":"can-show-terminal","data":{"command":"npm install -D tailwindcss postcss autoprefixer"}}
Message: {"type":"async-wrapper","requestId":"6be71d03-eff8-4240-81fb-b7d6af51c944","error":null,"baseMsg":{"type":"can-show-terminal","data":{"command":"npm install -D tailwindcss postcss autoprefixer"}},"destination":"host"}
2025-09-23 09:33:11,127 [ 141250]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_ERROR]: Failed to check if terminal can be shown: Error: Unable to parse webview message: {"type":"async-wrapper","requestId":"6be71d03-eff8-4240-81fb-b7d6af51c944","error":null,"baseMsg":{"type":"can-show-terminal","data":{"command":"npm install -D tailwindcss postcss autoprefixer"}},"destination":"host"} [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:33:11,910 [ 142033]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Non of the blobs were indexed, backing off and retrying...
2025-09-23 09:33:12,172 [ 142295]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: f9db5b6c8b6dc41584c8fdb7b7cf0d999425b4ca8ee2bf1db765313ebb1a6da9 -> 69ccdfce759b02458f42eadea0dbc7976ac875c0b1ef5e22c5ca6c300aafa7d9
2025-09-23 09:33:58,033 [ 188156]   WARN - #com.augmentcode.intellij.sentry.SentryMetadataCollector - Git ls-files command failed with exit code: 128
2025-09-23 09:33:58,888 [ 189011]   WARN - #com.augmentcode.intellij.webviews.AbstractWebviewMessagingService - Failed to parse webview message as protobuf.
Exception: Missing type url when parsing: {"type":"can-show-terminal","data":{"command":"npm install -D tailwindcss postcss autoprefixer"}}
Message: {"type":"async-wrapper","requestId":"652a0ad4-569d-4b87-82e7-d2843c6fd99f","error":null,"baseMsg":{"type":"can-show-terminal","data":{"command":"npm install -D tailwindcss postcss autoprefixer"}},"destination":"host"}
2025-09-23 09:33:58,890 [ 189013]   WARN - #c.i.o.progress - Suppressed a frequent exception logged for the 2nd time: This method is forbidden on EDT because it does not pump the event queue. Switch to a BGT, or use com.intellij.openapi.progress.TasksKt.runWithModalProgressBlocking. 
2025-09-23 09:33:58,890 [ 189013]   WARN - #c.i.o.progress - Suppressed a frequent exception logged for the 2nd time: This method is forbidden on EDT because it does not pump the event queue. Switch to a BGT, or use com.intellij.openapi.progress.TasksKt.runWithModalProgressBlocking. 
2025-09-23 09:33:58,892 [ 189015]   WARN - #c.i.o.progress - Suppressed a frequent exception logged for the 2nd time: This method is forbidden on EDT because it does not pump the event queue. Switch to a BGT, or use com.intellij.openapi.progress.TasksKt.runWithModalProgressBlocking. 
2025-09-23 09:33:58,892 [ 189015]   WARN - #c.i.o.progress - Suppressed a frequent exception logged for the 2nd time: This method is forbidden on EDT because it does not pump the event queue. Switch to a BGT, or use com.intellij.openapi.progress.TasksKt.runWithModalProgressBlocking. 
2025-09-23 09:33:58,893 [ 189016]   WARN - #c.i.o.progress - Suppressed a frequent exception logged for the 2nd time: This method is forbidden on EDT because it does not pump the event queue. Switch to a BGT, or use com.intellij.openapi.progress.TasksKt.runWithModalProgressBlocking. 
2025-09-23 09:33:58,898 [ 189021]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_ERROR]: Failed to check if terminal can be shown: Error: Unable to parse webview message: {"type":"async-wrapper","requestId":"652a0ad4-569d-4b87-82e7-d2843c6fd99f","error":null,"baseMsg":{"type":"can-show-terminal","data":{"command":"npm install -D tailwindcss postcss autoprefixer"}},"destination":"host"} [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:33:59,455 [ 189578]   WARN - #c.i.e.wsl - Failed to get WSL mount root for Ubuntu: DOS working directory is expected, but got \\wsl.localhost\Ubuntu\home\oni\audio-transcriber
2025-09-23 09:34:04,498 [ 194621]   WARN - #com.augmentcode.intellij.chat.ChatWebviewMessageClient$Companion - Workspace guidelines are empty, not including in chat request. 
2025-09-23 09:34:05,461 [ 195584]   WARN - #c.i.e.wsl - Failed to get WSL mount root for Ubuntu: DOS working directory is expected, but got \\wsl.localhost\Ubuntu\home\oni\audio-transcriber
2025-09-23 09:34:07,721 [ 197844]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: No exchange with this request ID found. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:34:09,349 [ 199472]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: IntersectionDisplay context not found.  Call setIntersectionContext first. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:34:09,580 [ 199703]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 70ebac7484969feb28f4841fe9e3340bf759d81e9eeab0bf40d8a5901fbfc921 -> 26307c9cafb85f37dfb7b8a661544ff67c5f286944de5867ec4819689fe4070a
2025-09-23 09:34:09,624 [ 199747]   WARN - #com.augmentcode.intellij.webviews.AbstractWebviewMessagingService - Failed to parse webview message as protobuf.
Exception: Missing type url when parsing: {"type":"can-show-terminal","data":{"command":"npx tailwindcss init -p"}}
Message: {"type":"async-wrapper","requestId":"f526ca18-bd90-4871-8ecc-bb122aa3fbc8","error":null,"baseMsg":{"type":"can-show-terminal","data":{"command":"npx tailwindcss init -p"}},"destination":"host"}
2025-09-23 09:34:09,625 [ 199748]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_ERROR]: Failed to check if terminal can be shown: Error: Unable to parse webview message: {"type":"async-wrapper","requestId":"f526ca18-bd90-4871-8ecc-bb122aa3fbc8","error":null,"baseMsg":{"type":"can-show-terminal","data":{"command":"npx tailwindcss init -p"}},"destination":"host"} [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:34:11,610 [ 201733]   WARN - #com.augmentcode.intellij.webviews.AbstractWebviewMessagingService - Failed to parse webview message as protobuf.
Exception: Missing type url when parsing: {"type":"can-show-terminal","data":{"command":"npx tailwindcss init -p"}}
Message: {"type":"async-wrapper","requestId":"438cebca-64c1-49c5-b21b-f6f1c2eeaa19","error":null,"baseMsg":{"type":"can-show-terminal","data":{"command":"npx tailwindcss init -p"}},"destination":"host"}
2025-09-23 09:34:11,619 [ 201742]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_ERROR]: Failed to check if terminal can be shown: Error: Unable to parse webview message: {"type":"async-wrapper","requestId":"438cebca-64c1-49c5-b21b-f6f1c2eeaa19","error":null,"baseMsg":{"type":"can-show-terminal","data":{"command":"npx tailwindcss init -p"}},"destination":"host"} [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:34:12,571 [ 202694]   WARN - #com.augmentcode.intellij.chat.ChatWebviewMessageClient$Companion - Workspace guidelines are empty, not including in chat request. 
2025-09-23 09:34:13,352 [ 203475]   WARN - #c.i.e.wsl - Failed to get WSL mount root for Ubuntu: DOS working directory is expected, but got \\wsl.localhost\Ubuntu\home\oni\audio-transcriber
2025-09-23 09:34:15,646 [ 205769]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: No exchange with this request ID found. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:34:16,470 [ 206593]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Non of the blobs were indexed, backing off and retrying...
2025-09-23 09:34:16,873 [ 206996]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: IntersectionDisplay context not found.  Call setIntersectionContext first. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:34:17,160 [ 207283]   WARN - #com.augmentcode.intellij.webviews.AbstractWebviewMessagingService - Failed to parse webview message as protobuf.
Exception: Missing type url when parsing: {"type":"can-show-terminal","data":{"command":"./node_modules/.bin/tailwindcss init -p"}}
Message: {"type":"async-wrapper","requestId":"7851f1c5-1c91-4381-9724-90b94ab6b6d7","error":null,"baseMsg":{"type":"can-show-terminal","data":{"command":"./node_modules/.bin/tailwindcss init -p"}},"destination":"host"}
2025-09-23 09:34:17,162 [ 207285]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_ERROR]: Failed to check if terminal can be shown: Error: Unable to parse webview message: {"type":"async-wrapper","requestId":"7851f1c5-1c91-4381-9724-90b94ab6b6d7","error":null,"baseMsg":{"type":"can-show-terminal","data":{"command":"./node_modules/.bin/tailwindcss init -p"}},"destination":"host"} [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:34:18,650 [ 208773]   WARN - #com.augmentcode.intellij.webviews.AbstractWebviewMessagingService - Failed to parse webview message as protobuf.
Exception: Missing type url when parsing: {"type":"can-show-terminal","data":{"command":"./node_modules/.bin/tailwindcss init -p"}}
Message: {"type":"async-wrapper","requestId":"56bbc764-163d-452c-96e7-65f04fa8cf71","error":null,"baseMsg":{"type":"can-show-terminal","data":{"command":"./node_modules/.bin/tailwindcss init -p"}},"destination":"host"}
2025-09-23 09:34:18,661 [ 208784]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_ERROR]: Failed to check if terminal can be shown: Error: Unable to parse webview message: {"type":"async-wrapper","requestId":"56bbc764-163d-452c-96e7-65f04fa8cf71","error":null,"baseMsg":{"type":"can-show-terminal","data":{"command":"./node_modules/.bin/tailwindcss init -p"}},"destination":"host"} [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:34:18,728 [ 208851]   WARN - #com.augmentcode.intellij.chat.ChatWebviewMessageClient$Companion - Workspace guidelines are empty, not including in chat request. 
2025-09-23 09:34:19,334 [ 209457]   WARN - #c.i.e.wsl - Failed to get WSL mount root for Ubuntu: DOS working directory is expected, but got \\wsl.localhost\Ubuntu\home\oni\audio-transcriber
2025-09-23 09:34:22,408 [ 212531]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: No exchange with this request ID found. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:34:23,567 [ 213690]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: IntersectionDisplay context not found.  Call setIntersectionContext first. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:34:23,616 [ 213739]   WARN - #com.augmentcode.intellij.chat.ChatWebviewMessageClient$Companion - Workspace guidelines are empty, not including in chat request. 
2025-09-23 09:34:26,339 [ 216462]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: No exchange with this request ID found. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:34:30,630 [ 220753]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: IntersectionDisplay context not found.  Call setIntersectionContext first. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:34:31,840 [ 221963]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: IntersectionDisplay context not found.  Call setIntersectionContext first. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:34:33,310 [ 223433]   WARN - #com.augmentcode.intellij.chat.ChatWebviewMessageClient$Companion - Workspace guidelines are empty, not including in chat request. 
2025-09-23 09:34:36,525 [ 226648]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: No exchange with this request ID found. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:34:39,372 [ 229495]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: IntersectionDisplay context not found.  Call setIntersectionContext first. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:34:39,658 [ 229781]   WARN - #com.augmentcode.intellij.webviews.AbstractWebviewMessagingService - Failed to parse webview message as protobuf.
Exception: Missing type url when parsing: {"type":"can-show-terminal","data":{"command":"mkdir -p server"}}
Message: {"type":"async-wrapper","requestId":"eb7ac5fb-a33a-4706-b096-65172e86e3b6","error":null,"baseMsg":{"type":"can-show-terminal","data":{"command":"mkdir -p server"}},"destination":"host"}
2025-09-23 09:34:39,660 [ 229783]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_ERROR]: Failed to check if terminal can be shown: Error: Unable to parse webview message: {"type":"async-wrapper","requestId":"eb7ac5fb-a33a-4706-b096-65172e86e3b6","error":null,"baseMsg":{"type":"can-show-terminal","data":{"command":"mkdir -p server"}},"destination":"host"} [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:34:58,174 [ 248297]   WARN - #com.augmentcode.intellij.sentry.SentryMetadataCollector - Git ls-files command failed with exit code: 128
2025-09-23 09:35:07,294 [ 257417]   WARN - #com.augmentcode.intellij.api.AugmentHttpClient$Companion - Failed to call client-metrics (eabf5b36-fc8b-4b56-a546-3920912a0b4f)
io.ktor.client.plugins.HttpRequestTimeoutException: Request timeout has expired [url=https://i1.api.augmentcode.com/client-metrics, request_timeout=60000 ms]
	at io.ktor.client.plugins.HttpTimeout$Plugin$install$1$1$killer$1.invokeSuspend(HttpTimeout.kt:165)
	at kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)
	at kotlinx.coroutines.DispatchedTask.run(DispatchedTask.kt:100)
	at kotlinx.coroutines.internal.SoftLimitedDispatcher$Worker.run(SoftLimitedDispatcher.kt:130)
	at kotlinx.coroutines.scheduling.TaskImpl.run(Tasks.kt:89)
	at kotlinx.coroutines.scheduling.CoroutineScheduler.runSafely(CoroutineScheduler.kt:613)
	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.executeTask(CoroutineScheduler.kt:1183)
	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.runWorker(CoroutineScheduler.kt:778)
	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.run(CoroutineScheduler.kt:765)
2025-09-23 09:35:07,294 [ 257417]   WARN - #com.augmentcode.intellij.metrics.MetricsReporter - Failed to upload 1 metrics
java.lang.IllegalStateException: Failed to make network call to client-metrics with request ID eabf5b36-fc8b-4b56-a546-3920912a0b4f: Request timeout has expired [url=https://i1.api.augmentcode.com/client-metrics, request_timeout=60000 ms]
	at com.augmentcode.intellij.api.AugmentHttpClient.wrapNetworkError(AugmentHttpClient.kt:223)
	at com.augmentcode.intellij.api.AugmentHttpClient.access$wrapNetworkError(AugmentHttpClient.kt:31)
	at com.augmentcode.intellij.api.AugmentHttpClient$post$2.invokeSuspend(AugmentHttpClient.kt:180)
	at kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)
	at kotlinx.coroutines.DispatchedTask.run(DispatchedTask.kt:98)
	at kotlinx.coroutines.scheduling.CoroutineScheduler.runSafely(CoroutineScheduler.kt:613)
	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.executeTask(CoroutineScheduler.kt:1189)
	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.runWorker(CoroutineScheduler.kt:778)
	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.run(CoroutineScheduler.kt:765)
Caused by: io.ktor.client.plugins.HttpRequestTimeoutException: Request timeout has expired [url=https://i1.api.augmentcode.com/client-metrics, request_timeout=60000 ms]
	at io.ktor.client.plugins.HttpTimeout$Plugin$install$1$1$killer$1.invokeSuspend(HttpTimeout.kt:165)
	at kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)
	at kotlinx.coroutines.DispatchedTask.run(DispatchedTask.kt:100)
	at kotlinx.coroutines.internal.SoftLimitedDispatcher$Worker.run(SoftLimitedDispatcher.kt:130)
	at kotlinx.coroutines.scheduling.TaskImpl.run(Tasks.kt:89)
	at kotlinx.coroutines.scheduling.CoroutineScheduler.runSafely(CoroutineScheduler.kt:613)
	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.executeTask(CoroutineScheduler.kt:1183)
	... 2 more
2025-09-23 09:35:28,285 [ 278408]   WARN - #com.augmentcode.intellij.webviews.AbstractWebviewMessagingService - Failed to parse webview message as protobuf.
Exception: Missing type url when parsing: {"type":"can-show-terminal","data":{"command":"mkdir -p server"}}
Message: {"type":"async-wrapper","requestId":"1cff6e48-675e-486f-8f2f-e1082f23e71a","error":null,"baseMsg":{"type":"can-show-terminal","data":{"command":"mkdir -p server"}},"destination":"host"}
2025-09-23 09:35:28,288 [ 278411]   WARN - #c.i.o.progress - Suppressed a frequent exception logged for the 5th time: This method is forbidden on EDT because it does not pump the event queue. Switch to a BGT, or use com.intellij.openapi.progress.TasksKt.runWithModalProgressBlocking. 
2025-09-23 09:35:28,288 [ 278411]   WARN - #c.i.o.progress - Suppressed a frequent exception logged for the 5th time: This method is forbidden on EDT because it does not pump the event queue. Switch to a BGT, or use com.intellij.openapi.progress.TasksKt.runWithModalProgressBlocking. 
2025-09-23 09:35:28,289 [ 278412]   WARN - #c.i.o.progress - Suppressed a frequent exception logged for the 5th time: This method is forbidden on EDT because it does not pump the event queue. Switch to a BGT, or use com.intellij.openapi.progress.TasksKt.runWithModalProgressBlocking. 
2025-09-23 09:35:28,290 [ 278413]   WARN - #c.i.o.progress - Suppressed a frequent exception logged for the 5th time: This method is forbidden on EDT because it does not pump the event queue. Switch to a BGT, or use com.intellij.openapi.progress.TasksKt.runWithModalProgressBlocking. 
2025-09-23 09:35:28,290 [ 278413]   WARN - #c.i.o.progress - Suppressed a frequent exception logged for the 5th time: This method is forbidden on EDT because it does not pump the event queue. Switch to a BGT, or use com.intellij.openapi.progress.TasksKt.runWithModalProgressBlocking. 
2025-09-23 09:35:28,305 [ 278428]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_ERROR]: Failed to check if terminal can be shown: Error: Unable to parse webview message: {"type":"async-wrapper","requestId":"1cff6e48-675e-486f-8f2f-e1082f23e71a","error":null,"baseMsg":{"type":"can-show-terminal","data":{"command":"mkdir -p server"}},"destination":"host"} [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:35:28,327 [ 278450]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_ERROR]: _validateAndCheckTool called while another tool is active [object Object] [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:35:28,353 [ 278476]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: IntersectionDisplay context not found.  Call setIntersectionContext first. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:35:28,390 [ 278513]   WARN - #c.i.e.wsl - Failed to get WSL mount root for Ubuntu: DOS working directory is expected, but got \\wsl.localhost\Ubuntu\home\oni\audio-transcriber
2025-09-23 09:35:28,515 [ 278638]   WARN - #com.augmentcode.intellij.chat.ChatWebviewMessageClient$Companion - Workspace guidelines are empty, not including in chat request. 
2025-09-23 09:35:33,249 [ 283372]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: No exchange with this request ID found. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:35:46,503 [ 296626]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: IntersectionDisplay context not found.  Call setIntersectionContext first. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:35:46,633 [ 296756]   WARN - #com.augmentcode.intellij.chat.ChatWebviewMessageClient$Companion - Workspace guidelines are empty, not including in chat request. 
2025-09-23 09:35:50,611 [ 300734]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: No exchange with this request ID found. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:35:54,173 [ 304296]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Non of the blobs were indexed, backing off and retrying...
2025-09-23 09:35:56,116 [ 306239]   WARN - #c.i.u.i.p.ProjectIndexableFilesFilterHealthCheck - Following files are indexable but they were NOT found in filter. Errors count: 1. Examples:
file id=198693 path=//wsl.localhost/Ubuntu/home/<USER>/audio-transcriber/node_modules/react-scripts/node_modules/tailwindcss/peers/index.js
2025-09-23 09:35:58,325 [ 308448]   WARN - #com.augmentcode.intellij.sentry.SentryMetadataCollector - Git ls-files command failed with exit code: 128
2025-09-23 09:36:00,825 [ 310948]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: IntersectionDisplay context not found.  Call setIntersectionContext first. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:36:00,950 [ 311073]   WARN - #com.augmentcode.intellij.chat.ChatWebviewMessageClient$Companion - Workspace guidelines are empty, not including in chat request. 
2025-09-23 09:36:04,842 [ 314965]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: No exchange with this request ID found. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:36:06,472 [ 316595]   WARN - #com.augmentcode.intellij.api.AugmentHttpClient$Companion - Failed to call record-session-events (bf79660d-9598-4dc4-bb04-1e5e5f18c9f8): 400 Bad Request
2025-09-23 09:36:06,472 [ 316595]   WARN - #com.augmentcode.intellij.api.AugmentAPI$Companion - Failed to record session events: 400 Bad Request
2025-09-23 09:36:10,917 [ 321040]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: IntersectionDisplay context not found.  Call setIntersectionContext first. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:36:19,173 [ 329296]   WARN - #c.i.e.wsl - Failed to get WSL mount root for Ubuntu: DOS working directory is expected, but got \\wsl.localhost\Ubuntu\home\oni\audio-transcriber
2025-09-23 09:36:20,196 [ 330319]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: Loading changes  for 77f85e56-27c4-49cf-bd22-07899593832e retry 0:  Error: No changes found [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:36:20,198 [ 330321]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: Loading changes  for 77f85e56-27c4-49cf-bd22-07899593832e retry 1:  Error: No changes found [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:36:20,208 [ 330331]   WARN - #com.augmentcode.intellij.chat.ChatWebviewMessageClient$Companion - Workspace guidelines are empty, not including in chat request. 
2025-09-23 09:36:20,700 [ 330823]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: Loading changes  for 77f85e56-27c4-49cf-bd22-07899593832e retry 2:  Error: No changes found [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:36:20,702 [ 330825]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: Loading changes  for 77f85e56-27c4-49cf-bd22-07899593832e retry 3:  Error: No changes found [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:36:20,702 [ 330825]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: Loading changes failed for 77f85e56-27c4-49cf-bd22-07899593832e:  Error: No changes found [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:36:21,206 [ 331329]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: Loading changes  for 77f85e56-27c4-49cf-bd22-07899593832e retry 4:  Error: No changes found [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:36:21,206 [ 331329]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: Loading changes failed for 77f85e56-27c4-49cf-bd22-07899593832e:  Error: No changes found [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:36:23,358 [ 333481]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: No exchange with this request ID found. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:36:26,979 [ 337102]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Non of the blobs were indexed, backing off and retrying...
2025-09-23 09:36:35,082 [ 345205]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: IntersectionDisplay context not found.  Call setIntersectionContext first. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:36:36,130 [ 346253]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: Loading changes  for cc4fc4c0-65ec-4b0b-a94a-6a9f0b8f6458 retry 0:  Error: No changes found [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:36:36,138 [ 346261]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: Loading changes  for cc4fc4c0-65ec-4b0b-a94a-6a9f0b8f6458 retry 1:  Error: No changes found [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:36:36,149 [ 346272]   WARN - #com.augmentcode.intellij.chat.ChatWebviewMessageClient$Companion - Workspace guidelines are empty, not including in chat request. 
2025-09-23 09:36:36,634 [ 346757]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: Loading changes  for cc4fc4c0-65ec-4b0b-a94a-6a9f0b8f6458 retry 2:  Error: No changes found [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:36:36,641 [ 346764]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: Loading changes  for cc4fc4c0-65ec-4b0b-a94a-6a9f0b8f6458 retry 3:  Error: No changes found [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:36:36,641 [ 346764]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: Loading changes failed for cc4fc4c0-65ec-4b0b-a94a-6a9f0b8f6458:  Error: No changes found [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:36:37,138 [ 347261]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: Loading changes  for cc4fc4c0-65ec-4b0b-a94a-6a9f0b8f6458 retry 4:  Error: No changes found [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:36:37,138 [ 347261]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: Loading changes failed for cc4fc4c0-65ec-4b0b-a94a-6a9f0b8f6458:  Error: No changes found [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:36:39,191 [ 349314]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: No exchange with this request ID found. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:36:46,347 [ 356470]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Non of the blobs were indexed, backing off and retrying...
2025-09-23 09:36:50,348 [ 360471]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: IntersectionDisplay context not found.  Call setIntersectionContext first. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:36:50,441 [ 360564]   WARN - #c.i.e.wsl - Failed to get WSL mount root for Ubuntu: DOS working directory is expected, but got \\wsl.localhost\Ubuntu\home\oni\audio-transcriber
2025-09-23 09:36:51,398 [ 361521]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: Loading changes  for c5af8158-e25d-4b08-bfc3-feb5703d90b5 retry 0:  Error: No changes found [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:36:51,405 [ 361528]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: Loading changes  for c5af8158-e25d-4b08-bfc3-feb5703d90b5 retry 1:  Error: No changes found [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:36:51,414 [ 361537]   WARN - #com.augmentcode.intellij.chat.ChatWebviewMessageClient$Companion - Workspace guidelines are empty, not including in chat request. 
2025-09-23 09:36:51,902 [ 362025]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: Loading changes  for c5af8158-e25d-4b08-bfc3-feb5703d90b5 retry 2:  Error: No changes found [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:36:51,907 [ 362030]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: Loading changes  for c5af8158-e25d-4b08-bfc3-feb5703d90b5 retry 3:  Error: No changes found [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:36:51,907 [ 362030]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: Loading changes failed for c5af8158-e25d-4b08-bfc3-feb5703d90b5:  Error: No changes found [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:36:52,406 [ 362529]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: Loading changes  for c5af8158-e25d-4b08-bfc3-feb5703d90b5 retry 4:  Error: No changes found [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:36:52,406 [ 362529]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: Loading changes failed for c5af8158-e25d-4b08-bfc3-feb5703d90b5:  Error: No changes found [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:36:54,545 [ 364668]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: No exchange with this request ID found. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:36:56,792 [ 366915]   WARN - #c.i.e.wsl - Failed to get WSL mount root for Ubuntu: DOS working directory is expected, but got \\wsl.localhost\Ubuntu\home\oni\audio-transcriber
2025-09-23 09:36:57,766 [ 367889]   WARN - #c.i.e.wsl - Failed to get WSL mount root for Ubuntu: DOS working directory is expected, but got \\wsl.localhost\Ubuntu\home\oni\audio-transcriber
2025-09-23 09:36:58,501 [ 368624]   WARN - #com.augmentcode.intellij.sentry.SentryMetadataCollector - Git ls-files command failed with exit code: 128
2025-09-23 09:37:04,213 [ 374336]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Non of the blobs were indexed, backing off and retrying...
2025-09-23 09:37:04,574 [ 374697]   WARN - #c.i.e.wsl - Failed to get WSL mount root for Ubuntu: DOS working directory is expected, but got \\wsl.localhost\Ubuntu\home\oni\audio-transcriber
2025-09-23 09:37:07,115 [ 377238]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: IntersectionDisplay context not found.  Call setIntersectionContext first. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:37:08,175 [ 378298]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: Loading changes  for b5c02dcb-37b0-4b0a-98d7-d2bbc889b707 retry 0:  Error: No changes found [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:37:08,180 [ 378303]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: Loading changes  for b5c02dcb-37b0-4b0a-98d7-d2bbc889b707 retry 1:  Error: No changes found [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:37:08,184 [ 378307]   WARN - #com.augmentcode.intellij.chat.ChatWebviewMessageClient$Companion - Workspace guidelines are empty, not including in chat request. 
2025-09-23 09:37:08,680 [ 378803]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: Loading changes  for b5c02dcb-37b0-4b0a-98d7-d2bbc889b707 retry 2:  Error: No changes found [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:37:08,683 [ 378806]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: Loading changes  for b5c02dcb-37b0-4b0a-98d7-d2bbc889b707 retry 3:  Error: No changes found [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:37:08,683 [ 378806]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: Loading changes failed for b5c02dcb-37b0-4b0a-98d7-d2bbc889b707:  Error: No changes found [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:37:09,148 [ 379271]   WARN - #c.i.e.wsl - Failed to get WSL mount root for Ubuntu: DOS working directory is expected, but got \\wsl.localhost\Ubuntu\home\oni\audio-transcriber
2025-09-23 09:37:09,185 [ 379308]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: Loading changes  for b5c02dcb-37b0-4b0a-98d7-d2bbc889b707 retry 4:  Error: No changes found [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:37:09,185 [ 379308]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: Loading changes failed for b5c02dcb-37b0-4b0a-98d7-d2bbc889b707:  Error: No changes found [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:37:12,432 [ 382555]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: No exchange with this request ID found. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:37:22,461 [ 392584]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: IntersectionDisplay context not found.  Call setIntersectionContext first. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:37:23,519 [ 393642]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: Loading changes  for 6d3d8589-c9aa-44af-a6b9-82e6f0069194 retry 0:  Error: No changes found [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:37:23,520 [ 393643]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: Loading changes  for 6d3d8589-c9aa-44af-a6b9-82e6f0069194 retry 1:  Error: No changes found [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:37:23,533 [ 393656]   WARN - #com.augmentcode.intellij.chat.ChatWebviewMessageClient$Companion - Workspace guidelines are empty, not including in chat request. 
2025-09-23 09:37:24,023 [ 394146]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: Loading changes  for 6d3d8589-c9aa-44af-a6b9-82e6f0069194 retry 2:  Error: No changes found [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:37:24,024 [ 394147]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: Loading changes  for 6d3d8589-c9aa-44af-a6b9-82e6f0069194 retry 3:  Error: No changes found [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:37:24,024 [ 394147]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: Loading changes failed for 6d3d8589-c9aa-44af-a6b9-82e6f0069194:  Error: No changes found [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:37:24,528 [ 394651]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: Loading changes  for 6d3d8589-c9aa-44af-a6b9-82e6f0069194 retry 4:  Error: No changes found [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:37:24,528 [ 394651]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: Loading changes failed for 6d3d8589-c9aa-44af-a6b9-82e6f0069194:  Error: No changes found [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:37:27,765 [ 397888]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: No exchange with this request ID found. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:37:30,486 [ 400609]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: IntersectionDisplay context not found.  Call setIntersectionContext first. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:37:31,549 [ 401672]   WARN - #com.augmentcode.intellij.chat.ChatWebviewMessageClient$Companion - Workspace guidelines are empty, not including in chat request. 
2025-09-23 09:37:34,625 [ 404748]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: No exchange with this request ID found. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:37:38,519 [ 408642]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: IntersectionDisplay context not found.  Call setIntersectionContext first. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:37:39,597 [ 409720]   WARN - #com.augmentcode.intellij.chat.ChatWebviewMessageClient$Companion - Workspace guidelines are empty, not including in chat request. 
2025-09-23 09:37:42,607 [ 412730]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: No exchange with this request ID found. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:37:44,782 [ 414905]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: IntersectionDisplay context not found.  Call setIntersectionContext first. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:37:58,658 [ 428781]   WARN - #com.augmentcode.intellij.sentry.SentryMetadataCollector - Git ls-files command failed with exit code: 128
2025-09-23 09:38:21,144 [ 451267]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Non of the blobs were indexed, backing off and retrying...
2025-09-23 09:38:28,086 [ 458209]   WARN - #com.augmentcode.intellij.webviews.AbstractWebviewMessagingService - Failed to parse webview message as protobuf.
Exception: Missing type url when parsing: {"type":"can-show-terminal","data":{"command":"npm install -D concurrently"}}
Message: {"type":"async-wrapper","requestId":"d117fd1c-78c3-4407-8c02-75bfd4476dc8","error":null,"baseMsg":{"type":"can-show-terminal","data":{"command":"npm install -D concurrently"}},"destination":"host"}
2025-09-23 09:38:28,088 [ 458211]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_ERROR]: Failed to check if terminal can be shown: Error: Unable to parse webview message: {"type":"async-wrapper","requestId":"d117fd1c-78c3-4407-8c02-75bfd4476dc8","error":null,"baseMsg":{"type":"can-show-terminal","data":{"command":"npm install -D concurrently"}},"destination":"host"} [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:38:30,256 [ 460379]   WARN - #com.augmentcode.intellij.webviews.AbstractWebviewMessagingService - Failed to parse webview message as protobuf.
Exception: Missing type url when parsing: {"type":"can-show-terminal","data":{"command":"npm install -D concurrently"}}
Message: {"type":"async-wrapper","requestId":"7e8b16af-641a-4aef-9ae4-65048161a1bb","error":null,"baseMsg":{"type":"can-show-terminal","data":{"command":"npm install -D concurrently"}},"destination":"host"}
2025-09-23 09:38:30,277 [ 460400]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_ERROR]: Failed to check if terminal can be shown: Error: Unable to parse webview message: {"type":"async-wrapper","requestId":"7e8b16af-641a-4aef-9ae4-65048161a1bb","error":null,"baseMsg":{"type":"can-show-terminal","data":{"command":"npm install -D concurrently"}},"destination":"host"} [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:38:32,644 [ 462767]   WARN - #c.i.e.wsl - Failed to get WSL mount root for Ubuntu: DOS working directory is expected, but got \\wsl.localhost\Ubuntu\home\oni\audio-transcriber
2025-09-23 09:38:37,295 [ 467418]   WARN - #com.augmentcode.intellij.chat.ChatWebviewMessageClient$Companion - Workspace guidelines are empty, not including in chat request. 
2025-09-23 09:38:40,199 [ 470322]   WARN - #c.i.e.wsl - Failed to get WSL mount root for Ubuntu: DOS working directory is expected, but got \\wsl.localhost\Ubuntu\home\oni\audio-transcriber
2025-09-23 09:38:40,773 [ 470896]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: No exchange with this request ID found. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:38:47,234 [ 477357]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: IntersectionDisplay context not found.  Call setIntersectionContext first. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:38:48,374 [ 478497]   WARN - #com.augmentcode.intellij.chat.ChatWebviewMessageClient$Companion - Workspace guidelines are empty, not including in chat request. 
2025-09-23 09:38:51,791 [ 481914]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: No exchange with this request ID found. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:38:55,931 [ 486054]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Non of the blobs were indexed, backing off and retrying...
2025-09-23 09:38:57,713 [ 487836]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: IntersectionDisplay context not found.  Call setIntersectionContext first. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:38:58,801 [ 488924]   WARN - #com.augmentcode.intellij.chat.ChatWebviewMessageClient$Companion - Workspace guidelines are empty, not including in chat request. 
2025-09-23 09:38:58,804 [ 488927]   WARN - #com.augmentcode.intellij.sentry.SentryMetadataCollector - Git ls-files command failed with exit code: 128
2025-09-23 09:39:01,847 [ 491970]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: No exchange with this request ID found. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:39:09,681 [ 499804]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: IntersectionDisplay context not found.  Call setIntersectionContext first. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:39:09,799 [ 499922]   WARN - #com.augmentcode.intellij.chat.ChatWebviewMessageClient$Companion - Workspace guidelines are empty, not including in chat request. 
2025-09-23 09:39:13,629 [ 503752]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: No exchange with this request ID found. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:39:15,357 [ 505480]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: IntersectionDisplay context not found.  Call setIntersectionContext first. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:39:16,432 [ 506555]   WARN - #com.augmentcode.intellij.api.AugmentHttpClient$Companion - Failed to call record-session-events (fa3be8ff-2f5e-4091-8f97-a330e357e4bc): 400 Bad Request
2025-09-23 09:39:16,433 [ 506556]   WARN - #com.augmentcode.intellij.api.AugmentAPI$Companion - Failed to record session events: 400 Bad Request
2025-09-23 09:39:18,236 [ 508359]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Non of the blobs were indexed, backing off and retrying...
2025-09-23 09:39:19,018 [ 509141]   WARN - #c.i.e.wsl - Failed to get WSL mount root for Ubuntu: DOS working directory is expected, but got \\wsl.localhost\Ubuntu\home\oni\audio-transcriber
2025-09-23 09:39:19,422 [ 509545]   WARN - #com.augmentcode.intellij.webviews.AbstractWebviewMessagingService - Failed to parse webview message as protobuf.
Exception: Missing type url when parsing: {"type":"can-show-terminal","data":{"command":"npm run server"}}
Message: {"type":"async-wrapper","requestId":"ee9af1d4-f914-4a60-ac25-1ab14dd2e717","error":null,"baseMsg":{"type":"can-show-terminal","data":{"command":"npm run server"}},"destination":"host"}
2025-09-23 09:39:19,423 [ 509546]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_ERROR]: Failed to check if terminal can be shown: Error: Unable to parse webview message: {"type":"async-wrapper","requestId":"ee9af1d4-f914-4a60-ac25-1ab14dd2e717","error":null,"baseMsg":{"type":"can-show-terminal","data":{"command":"npm run server"}},"destination":"host"} [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:39:20,631 [ 510754]   WARN - #com.augmentcode.intellij.webviews.AbstractWebviewMessagingService - Failed to parse webview message as protobuf.
Exception: Missing type url when parsing: {"type":"can-show-terminal","data":{"command":"npm run server"}}
Message: {"type":"async-wrapper","requestId":"016eeed7-fb12-409e-bb5b-70a6c3183929","error":null,"baseMsg":{"type":"can-show-terminal","data":{"command":"npm run server"}},"destination":"host"}
2025-09-23 09:39:20,664 [ 510787]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_ERROR]: Failed to check if terminal can be shown: Error: Unable to parse webview message: {"type":"async-wrapper","requestId":"016eeed7-fb12-409e-bb5b-70a6c3183929","error":null,"baseMsg":{"type":"can-show-terminal","data":{"command":"npm run server"}},"destination":"host"} [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:39:20,717 [ 510840]   WARN - #com.augmentcode.intellij.chat.ChatWebviewMessageClient$Companion - Workspace guidelines are empty, not including in chat request. 
2025-09-23 09:39:21,877 [ 512000]   WARN - #c.i.e.wsl - Failed to get WSL mount root for Ubuntu: DOS working directory is expected, but got \\wsl.localhost\Ubuntu\home\oni\audio-transcriber
2025-09-23 09:39:22,595 [ 512718]   WARN - #c.i.e.wsl - Failed to get WSL mount root for Ubuntu: DOS working directory is expected, but got \\wsl.localhost\Ubuntu\home\oni\audio-transcriber
2025-09-23 09:39:23,028 [ 513151]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 79aaf534e95cd53da8534afe6b9ff3318f93499a1ad6ccc45ebd6b47a3ea67e4 -> 008532cc34ed496447878e40aa19e11159814bd1dd06eeccbc20caf0f6d3da2b
2025-09-23 09:39:27,210 [ 517333]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: No exchange with this request ID found. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:39:27,285 [ 517408]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: IntersectionDisplay context not found.  Call setIntersectionContext first. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:39:27,363 [ 517486]   WARN - #com.augmentcode.intellij.chat.ChatWebviewMessageClient$Companion - Workspace guidelines are empty, not including in chat request. 
2025-09-23 09:39:27,561 [ 517684]   WARN - #com.augmentcode.intellij.webviews.AbstractWebviewMessagingService - Failed to parse webview message as protobuf.
Exception: Missing type url when parsing: {"type":"can-show-terminal","data":{"terminalId":8}}
Message: {"type":"async-wrapper","requestId":"09f840f5-60a8-4a71-8817-3fdd5b7cd29d","error":null,"baseMsg":{"type":"can-show-terminal","data":{"terminalId":8}},"destination":"host"}
2025-09-23 09:39:27,562 [ 517685]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_ERROR]: Failed to check if terminal can be shown: Error: Unable to parse webview message: {"type":"async-wrapper","requestId":"09f840f5-60a8-4a71-8817-3fdd5b7cd29d","error":null,"baseMsg":{"type":"can-show-terminal","data":{"terminalId":8}},"destination":"host"} [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:39:31,022 [ 521145]   WARN - #c.i.e.wsl - Failed to get WSL mount root for Ubuntu: DOS working directory is expected, but got \\wsl.localhost\Ubuntu\home\oni\audio-transcriber
2025-09-23 09:39:31,081 [ 521204]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: No exchange with this request ID found. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:39:32,579 [ 522702]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: IntersectionDisplay context not found.  Call setIntersectionContext first. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:39:33,053 [ 523176]   WARN - #com.augmentcode.intellij.webviews.AbstractWebviewMessagingService - Failed to parse webview message as protobuf.
Exception: Missing type url when parsing: {"type":"can-show-terminal","data":{"command":"curl http://localhost:5000/api/health"}}
Message: {"type":"async-wrapper","requestId":"995f1af0-f354-4e7d-88e0-610d79b1f03a","error":null,"baseMsg":{"type":"can-show-terminal","data":{"command":"curl http://localhost:5000/api/health"}},"destination":"host"}
2025-09-23 09:39:33,055 [ 523178]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_ERROR]: Failed to check if terminal can be shown: Error: Unable to parse webview message: {"type":"async-wrapper","requestId":"995f1af0-f354-4e7d-88e0-610d79b1f03a","error":null,"baseMsg":{"type":"can-show-terminal","data":{"command":"curl http://localhost:5000/api/health"}},"destination":"host"} [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:39:35,518 [ 525641]   WARN - #com.augmentcode.intellij.webviews.AbstractWebviewMessagingService - Failed to parse webview message as protobuf.
Exception: Missing type url when parsing: {"type":"can-show-terminal","data":{"command":"curl http://localhost:5000/api/health"}}
Message: {"type":"async-wrapper","requestId":"a0e8219f-02f8-48ba-bcd4-2c2f85c21f7c","error":null,"baseMsg":{"type":"can-show-terminal","data":{"command":"curl http://localhost:5000/api/health"}},"destination":"host"}
2025-09-23 09:39:35,542 [ 525665]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_ERROR]: Failed to check if terminal can be shown: Error: Unable to parse webview message: {"type":"async-wrapper","requestId":"a0e8219f-02f8-48ba-bcd4-2c2f85c21f7c","error":null,"baseMsg":{"type":"can-show-terminal","data":{"command":"curl http://localhost:5000/api/health"}},"destination":"host"} [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:39:35,603 [ 525726]   WARN - #com.augmentcode.intellij.chat.ChatWebviewMessageClient$Companion - Workspace guidelines are empty, not including in chat request. 
2025-09-23 09:39:36,209 [ 526332]   WARN - #c.i.e.wsl - Failed to get WSL mount root for Ubuntu: DOS working directory is expected, but got \\wsl.localhost\Ubuntu\home\oni\audio-transcriber
2025-09-23 09:39:38,473 [ 528596]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: No exchange with this request ID found. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:39:39,929 [ 530052]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: IntersectionDisplay context not found.  Call setIntersectionContext first. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:39:39,996 [ 530119]   WARN - #com.augmentcode.intellij.chat.ChatWebviewMessageClient$Companion - Workspace guidelines are empty, not including in chat request. 
2025-09-23 09:39:43,230 [ 533353]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: No exchange with this request ID found. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:39:43,494 [ 533617]   WARN - #c.i.e.wsl - Failed to get WSL mount root for Ubuntu: DOS working directory is expected, but got \\wsl.localhost\Ubuntu\home\oni\audio-transcriber
2025-09-23 09:39:43,786 [ 533909]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: IntersectionDisplay context not found.  Call setIntersectionContext first. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:39:43,833 [ 533956]   WARN - #com.augmentcode.intellij.chat.ChatWebviewMessageClient$Companion - Workspace guidelines are empty, not including in chat request. 
2025-09-23 09:39:44,100 [ 534223]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Non of the blobs were indexed, backing off and retrying...
2025-09-23 09:39:47,384 [ 537507]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: No exchange with this request ID found. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:39:48,849 [ 538972]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: IntersectionDisplay context not found.  Call setIntersectionContext first. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:39:50,852 [ 540975]   WARN - #c.i.e.wsl - Failed to get WSL mount root for Ubuntu: DOS working directory is expected, but got \\wsl.localhost\Ubuntu\home\oni\audio-transcriber
2025-09-23 09:39:51,021 [ 541144]   WARN - #com.augmentcode.intellij.webviews.AbstractWebviewMessagingService - Failed to parse webview message as protobuf.
Exception: Missing type url when parsing: {"type":"can-show-terminal","data":{"command":"node server/server.js"}}
Message: {"type":"async-wrapper","requestId":"d1a7283d-3fa4-41c5-88ba-dd6e26f4249e","error":null,"baseMsg":{"type":"can-show-terminal","data":{"command":"node server/server.js"}},"destination":"host"}
2025-09-23 09:39:51,023 [ 541146]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_ERROR]: Failed to check if terminal can be shown: Error: Unable to parse webview message: {"type":"async-wrapper","requestId":"d1a7283d-3fa4-41c5-88ba-dd6e26f4249e","error":null,"baseMsg":{"type":"can-show-terminal","data":{"command":"node server/server.js"}},"destination":"host"} [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:39:52,121 [ 542244]   WARN - #com.augmentcode.intellij.webviews.AbstractWebviewMessagingService - Failed to parse webview message as protobuf.
Exception: Missing type url when parsing: {"type":"can-show-terminal","data":{"command":"node server/server.js"}}
Message: {"type":"async-wrapper","requestId":"f84dea0b-c784-4889-9e44-f218432fbc16","error":null,"baseMsg":{"type":"can-show-terminal","data":{"command":"node server/server.js"}},"destination":"host"}
2025-09-23 09:39:52,142 [ 542265]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_ERROR]: Failed to check if terminal can be shown: Error: Unable to parse webview message: {"type":"async-wrapper","requestId":"f84dea0b-c784-4889-9e44-f218432fbc16","error":null,"baseMsg":{"type":"can-show-terminal","data":{"command":"node server/server.js"}},"destination":"host"} [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:39:52,202 [ 542325]   WARN - #com.augmentcode.intellij.chat.ChatWebviewMessageClient$Companion - Workspace guidelines are empty, not including in chat request. 
2025-09-23 09:39:52,392 [ 542515]   WARN - #com.augmentcode.intellij.webviews.AbstractWebviewMessagingService - Failed to parse webview message as protobuf.
Exception: Missing type url when parsing: {"type":"can-show-terminal","data":{"terminalId":8}}
Message: {"type":"async-wrapper","requestId":"3d5189af-d2c2-4cc0-85dd-670dee50600d","error":null,"baseMsg":{"type":"can-show-terminal","data":{"terminalId":8}},"destination":"host"}
2025-09-23 09:39:52,394 [ 542517]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_ERROR]: Failed to check if terminal can be shown: Error: Unable to parse webview message: {"type":"async-wrapper","requestId":"3d5189af-d2c2-4cc0-85dd-670dee50600d","error":null,"baseMsg":{"type":"can-show-terminal","data":{"terminalId":8}},"destination":"host"} [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:39:53,092 [ 543215]   WARN - #c.i.e.wsl - Failed to get WSL mount root for Ubuntu: DOS working directory is expected, but got \\wsl.localhost\Ubuntu\home\oni\audio-transcriber
2025-09-23 09:39:55,759 [ 545882]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: No exchange with this request ID found. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:39:56,179 [ 546302]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: IntersectionDisplay context not found.  Call setIntersectionContext first. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:39:56,228 [ 546351]   WARN - #com.augmentcode.intellij.chat.ChatWebviewMessageClient$Companion - Workspace guidelines are empty, not including in chat request. 
2025-09-23 09:39:56,459 [ 546582]   WARN - #com.augmentcode.intellij.webviews.AbstractWebviewMessagingService - Failed to parse webview message as protobuf.
Exception: Missing type url when parsing: {"type":"can-show-terminal","data":{"terminalId":10}}
Message: {"type":"async-wrapper","requestId":"c2c236b9-ee9d-4563-833f-b4b98c87909c","error":null,"baseMsg":{"type":"can-show-terminal","data":{"terminalId":10}},"destination":"host"}
2025-09-23 09:39:56,461 [ 546584]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_ERROR]: Failed to check if terminal can be shown: Error: Unable to parse webview message: {"type":"async-wrapper","requestId":"c2c236b9-ee9d-4563-833f-b4b98c87909c","error":null,"baseMsg":{"type":"can-show-terminal","data":{"terminalId":10}},"destination":"host"} [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:39:57,455 [ 547578]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 7520c862ab9d9cdb64bccca2a6b330336f94780405ba0948466f53493a57c4f5 -> 1c66770908e5ab077fb95b030438c16482da22eda565a8c4da931c38c754f2e0
2025-09-23 09:39:58,955 [ 549078]   WARN - #com.augmentcode.intellij.sentry.SentryMetadataCollector - Git ls-files command failed with exit code: 128
2025-09-23 09:40:00,098 [ 550221]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: No exchange with this request ID found. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:40:00,198 [ 550321]   WARN - #c.i.e.wsl - Failed to get WSL mount root for Ubuntu: DOS working directory is expected, but got \\wsl.localhost\Ubuntu\home\oni\audio-transcriber
2025-09-23 09:40:00,933 [ 551056]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Non of the blobs were indexed, backing off and retrying...
2025-09-23 09:40:01,472 [ 551595]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: IntersectionDisplay context not found.  Call setIntersectionContext first. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:40:01,757 [ 551880]   WARN - #com.augmentcode.intellij.webviews.AbstractWebviewMessagingService - Failed to parse webview message as protobuf.
Exception: Missing type url when parsing: {"type":"can-show-terminal","data":{"command":"curl -X GET http://localhost:5000/api/health"}}
Message: {"type":"async-wrapper","requestId":"3cf562dd-b6c6-4a29-84f5-dc030b7c43ec","error":null,"baseMsg":{"type":"can-show-terminal","data":{"command":"curl -X GET http://localhost:5000/api/health"}},"destination":"host"}
2025-09-23 09:40:01,759 [ 551882]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_ERROR]: Failed to check if terminal can be shown: Error: Unable to parse webview message: {"type":"async-wrapper","requestId":"3cf562dd-b6c6-4a29-84f5-dc030b7c43ec","error":null,"baseMsg":{"type":"can-show-terminal","data":{"command":"curl -X GET http://localhost:5000/api/health"}},"destination":"host"} [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:40:03,254 [ 553377]   WARN - #com.augmentcode.intellij.webviews.AbstractWebviewMessagingService - Failed to parse webview message as protobuf.
Exception: Missing type url when parsing: {"type":"can-show-terminal","data":{"command":"curl -X GET http://localhost:5000/api/health"}}
Message: {"type":"async-wrapper","requestId":"5c7730f7-41cf-4904-842e-ae945cfdc513","error":null,"baseMsg":{"type":"can-show-terminal","data":{"command":"curl -X GET http://localhost:5000/api/health"}},"destination":"host"}
2025-09-23 09:40:03,257 [ 553380]   WARN - #c.i.o.progress - Suppressed a frequent exception logged for the 10th time: This method is forbidden on EDT because it does not pump the event queue. Switch to a BGT, or use com.intellij.openapi.progress.TasksKt.runWithModalProgressBlocking. 
2025-09-23 09:40:03,257 [ 553380]   WARN - #c.i.o.progress - Suppressed a frequent exception logged for the 10th time: This method is forbidden on EDT because it does not pump the event queue. Switch to a BGT, or use com.intellij.openapi.progress.TasksKt.runWithModalProgressBlocking. 
2025-09-23 09:40:03,259 [ 553382]   WARN - #c.i.o.progress - Suppressed a frequent exception logged for the 10th time: This method is forbidden on EDT because it does not pump the event queue. Switch to a BGT, or use com.intellij.openapi.progress.TasksKt.runWithModalProgressBlocking. 
2025-09-23 09:40:03,259 [ 553382]   WARN - #c.i.o.progress - Suppressed a frequent exception logged for the 10th time: This method is forbidden on EDT because it does not pump the event queue. Switch to a BGT, or use com.intellij.openapi.progress.TasksKt.runWithModalProgressBlocking. 
2025-09-23 09:40:03,260 [ 553383]   WARN - #c.i.o.progress - Suppressed a frequent exception logged for the 10th time: This method is forbidden on EDT because it does not pump the event queue. Switch to a BGT, or use com.intellij.openapi.progress.TasksKt.runWithModalProgressBlocking. 
2025-09-23 09:40:03,274 [ 553397]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_ERROR]: Failed to check if terminal can be shown: Error: Unable to parse webview message: {"type":"async-wrapper","requestId":"5c7730f7-41cf-4904-842e-ae945cfdc513","error":null,"baseMsg":{"type":"can-show-terminal","data":{"command":"curl -X GET http://localhost:5000/api/health"}},"destination":"host"} [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:40:03,337 [ 553460]   WARN - #com.augmentcode.intellij.chat.ChatWebviewMessageClient$Companion - Workspace guidelines are empty, not including in chat request. 
2025-09-23 09:40:03,935 [ 554058]   WARN - #c.i.e.wsl - Failed to get WSL mount root for Ubuntu: DOS working directory is expected, but got \\wsl.localhost\Ubuntu\home\oni\audio-transcriber
2025-09-23 09:40:04,253 [ 554376]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 58a4c2d828dca4e2c72d2bc6631364af18b5d8817e1009796be56d67017ed298 -> cd7d80875934c274364b4bb38b2fd3dfd56a4a2c662aea8b2037beb8a53ad158
2025-09-23 09:40:06,366 [ 556489]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: No exchange with this request ID found. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:40:08,270 [ 558393]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: IntersectionDisplay context not found.  Call setIntersectionContext first. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:40:08,346 [ 558469]   WARN - #com.augmentcode.intellij.chat.ChatWebviewMessageClient$Companion - Workspace guidelines are empty, not including in chat request. 
2025-09-23 09:40:08,856 [ 558979]   WARN - #c.i.e.wsl - Failed to get WSL mount root for Ubuntu: DOS working directory is expected, but got \\wsl.localhost\Ubuntu\home\oni\audio-transcriber
2025-09-23 09:40:11,985 [ 562108]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: No exchange with this request ID found. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:40:15,860 [ 565983]   WARN - #c.i.e.wsl - Failed to get WSL mount root for Ubuntu: DOS working directory is expected, but got \\wsl.localhost\Ubuntu\home\oni\audio-transcriber
2025-09-23 09:40:16,317 [ 566440]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (cd7d80875934c274364b4bb38b2fd3dfd56a4a2c662aea8b2037beb8a53ad158) is unknown but expected to be uploaded
2025-09-23 09:40:16,317 [ 566440]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (cd7d80875934c274364b4bb38b2fd3dfd56a4a2c662aea8b2037beb8a53ad158) is unknown but expected to be uploaded
2025-09-23 09:40:16,936 [ 567059]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: cd7d80875934c274364b4bb38b2fd3dfd56a4a2c662aea8b2037beb8a53ad158 -> 81cdc7ada2b005fd5e289965f7482906cb35f40e4112ba6be455726e1711164b
2025-09-23 09:40:17,410 [ 567533]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: cd7d80875934c274364b4bb38b2fd3dfd56a4a2c662aea8b2037beb8a53ad158 -> 81cdc7ada2b005fd5e289965f7482906cb35f40e4112ba6be455726e1711164b
2025-09-23 09:40:18,232 [ 568355]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: IntersectionDisplay context not found.  Call setIntersectionContext first. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:40:18,549 [ 568672]   WARN - #c.i.e.wsl - Failed to get WSL mount root for Ubuntu: DOS working directory is expected, but got \\wsl.localhost\Ubuntu\home\oni\audio-transcriber
2025-09-23 09:40:19,283 [ 569406]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: Loading changes  for 1e6ef5b8-91b1-46e4-9859-40dbe7d4bc6c retry 0:  Error: No changes found [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:40:19,283 [ 569406]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: Loading changes  for 1e6ef5b8-91b1-46e4-9859-40dbe7d4bc6c retry 1:  Error: No changes found [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:40:19,295 [ 569418]   WARN - #com.augmentcode.intellij.chat.ChatWebviewMessageClient$Companion - Workspace guidelines are empty, not including in chat request. 
2025-09-23 09:40:19,768 [ 569891]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 81cdc7ada2b005fd5e289965f7482906cb35f40e4112ba6be455726e1711164b -> c6d2c67b11f72e27cd2a3cf26af526e9c4f91f80c9ba77f27386549d737bb151
2025-09-23 09:40:19,788 [ 569911]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: Loading changes  for 1e6ef5b8-91b1-46e4-9859-40dbe7d4bc6c retry 2:  Error: No changes found [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:40:19,788 [ 569911]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: Loading changes  for 1e6ef5b8-91b1-46e4-9859-40dbe7d4bc6c retry 3:  Error: No changes found [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:40:19,789 [ 569912]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: Loading changes failed for 1e6ef5b8-91b1-46e4-9859-40dbe7d4bc6c:  Error: No changes found [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:40:20,292 [ 570415]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: Loading changes  for 1e6ef5b8-91b1-46e4-9859-40dbe7d4bc6c retry 4:  Error: No changes found [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:40:20,292 [ 570415]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: Loading changes failed for 1e6ef5b8-91b1-46e4-9859-40dbe7d4bc6c:  Error: No changes found [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:40:22,534 [ 572657]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: No exchange with this request ID found. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:40:22,607 [ 572730]   WARN - #c.i.e.wsl - Failed to get WSL mount root for Ubuntu: DOS working directory is expected, but got \\wsl.localhost\Ubuntu\home\oni\audio-transcriber
2025-09-23 09:40:23,389 [ 573512]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: c6d2c67b11f72e27cd2a3cf26af526e9c4f91f80c9ba77f27386549d737bb151 -> a5f63ed1b802e58f122627aeb95e75b948543cec5b041f4afc887dc718940113
2025-09-23 09:40:23,945 [ 574068]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: IntersectionDisplay context not found.  Call setIntersectionContext first. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:40:24,327 [ 574450]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: IntersectionDisplay context not found.  Call setIntersectionContext first. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:40:26,053 [ 576176]   WARN - #com.augmentcode.intellij.webviews.AbstractWebviewMessagingService - Failed to parse webview message as protobuf.
Exception: Missing type url when parsing: {"type":"can-show-terminal","data":{"command":"node server/server.js"}}
Message: {"type":"async-wrapper","requestId":"2e99f7e6-4f69-4ca2-a4a6-7281d3b30298","error":null,"baseMsg":{"type":"can-show-terminal","data":{"command":"node server/server.js"}},"destination":"host"}
2025-09-23 09:40:26,054 [ 576177]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_ERROR]: Failed to check if terminal can be shown: Error: Unable to parse webview message: {"type":"async-wrapper","requestId":"2e99f7e6-4f69-4ca2-a4a6-7281d3b30298","error":null,"baseMsg":{"type":"can-show-terminal","data":{"command":"node server/server.js"}},"destination":"host"} [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:40:26,118 [ 576241]   WARN - #com.augmentcode.intellij.webviews.AbstractWebviewMessagingService - Failed to parse webview message as protobuf.
Exception: Missing type url when parsing: {"type":"can-show-terminal","data":{"terminalId":10}}
Message: {"type":"async-wrapper","requestId":"ca780589-1fb5-4166-8b7c-aee6e21755c3","error":null,"baseMsg":{"type":"can-show-terminal","data":{"terminalId":10}},"destination":"host"}
2025-09-23 09:40:26,119 [ 576242]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_ERROR]: Failed to check if terminal can be shown: Error: Unable to parse webview message: {"type":"async-wrapper","requestId":"ca780589-1fb5-4166-8b7c-aee6e21755c3","error":null,"baseMsg":{"type":"can-show-terminal","data":{"terminalId":10}},"destination":"host"} [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:40:26,824 [ 576947]   WARN - #com.augmentcode.intellij.webviews.AbstractWebviewMessagingService - Failed to parse webview message as protobuf.
Exception: Missing type url when parsing: {"type":"can-show-terminal","data":{"command":"node server/server.js"}}
Message: {"type":"async-wrapper","requestId":"6cfc7927-0799-43dc-9287-42f45d02013c","error":null,"baseMsg":{"type":"can-show-terminal","data":{"command":"node server/server.js"}},"destination":"host"}
2025-09-23 09:40:26,857 [ 576980]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_ERROR]: Failed to check if terminal can be shown: Error: Unable to parse webview message: {"type":"async-wrapper","requestId":"6cfc7927-0799-43dc-9287-42f45d02013c","error":null,"baseMsg":{"type":"can-show-terminal","data":{"command":"node server/server.js"}},"destination":"host"} [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:40:26,862 [ 576985]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (81cdc7ada2b005fd5e289965f7482906cb35f40e4112ba6be455726e1711164b) is unknown but expected to be uploaded
2025-09-23 09:40:26,862 [ 576985]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (81cdc7ada2b005fd5e289965f7482906cb35f40e4112ba6be455726e1711164b) is unknown but expected to be uploaded
2025-09-23 09:40:26,921 [ 577044]   WARN - #com.augmentcode.intellij.chat.ChatWebviewMessageClient$Companion - Workspace guidelines are empty, not including in chat request. 
2025-09-23 09:40:27,432 [ 577555]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 81cdc7ada2b005fd5e289965f7482906cb35f40e4112ba6be455726e1711164b -> a5f63ed1b802e58f122627aeb95e75b948543cec5b041f4afc887dc718940113
2025-09-23 09:40:27,661 [ 577784]   WARN - #c.i.e.wsl - Failed to get WSL mount root for Ubuntu: DOS working directory is expected, but got \\wsl.localhost\Ubuntu\home\oni\audio-transcriber
2025-09-23 09:40:30,505 [ 580628]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: No exchange with this request ID found. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:40:30,959 [ 581082]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: IntersectionDisplay context not found.  Call setIntersectionContext first. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:40:31,039 [ 581162]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 81cdc7ada2b005fd5e289965f7482906cb35f40e4112ba6be455726e1711164b -> 50e90b21bd026e053b0f279f03931ea6d13bae2fa913098c8e9da3bf07ebb6bd
2025-09-23 09:40:31,118 [ 581241]   WARN - #com.augmentcode.intellij.chat.ChatWebviewMessageClient$Companion - Workspace guidelines are empty, not including in chat request. 
2025-09-23 09:40:31,226 [ 581349]   WARN - #com.augmentcode.intellij.webviews.AbstractWebviewMessagingService - Failed to parse webview message as protobuf.
Exception: Missing type url when parsing: {"type":"can-show-terminal","data":{"terminalId":12}}
Message: {"type":"async-wrapper","requestId":"77ab47ac-3da7-4c70-afa6-225fedd1c695","error":null,"baseMsg":{"type":"can-show-terminal","data":{"terminalId":12}},"destination":"host"}
2025-09-23 09:40:31,228 [ 581351]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_ERROR]: Failed to check if terminal can be shown: Error: Unable to parse webview message: {"type":"async-wrapper","requestId":"77ab47ac-3da7-4c70-afa6-225fedd1c695","error":null,"baseMsg":{"type":"can-show-terminal","data":{"terminalId":12}},"destination":"host"} [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:40:34,505 [ 584628]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (a5f63ed1b802e58f122627aeb95e75b948543cec5b041f4afc887dc718940113) is unknown but expected to be uploaded
2025-09-23 09:40:34,506 [ 584629]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (a5f63ed1b802e58f122627aeb95e75b948543cec5b041f4afc887dc718940113) is unknown but expected to be uploaded
2025-09-23 09:40:34,506 [ 584629]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (a5f63ed1b802e58f122627aeb95e75b948543cec5b041f4afc887dc718940113) is unknown but expected to be uploaded
2025-09-23 09:40:34,506 [ 584629]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (a5f63ed1b802e58f122627aeb95e75b948543cec5b041f4afc887dc718940113) is unknown but expected to be uploaded
2025-09-23 09:40:34,596 [ 584719]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: No exchange with this request ID found. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:40:35,241 [ 585364]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: a5f63ed1b802e58f122627aeb95e75b948543cec5b041f4afc887dc718940113 -> 50e90b21bd026e053b0f279f03931ea6d13bae2fa913098c8e9da3bf07ebb6bd
2025-09-23 09:40:35,682 [ 585805]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: a5f63ed1b802e58f122627aeb95e75b948543cec5b041f4afc887dc718940113 -> 50e90b21bd026e053b0f279f03931ea6d13bae2fa913098c8e9da3bf07ebb6bd
2025-09-23 09:40:35,940 [ 586063]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: IntersectionDisplay context not found.  Call setIntersectionContext first. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:40:36,147 [ 586270]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: a5f63ed1b802e58f122627aeb95e75b948543cec5b041f4afc887dc718940113 -> 50e90b21bd026e053b0f279f03931ea6d13bae2fa913098c8e9da3bf07ebb6bd
2025-09-23 09:40:36,223 [ 586346]   WARN - #com.augmentcode.intellij.webviews.AbstractWebviewMessagingService - Failed to parse webview message as protobuf.
Exception: Missing type url when parsing: {"type":"can-show-terminal","data":{"command":"curl -X GET http://localhost:5000/api/health"}}
Message: {"type":"async-wrapper","requestId":"e83453c8-f306-4172-848f-e0ed1ba9fb49","error":null,"baseMsg":{"type":"can-show-terminal","data":{"command":"curl -X GET http://localhost:5000/api/health"}},"destination":"host"}
2025-09-23 09:40:36,225 [ 586348]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_ERROR]: Failed to check if terminal can be shown: Error: Unable to parse webview message: {"type":"async-wrapper","requestId":"e83453c8-f306-4172-848f-e0ed1ba9fb49","error":null,"baseMsg":{"type":"can-show-terminal","data":{"command":"curl -X GET http://localhost:5000/api/health"}},"destination":"host"} [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:40:38,057 [ 588180]   WARN - #com.augmentcode.intellij.webviews.AbstractWebviewMessagingService - Failed to parse webview message as protobuf.
Exception: Missing type url when parsing: {"type":"can-show-terminal","data":{"command":"curl -X GET http://localhost:5000/api/health"}}
Message: {"type":"async-wrapper","requestId":"3e617949-8ab7-47c7-9723-8866c7ac4e97","error":null,"baseMsg":{"type":"can-show-terminal","data":{"command":"curl -X GET http://localhost:5000/api/health"}},"destination":"host"}
2025-09-23 09:40:38,078 [ 588201]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_ERROR]: Failed to check if terminal can be shown: Error: Unable to parse webview message: {"type":"async-wrapper","requestId":"3e617949-8ab7-47c7-9723-8866c7ac4e97","error":null,"baseMsg":{"type":"can-show-terminal","data":{"command":"curl -X GET http://localhost:5000/api/health"}},"destination":"host"} [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:40:38,163 [ 588286]   WARN - #com.augmentcode.intellij.chat.ChatWebviewMessageClient$Companion - Workspace guidelines are empty, not including in chat request. 
2025-09-23 09:40:38,744 [ 588867]   WARN - #c.i.e.wsl - Failed to get WSL mount root for Ubuntu: DOS working directory is expected, but got \\wsl.localhost\Ubuntu\home\oni\audio-transcriber
2025-09-23 09:40:40,058 [ 590181]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: a5f63ed1b802e58f122627aeb95e75b948543cec5b041f4afc887dc718940113 -> d843b9d79326897927d7e4948692ba265e2d64f3b52c632e25569c4fb2f3cffb
2025-09-23 09:40:41,009 [ 591132]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: No exchange with this request ID found. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:40:42,979 [ 593102]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: IntersectionDisplay context not found.  Call setIntersectionContext first. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:40:43,262 [ 593385]   WARN - #com.augmentcode.intellij.webviews.AbstractWebviewMessagingService - Failed to parse webview message as protobuf.
Exception: Missing type url when parsing: {"type":"can-show-terminal","data":{"command":"lsof -i :5000"}}
Message: {"type":"async-wrapper","requestId":"0bb12b42-4eb6-4ecf-ba68-f66b8e1f185a","error":null,"baseMsg":{"type":"can-show-terminal","data":{"command":"lsof -i :5000"}},"destination":"host"}
2025-09-23 09:40:43,265 [ 593388]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_ERROR]: Failed to check if terminal can be shown: Error: Unable to parse webview message: {"type":"async-wrapper","requestId":"0bb12b42-4eb6-4ecf-ba68-f66b8e1f185a","error":null,"baseMsg":{"type":"can-show-terminal","data":{"command":"lsof -i :5000"}},"destination":"host"} [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:40:47,105 [ 597228]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (d843b9d79326897927d7e4948692ba265e2d64f3b52c632e25569c4fb2f3cffb) is unknown but expected to be uploaded
2025-09-23 09:40:47,105 [ 597228]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (d843b9d79326897927d7e4948692ba265e2d64f3b52c632e25569c4fb2f3cffb) is unknown but expected to be uploaded
2025-09-23 09:40:48,844 [ 598967]   WARN - #c.i.e.wsl - Failed to get WSL mount root for Ubuntu: DOS working directory is expected, but got \\wsl.localhost\Ubuntu\home\oni\audio-transcriber
2025-09-23 09:40:49,839 [ 599962]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: d843b9d79326897927d7e4948692ba265e2d64f3b52c632e25569c4fb2f3cffb -> 9b0464e962f6d23db0597786f533dac1506e31700d5dbada2a87aa1020e6e7e7
2025-09-23 09:40:52,787 [ 602910]   WARN - #c.i.e.wsl - Failed to get WSL mount root for Ubuntu: DOS working directory is expected, but got \\wsl.localhost\Ubuntu\home\oni\audio-transcriber
2025-09-23 09:40:53,441 [ 603564]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 9b0464e962f6d23db0597786f533dac1506e31700d5dbada2a87aa1020e6e7e7 -> af0aa139a64c4bc88572537d38b906f0817af15ff898818f5ae9eb763d5ca40a
2025-09-23 09:40:55,386 [ 605509]   WARN - #com.augmentcode.intellij.webviews.AbstractWebviewMessagingService - Failed to parse webview message as protobuf.
Exception: Missing type url when parsing: {"type":"can-show-terminal","data":{"command":"lsof -i :5000"}}
Message: {"type":"async-wrapper","requestId":"1b012c41-f91a-47fb-bb1f-b4fbb9a28c04","error":null,"baseMsg":{"type":"can-show-terminal","data":{"command":"lsof -i :5000"}},"destination":"host"}
2025-09-23 09:40:55,417 [ 605540]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_ERROR]: Failed to check if terminal can be shown: Error: Unable to parse webview message: {"type":"async-wrapper","requestId":"1b012c41-f91a-47fb-bb1f-b4fbb9a28c04","error":null,"baseMsg":{"type":"can-show-terminal","data":{"command":"lsof -i :5000"}},"destination":"host"} [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:40:55,504 [ 605627]   WARN - #com.augmentcode.intellij.chat.ChatWebviewMessageClient$Companion - Workspace guidelines are empty, not including in chat request. 
2025-09-23 09:40:56,034 [ 606157]   WARN - #c.i.u.i.p.ProjectIndexableFilesFilterHealthCheck - Following files are indexable but they were NOT found in filter. Errors count: 2. Examples:
file id=199049 path=//wsl.localhost/Ubuntu/home/<USER>/audio-transcriber/node_modules/.cache/default-development/0.pack
file id=201627 path=//wsl.localhost/Ubuntu/home/<USER>/audio-transcriber/node_modules/.cache/default-development/1.pack
2025-09-23 09:40:56,092 [ 606215]   WARN - #c.i.e.wsl - Failed to get WSL mount root for Ubuntu: DOS working directory is expected, but got \\wsl.localhost\Ubuntu\home\oni\audio-transcriber
2025-09-23 09:40:59,099 [ 609222]   WARN - #com.augmentcode.intellij.sentry.SentryMetadataCollector - Git ls-files command failed with exit code: 128
2025-09-23 09:40:59,803 [ 609926]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: No exchange with this request ID found. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:41:01,271 [ 611394]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: IntersectionDisplay context not found.  Call setIntersectionContext first. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:41:01,566 [ 611689]   WARN - #com.augmentcode.intellij.webviews.AbstractWebviewMessagingService - Failed to parse webview message as protobuf.
Exception: Missing type url when parsing: {"type":"can-show-terminal","data":{"command":"kill 5360"}}
Message: {"type":"async-wrapper","requestId":"b4eac85b-2b83-4dad-9e4d-966c08b85137","error":null,"baseMsg":{"type":"can-show-terminal","data":{"command":"kill 5360"}},"destination":"host"}
2025-09-23 09:41:01,568 [ 611691]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_ERROR]: Failed to check if terminal can be shown: Error: Unable to parse webview message: {"type":"async-wrapper","requestId":"b4eac85b-2b83-4dad-9e4d-966c08b85137","error":null,"baseMsg":{"type":"can-show-terminal","data":{"command":"kill 5360"}},"destination":"host"} [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:41:03,575 [ 613698]   WARN - #com.augmentcode.intellij.webviews.AbstractWebviewMessagingService - Failed to parse webview message as protobuf.
Exception: Missing type url when parsing: {"type":"can-show-terminal","data":{"command":"kill 5360"}}
Message: {"type":"async-wrapper","requestId":"e9570f40-cf71-4837-b858-0b442f20ca33","error":null,"baseMsg":{"type":"can-show-terminal","data":{"command":"kill 5360"}},"destination":"host"}
2025-09-23 09:41:03,586 [ 613709]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_ERROR]: Failed to check if terminal can be shown: Error: Unable to parse webview message: {"type":"async-wrapper","requestId":"e9570f40-cf71-4837-b858-0b442f20ca33","error":null,"baseMsg":{"type":"can-show-terminal","data":{"command":"kill 5360"}},"destination":"host"} [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:41:03,622 [ 613745]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_ERROR]: _validateAndCheckTool called while another tool is active [object Object] [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:41:03,670 [ 613793]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: IntersectionDisplay context not found.  Call setIntersectionContext first. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:41:03,848 [ 613971]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (af0aa139a64c4bc88572537d38b906f0817af15ff898818f5ae9eb763d5ca40a) is unknown but expected to be uploaded
2025-09-23 09:41:03,848 [ 613971]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (af0aa139a64c4bc88572537d38b906f0817af15ff898818f5ae9eb763d5ca40a) is unknown but expected to be uploaded
2025-09-23 09:41:03,958 [ 614081]   WARN - #com.augmentcode.intellij.webviews.AbstractWebviewMessagingService - Failed to parse webview message as protobuf.
Exception: Missing type url when parsing: {"type":"can-show-terminal","data":{"command":"node server/server.js"}}
Message: {"type":"async-wrapper","requestId":"4433e1aa-cb88-40f5-a74f-a7b34ac790e7","error":null,"baseMsg":{"type":"can-show-terminal","data":{"command":"node server/server.js"}},"destination":"host"}
2025-09-23 09:41:03,959 [ 614082]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_ERROR]: Failed to check if terminal can be shown: Error: Unable to parse webview message: {"type":"async-wrapper","requestId":"4433e1aa-cb88-40f5-a74f-a7b34ac790e7","error":null,"baseMsg":{"type":"can-show-terminal","data":{"command":"node server/server.js"}},"destination":"host"} [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:41:04,262 [ 614385]   WARN - #c.i.e.wsl - Failed to get WSL mount root for Ubuntu: DOS working directory is expected, but got \\wsl.localhost\Ubuntu\home\oni\audio-transcriber
2025-09-23 09:41:04,279 [ 614402]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: af0aa139a64c4bc88572537d38b906f0817af15ff898818f5ae9eb763d5ca40a -> 841b7c0c70a9a63e659ae69141fa06398bb71206ca035020cafb73c9cb654932
2025-09-23 09:41:05,946 [ 616069]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: af0aa139a64c4bc88572537d38b906f0817af15ff898818f5ae9eb763d5ca40a -> 75328b481f5ef5f69276fd16be2c631d8676b55b69e16e5fe02fe44004c456de
2025-09-23 09:41:09,418 [ 619541]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (841b7c0c70a9a63e659ae69141fa06398bb71206ca035020cafb73c9cb654932) is unknown but expected to be uploaded
2025-09-23 09:41:09,418 [ 619541]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (841b7c0c70a9a63e659ae69141fa06398bb71206ca035020cafb73c9cb654932) is unknown but expected to be uploaded
2025-09-23 09:41:09,418 [ 619541]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (841b7c0c70a9a63e659ae69141fa06398bb71206ca035020cafb73c9cb654932) is unknown but expected to be uploaded
2025-09-23 09:41:09,987 [ 620110]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 841b7c0c70a9a63e659ae69141fa06398bb71206ca035020cafb73c9cb654932 -> 75328b481f5ef5f69276fd16be2c631d8676b55b69e16e5fe02fe44004c456de
2025-09-23 09:41:10,433 [ 620556]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 841b7c0c70a9a63e659ae69141fa06398bb71206ca035020cafb73c9cb654932 -> 75328b481f5ef5f69276fd16be2c631d8676b55b69e16e5fe02fe44004c456de
2025-09-23 09:41:13,569 [ 623692]   WARN - #c.i.e.wsl - Failed to get WSL mount root for Ubuntu: DOS working directory is expected, but got \\wsl.localhost\Ubuntu\home\oni\audio-transcriber
2025-09-23 09:41:13,828 [ 623951]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (75328b481f5ef5f69276fd16be2c631d8676b55b69e16e5fe02fe44004c456de) is unknown but expected to be uploaded
2025-09-23 09:41:13,828 [ 623951]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (75328b481f5ef5f69276fd16be2c631d8676b55b69e16e5fe02fe44004c456de) is unknown but expected to be uploaded
2025-09-23 09:41:13,828 [ 623951]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (75328b481f5ef5f69276fd16be2c631d8676b55b69e16e5fe02fe44004c456de) is unknown but expected to be uploaded
2025-09-23 09:41:13,828 [ 623951]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (75328b481f5ef5f69276fd16be2c631d8676b55b69e16e5fe02fe44004c456de) is unknown but expected to be uploaded
2025-09-23 09:41:14,053 [ 624176]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 841b7c0c70a9a63e659ae69141fa06398bb71206ca035020cafb73c9cb654932 -> fb360b5251b57fc0cde66730aaf901124d2507aa7c242d24eff964241ef1bcde
2025-09-23 09:41:14,661 [ 624784]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 75328b481f5ef5f69276fd16be2c631d8676b55b69e16e5fe02fe44004c456de -> fb360b5251b57fc0cde66730aaf901124d2507aa7c242d24eff964241ef1bcde
2025-09-23 09:41:15,115 [ 625238]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 75328b481f5ef5f69276fd16be2c631d8676b55b69e16e5fe02fe44004c456de -> fb360b5251b57fc0cde66730aaf901124d2507aa7c242d24eff964241ef1bcde
2025-09-23 09:41:16,121 [ 626244]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 75328b481f5ef5f69276fd16be2c631d8676b55b69e16e5fe02fe44004c456de -> fb360b5251b57fc0cde66730aaf901124d2507aa7c242d24eff964241ef1bcde
2025-09-23 09:41:19,690 [ 629813]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 75328b481f5ef5f69276fd16be2c631d8676b55b69e16e5fe02fe44004c456de -> fb360b5251b57fc0cde66730aaf901124d2507aa7c242d24eff964241ef1bcde
2025-09-23 09:41:25,705 [ 635828]   WARN - #c.i.e.wsl - Failed to get WSL mount root for Ubuntu: DOS working directory is expected, but got \\wsl.localhost\Ubuntu\home\oni\audio-transcriber
2025-09-23 09:41:26,602 [ 636725]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (fb360b5251b57fc0cde66730aaf901124d2507aa7c242d24eff964241ef1bcde) is unknown but expected to be uploaded
2025-09-23 09:41:26,602 [ 636725]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (fb360b5251b57fc0cde66730aaf901124d2507aa7c242d24eff964241ef1bcde) is unknown but expected to be uploaded
2025-09-23 09:41:26,602 [ 636725]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (fb360b5251b57fc0cde66730aaf901124d2507aa7c242d24eff964241ef1bcde) is unknown but expected to be uploaded
2025-09-23 09:41:26,602 [ 636725]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (fb360b5251b57fc0cde66730aaf901124d2507aa7c242d24eff964241ef1bcde) is unknown but expected to be uploaded
2025-09-23 09:41:26,602 [ 636725]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (fb360b5251b57fc0cde66730aaf901124d2507aa7c242d24eff964241ef1bcde) is unknown but expected to be uploaded
2025-09-23 09:41:26,602 [ 636725]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (fb360b5251b57fc0cde66730aaf901124d2507aa7c242d24eff964241ef1bcde) is unknown but expected to be uploaded
2025-09-23 09:41:26,789 [ 636912]   WARN - #com.augmentcode.intellij.webviews.AbstractWebviewMessagingService - Failed to parse webview message as protobuf.
Exception: Missing type url when parsing: {"type":"can-show-terminal","data":{"command":"node server/server.js"}}
Message: {"type":"async-wrapper","requestId":"0c13729f-6835-4da2-8a02-360614a084bf","error":null,"baseMsg":{"type":"can-show-terminal","data":{"command":"node server/server.js"}},"destination":"host"}
2025-09-23 09:41:26,816 [ 636939]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_ERROR]: Failed to check if terminal can be shown: Error: Unable to parse webview message: {"type":"async-wrapper","requestId":"0c13729f-6835-4da2-8a02-360614a084bf","error":null,"baseMsg":{"type":"can-show-terminal","data":{"command":"node server/server.js"}},"destination":"host"} [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:41:26,866 [ 636989]   WARN - #com.augmentcode.intellij.chat.ChatWebviewMessageClient$Companion - Workspace guidelines are empty, not including in chat request. 
2025-09-23 09:41:27,333 [ 637456]   WARN - #c.i.e.wsl - Failed to get WSL mount root for Ubuntu: DOS working directory is expected, but got \\wsl.localhost\Ubuntu\home\oni\audio-transcriber
2025-09-23 09:41:27,846 [ 637969]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: fb360b5251b57fc0cde66730aaf901124d2507aa7c242d24eff964241ef1bcde -> a68bf4c6aa44940f448f4c63c22ebdff0d5fbee5c16c891280c6b13e5d888130
2025-09-23 09:41:28,325 [ 638448]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: fb360b5251b57fc0cde66730aaf901124d2507aa7c242d24eff964241ef1bcde -> a68bf4c6aa44940f448f4c63c22ebdff0d5fbee5c16c891280c6b13e5d888130
2025-09-23 09:41:28,845 [ 638968]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: fb360b5251b57fc0cde66730aaf901124d2507aa7c242d24eff964241ef1bcde -> a68bf4c6aa44940f448f4c63c22ebdff0d5fbee5c16c891280c6b13e5d888130
2025-09-23 09:41:29,294 [ 639417]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: fb360b5251b57fc0cde66730aaf901124d2507aa7c242d24eff964241ef1bcde -> a68bf4c6aa44940f448f4c63c22ebdff0d5fbee5c16c891280c6b13e5d888130
2025-09-23 09:41:29,722 [ 639845]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: fb360b5251b57fc0cde66730aaf901124d2507aa7c242d24eff964241ef1bcde -> a68bf4c6aa44940f448f4c63c22ebdff0d5fbee5c16c891280c6b13e5d888130
2025-09-23 09:41:30,906 [ 641029]   WARN - #c.i.e.wsl - Failed to get WSL mount root for Ubuntu: DOS working directory is expected, but got \\wsl.localhost\Ubuntu\home\oni\audio-transcriber
2025-09-23 09:41:31,734 [ 641857]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: No exchange with this request ID found. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:41:31,776 [ 641899]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: IntersectionDisplay context not found.  Call setIntersectionContext first. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:41:31,835 [ 641958]   WARN - #com.augmentcode.intellij.chat.ChatWebviewMessageClient$Companion - Workspace guidelines are empty, not including in chat request. 
2025-09-23 09:41:32,075 [ 642198]   WARN - #com.augmentcode.intellij.webviews.AbstractWebviewMessagingService - Failed to parse webview message as protobuf.
Exception: Missing type url when parsing: {"type":"can-show-terminal","data":{"terminalId":16}}
Message: {"type":"async-wrapper","requestId":"5a2ab37d-5713-464b-a427-87d1a4f92943","error":null,"baseMsg":{"type":"can-show-terminal","data":{"terminalId":16}},"destination":"host"}
2025-09-23 09:41:32,077 [ 642200]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_ERROR]: Failed to check if terminal can be shown: Error: Unable to parse webview message: {"type":"async-wrapper","requestId":"5a2ab37d-5713-464b-a427-87d1a4f92943","error":null,"baseMsg":{"type":"can-show-terminal","data":{"terminalId":16}},"destination":"host"} [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:41:33,163 [ 643286]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: fb360b5251b57fc0cde66730aaf901124d2507aa7c242d24eff964241ef1bcde -> a68bf4c6aa44940f448f4c63c22ebdff0d5fbee5c16c891280c6b13e5d888130
2025-09-23 09:41:37,477 [ 647600]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: No exchange with this request ID found. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:41:38,096 [ 648219]   WARN - #c.i.e.wsl - Failed to get WSL mount root for Ubuntu: DOS working directory is expected, but got \\wsl.localhost\Ubuntu\home\oni\audio-transcriber
2025-09-23 09:41:38,893 [ 649016]   WARN - #c.i.e.wsl - Failed to get WSL mount root for Ubuntu: DOS working directory is expected, but got \\wsl.localhost\Ubuntu\home\oni\audio-transcriber
2025-09-23 09:41:39,095 [ 649218]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: IntersectionDisplay context not found.  Call setIntersectionContext first. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:41:39,367 [ 649490]   WARN - #com.augmentcode.intellij.webviews.AbstractWebviewMessagingService - Failed to parse webview message as protobuf.
Exception: Missing type url when parsing: {"type":"can-show-terminal","data":{"command":"curl -X GET http://localhost:5000/api/health"}}
Message: {"type":"async-wrapper","requestId":"2018e0e7-37d7-4a80-9cb2-3eaa4e11b598","error":null,"baseMsg":{"type":"can-show-terminal","data":{"command":"curl -X GET http://localhost:5000/api/health"}},"destination":"host"}
2025-09-23 09:41:39,368 [ 649491]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_ERROR]: Failed to check if terminal can be shown: Error: Unable to parse webview message: {"type":"async-wrapper","requestId":"2018e0e7-37d7-4a80-9cb2-3eaa4e11b598","error":null,"baseMsg":{"type":"can-show-terminal","data":{"command":"curl -X GET http://localhost:5000/api/health"}},"destination":"host"} [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:41:40,079 [ 650202]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (a68bf4c6aa44940f448f4c63c22ebdff0d5fbee5c16c891280c6b13e5d888130) is unknown but expected to be uploaded
2025-09-23 09:41:40,079 [ 650202]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (a68bf4c6aa44940f448f4c63c22ebdff0d5fbee5c16c891280c6b13e5d888130) is unknown but expected to be uploaded
2025-09-23 09:41:40,079 [ 650202]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (a68bf4c6aa44940f448f4c63c22ebdff0d5fbee5c16c891280c6b13e5d888130) is unknown but expected to be uploaded
2025-09-23 09:41:40,079 [ 650202]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (a68bf4c6aa44940f448f4c63c22ebdff0d5fbee5c16c891280c6b13e5d888130) is unknown but expected to be uploaded
2025-09-23 09:41:40,079 [ 650202]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (a68bf4c6aa44940f448f4c63c22ebdff0d5fbee5c16c891280c6b13e5d888130) is unknown but expected to be uploaded
2025-09-23 09:41:40,079 [ 650202]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (a68bf4c6aa44940f448f4c63c22ebdff0d5fbee5c16c891280c6b13e5d888130) is unknown but expected to be uploaded
2025-09-23 09:41:40,079 [ 650202]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (a68bf4c6aa44940f448f4c63c22ebdff0d5fbee5c16c891280c6b13e5d888130) is unknown but expected to be uploaded
2025-09-23 09:41:40,629 [ 650752]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 4ee5b1641efe8a8bc51a6417ac3b45c7d97166a9cf841d977346c892a3e67fd9 -> 99f3022640b02b4cf762756a9a9b96673fc4b5f7c216214183542a16a9f1ab66
2025-09-23 09:41:41,194 [ 651317]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: a68bf4c6aa44940f448f4c63c22ebdff0d5fbee5c16c891280c6b13e5d888130 -> 99f3022640b02b4cf762756a9a9b96673fc4b5f7c216214183542a16a9f1ab66
2025-09-23 09:41:41,436 [ 651559]   WARN - #com.augmentcode.intellij.webviews.AbstractWebviewMessagingService - Failed to parse webview message as protobuf.
Exception: Missing type url when parsing: {"type":"can-show-terminal","data":{"command":"curl -X GET http://localhost:5000/api/health"}}
Message: {"type":"async-wrapper","requestId":"2f352bae-47ee-494d-99bf-9f103827fd0c","error":null,"baseMsg":{"type":"can-show-terminal","data":{"command":"curl -X GET http://localhost:5000/api/health"}},"destination":"host"}
2025-09-23 09:41:41,477 [ 651600]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_ERROR]: Failed to check if terminal can be shown: Error: Unable to parse webview message: {"type":"async-wrapper","requestId":"2f352bae-47ee-494d-99bf-9f103827fd0c","error":null,"baseMsg":{"type":"can-show-terminal","data":{"command":"curl -X GET http://localhost:5000/api/health"}},"destination":"host"} [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:41:41,550 [ 651673]   WARN - #com.augmentcode.intellij.chat.ChatWebviewMessageClient$Companion - Workspace guidelines are empty, not including in chat request. 
2025-09-23 09:41:41,662 [ 651785]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: a68bf4c6aa44940f448f4c63c22ebdff0d5fbee5c16c891280c6b13e5d888130 -> 99f3022640b02b4cf762756a9a9b96673fc4b5f7c216214183542a16a9f1ab66
2025-09-23 09:41:42,137 [ 652260]   WARN - #c.i.e.wsl - Failed to get WSL mount root for Ubuntu: DOS working directory is expected, but got \\wsl.localhost\Ubuntu\home\oni\audio-transcriber
2025-09-23 09:41:42,309 [ 652432]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: a68bf4c6aa44940f448f4c63c22ebdff0d5fbee5c16c891280c6b13e5d888130 -> 99f3022640b02b4cf762756a9a9b96673fc4b5f7c216214183542a16a9f1ab66
2025-09-23 09:41:42,856 [ 652979]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: a68bf4c6aa44940f448f4c63c22ebdff0d5fbee5c16c891280c6b13e5d888130 -> 5b3f50d9649f8eb19ba67951a1d3d013812c30282f81dec870f7c7d8f09689fc
2025-09-23 09:41:43,668 [ 653791]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: a68bf4c6aa44940f448f4c63c22ebdff0d5fbee5c16c891280c6b13e5d888130 -> 5b3f50d9649f8eb19ba67951a1d3d013812c30282f81dec870f7c7d8f09689fc
2025-09-23 09:41:44,121 [ 654244]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: a68bf4c6aa44940f448f4c63c22ebdff0d5fbee5c16c891280c6b13e5d888130 -> 5b3f50d9649f8eb19ba67951a1d3d013812c30282f81dec870f7c7d8f09689fc
2025-09-23 09:41:44,141 [ 654264]   WARN - #c.i.e.wsl - Failed to get WSL mount root for Ubuntu: DOS working directory is expected, but got \\wsl.localhost\Ubuntu\home\oni\audio-transcriber
2025-09-23 09:41:44,780 [ 654903]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: a68bf4c6aa44940f448f4c63c22ebdff0d5fbee5c16c891280c6b13e5d888130 -> 9b85664be9a21aeab7b9553a2e0fc05d3186899d3de584d7bedc9b87e8a92678
2025-09-23 09:41:44,965 [ 655088]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: No exchange with this request ID found. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:41:45,230 [ 655353]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 5b3f50d9649f8eb19ba67951a1d3d013812c30282f81dec870f7c7d8f09689fc -> 9b85664be9a21aeab7b9553a2e0fc05d3186899d3de584d7bedc9b87e8a92678
2025-09-23 09:41:48,671 [ 658794]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (99f3022640b02b4cf762756a9a9b96673fc4b5f7c216214183542a16a9f1ab66) is unknown but expected to be uploaded
2025-09-23 09:41:48,671 [ 658794]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (99f3022640b02b4cf762756a9a9b96673fc4b5f7c216214183542a16a9f1ab66) is unknown but expected to be uploaded
2025-09-23 09:41:48,671 [ 658794]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (99f3022640b02b4cf762756a9a9b96673fc4b5f7c216214183542a16a9f1ab66) is unknown but expected to be uploaded
2025-09-23 09:41:48,671 [ 658794]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (99f3022640b02b4cf762756a9a9b96673fc4b5f7c216214183542a16a9f1ab66) is unknown but expected to be uploaded
2025-09-23 09:41:48,671 [ 658794]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (99f3022640b02b4cf762756a9a9b96673fc4b5f7c216214183542a16a9f1ab66) is unknown but expected to be uploaded
2025-09-23 09:41:48,671 [ 658794]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (9b85664be9a21aeab7b9553a2e0fc05d3186899d3de584d7bedc9b87e8a92678) is unknown but expected to be uploaded
2025-09-23 09:41:48,671 [ 658794]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (9b85664be9a21aeab7b9553a2e0fc05d3186899d3de584d7bedc9b87e8a92678) is unknown but expected to be uploaded
2025-09-23 09:41:49,333 [ 659456]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 99f3022640b02b4cf762756a9a9b96673fc4b5f7c216214183542a16a9f1ab66 -> 9b85664be9a21aeab7b9553a2e0fc05d3186899d3de584d7bedc9b87e8a92678
2025-09-23 09:41:49,915 [ 660038]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 99f3022640b02b4cf762756a9a9b96673fc4b5f7c216214183542a16a9f1ab66 -> 9b85664be9a21aeab7b9553a2e0fc05d3186899d3de584d7bedc9b87e8a92678
2025-09-23 09:41:50,443 [ 660566]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 99f3022640b02b4cf762756a9a9b96673fc4b5f7c216214183542a16a9f1ab66 -> 9b85664be9a21aeab7b9553a2e0fc05d3186899d3de584d7bedc9b87e8a92678
2025-09-23 09:41:51,460 [ 661583]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 99f3022640b02b4cf762756a9a9b96673fc4b5f7c216214183542a16a9f1ab66 -> 9b85664be9a21aeab7b9553a2e0fc05d3186899d3de584d7bedc9b87e8a92678
2025-09-23 09:41:51,917 [ 662040]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 99f3022640b02b4cf762756a9a9b96673fc4b5f7c216214183542a16a9f1ab66 -> 9b85664be9a21aeab7b9553a2e0fc05d3186899d3de584d7bedc9b87e8a92678
2025-09-23 09:41:54,158 [ 664281]   WARN - #c.i.e.wsl - Failed to get WSL mount root for Ubuntu: DOS working directory is expected, but got \\wsl.localhost\Ubuntu\home\oni\audio-transcriber
2025-09-23 09:41:55,391 [ 665514]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (5b3f50d9649f8eb19ba67951a1d3d013812c30282f81dec870f7c7d8f09689fc) is unknown but expected to be uploaded
2025-09-23 09:41:55,391 [ 665514]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (5b3f50d9649f8eb19ba67951a1d3d013812c30282f81dec870f7c7d8f09689fc) is unknown but expected to be uploaded
2025-09-23 09:41:55,391 [ 665514]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (5b3f50d9649f8eb19ba67951a1d3d013812c30282f81dec870f7c7d8f09689fc) is unknown but expected to be uploaded
2025-09-23 09:41:55,391 [ 665514]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (9b85664be9a21aeab7b9553a2e0fc05d3186899d3de584d7bedc9b87e8a92678) is unknown but expected to be uploaded
2025-09-23 09:41:55,391 [ 665514]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (9b85664be9a21aeab7b9553a2e0fc05d3186899d3de584d7bedc9b87e8a92678) is unknown but expected to be uploaded
2025-09-23 09:41:55,391 [ 665514]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (9b85664be9a21aeab7b9553a2e0fc05d3186899d3de584d7bedc9b87e8a92678) is unknown but expected to be uploaded
2025-09-23 09:41:55,391 [ 665514]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (9b85664be9a21aeab7b9553a2e0fc05d3186899d3de584d7bedc9b87e8a92678) is unknown but expected to be uploaded
2025-09-23 09:41:55,391 [ 665514]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (9b85664be9a21aeab7b9553a2e0fc05d3186899d3de584d7bedc9b87e8a92678) is unknown but expected to be uploaded
2025-09-23 09:41:55,391 [ 665514]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (9b85664be9a21aeab7b9553a2e0fc05d3186899d3de584d7bedc9b87e8a92678) is unknown but expected to be uploaded
2025-09-23 09:41:55,391 [ 665514]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (9b85664be9a21aeab7b9553a2e0fc05d3186899d3de584d7bedc9b87e8a92678) is unknown but expected to be uploaded
2025-09-23 09:41:55,535 [ 665658]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 9b85664be9a21aeab7b9553a2e0fc05d3186899d3de584d7bedc9b87e8a92678 -> 18135d167dfe6035c61c0646c3e26e1e2e19f04056b81121bc9a4774a5943e24
2025-09-23 09:41:55,579 [ 665702]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: IntersectionDisplay context not found.  Call setIntersectionContext first. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:41:55,707 [ 665830]   WARN - #com.augmentcode.intellij.chat.ChatWebviewMessageClient$Companion - Workspace guidelines are empty, not including in chat request. 
2025-09-23 09:41:55,904 [ 666027]   WARN - #c.i.e.wsl - Failed to get WSL mount root for Ubuntu: DOS working directory is expected, but got \\wsl.localhost\Ubuntu\home\oni\audio-transcriber
2025-09-23 09:41:56,036 [ 666159]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 5b3f50d9649f8eb19ba67951a1d3d013812c30282f81dec870f7c7d8f09689fc -> 18135d167dfe6035c61c0646c3e26e1e2e19f04056b81121bc9a4774a5943e24
2025-09-23 09:41:56,550 [ 666673]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 5b3f50d9649f8eb19ba67951a1d3d013812c30282f81dec870f7c7d8f09689fc -> 18135d167dfe6035c61c0646c3e26e1e2e19f04056b81121bc9a4774a5943e24
2025-09-23 09:41:57,064 [ 667187]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 9b85664be9a21aeab7b9553a2e0fc05d3186899d3de584d7bedc9b87e8a92678 -> 18135d167dfe6035c61c0646c3e26e1e2e19f04056b81121bc9a4774a5943e24
2025-09-23 09:41:57,511 [ 667634]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 9b85664be9a21aeab7b9553a2e0fc05d3186899d3de584d7bedc9b87e8a92678 -> 18135d167dfe6035c61c0646c3e26e1e2e19f04056b81121bc9a4774a5943e24
2025-09-23 09:41:58,196 [ 668319]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 9b85664be9a21aeab7b9553a2e0fc05d3186899d3de584d7bedc9b87e8a92678 -> a90e89e0cc1c459a9d09743c1c3ae7be62a59ae71aa94c99f97734d5f4f33851
2025-09-23 09:41:58,642 [ 668765]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 5b3f50d9649f8eb19ba67951a1d3d013812c30282f81dec870f7c7d8f09689fc -> a90e89e0cc1c459a9d09743c1c3ae7be62a59ae71aa94c99f97734d5f4f33851
2025-09-23 09:41:59,262 [ 669385]   WARN - #com.augmentcode.intellij.sentry.SentryMetadataCollector - Git ls-files command failed with exit code: 128
2025-09-23 09:41:59,685 [ 669808]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 9b85664be9a21aeab7b9553a2e0fc05d3186899d3de584d7bedc9b87e8a92678 -> a90e89e0cc1c459a9d09743c1c3ae7be62a59ae71aa94c99f97734d5f4f33851
2025-09-23 09:41:59,844 [ 669967]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: No exchange with this request ID found. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:42:00,180 [ 670303]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 9b85664be9a21aeab7b9553a2e0fc05d3186899d3de584d7bedc9b87e8a92678 -> a90e89e0cc1c459a9d09743c1c3ae7be62a59ae71aa94c99f97734d5f4f33851
2025-09-23 09:42:00,653 [ 670776]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 9b85664be9a21aeab7b9553a2e0fc05d3186899d3de584d7bedc9b87e8a92678 -> a90e89e0cc1c459a9d09743c1c3ae7be62a59ae71aa94c99f97734d5f4f33851
2025-09-23 09:42:04,090 [ 674213]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (a90e89e0cc1c459a9d09743c1c3ae7be62a59ae71aa94c99f97734d5f4f33851) is unknown but expected to be uploaded
2025-09-23 09:42:04,091 [ 674214]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (a90e89e0cc1c459a9d09743c1c3ae7be62a59ae71aa94c99f97734d5f4f33851) is unknown but expected to be uploaded
2025-09-23 09:42:04,091 [ 674214]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (a90e89e0cc1c459a9d09743c1c3ae7be62a59ae71aa94c99f97734d5f4f33851) is unknown but expected to be uploaded
2025-09-23 09:42:04,091 [ 674214]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (a90e89e0cc1c459a9d09743c1c3ae7be62a59ae71aa94c99f97734d5f4f33851) is unknown but expected to be uploaded
2025-09-23 09:42:04,091 [ 674214]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (a90e89e0cc1c459a9d09743c1c3ae7be62a59ae71aa94c99f97734d5f4f33851) is unknown but expected to be uploaded
2025-09-23 09:42:04,147 [ 674270]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 9b85664be9a21aeab7b9553a2e0fc05d3186899d3de584d7bedc9b87e8a92678 -> a90e89e0cc1c459a9d09743c1c3ae7be62a59ae71aa94c99f97734d5f4f33851
2025-09-23 09:42:06,887 [ 677010]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: IntersectionDisplay context not found.  Call setIntersectionContext first. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:42:07,939 [ 678062]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: Loading changes  for 2d5e3a2a-fed3-4dbe-b1ec-d84785350992 retry 0:  Error: No changes found [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:42:07,940 [ 678063]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: Loading changes  for 2d5e3a2a-fed3-4dbe-b1ec-d84785350992 retry 1:  Error: No changes found [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:42:07,952 [ 678075]   WARN - #com.augmentcode.intellij.chat.ChatWebviewMessageClient$Companion - Workspace guidelines are empty, not including in chat request. 
2025-09-23 09:42:08,443 [ 678566]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: Loading changes  for 2d5e3a2a-fed3-4dbe-b1ec-d84785350992 retry 2:  Error: No changes found [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:42:08,443 [ 678566]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: Loading changes  for 2d5e3a2a-fed3-4dbe-b1ec-d84785350992 retry 3:  Error: No changes found [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:42:08,443 [ 678566]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: Loading changes failed for 2d5e3a2a-fed3-4dbe-b1ec-d84785350992:  Error: No changes found [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:42:08,947 [ 679070]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: Loading changes  for 2d5e3a2a-fed3-4dbe-b1ec-d84785350992 retry 4:  Error: No changes found [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:42:08,947 [ 679070]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: Loading changes failed for 2d5e3a2a-fed3-4dbe-b1ec-d84785350992:  Error: No changes found [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:42:09,638 [ 679761]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (a90e89e0cc1c459a9d09743c1c3ae7be62a59ae71aa94c99f97734d5f4f33851) is unknown but expected to be uploaded
2025-09-23 09:42:09,638 [ 679761]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (a90e89e0cc1c459a9d09743c1c3ae7be62a59ae71aa94c99f97734d5f4f33851) is unknown but expected to be uploaded
2025-09-23 09:42:09,638 [ 679761]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (a90e89e0cc1c459a9d09743c1c3ae7be62a59ae71aa94c99f97734d5f4f33851) is unknown but expected to be uploaded
2025-09-23 09:42:09,638 [ 679761]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (a90e89e0cc1c459a9d09743c1c3ae7be62a59ae71aa94c99f97734d5f4f33851) is unknown but expected to be uploaded
2025-09-23 09:42:09,638 [ 679761]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (a90e89e0cc1c459a9d09743c1c3ae7be62a59ae71aa94c99f97734d5f4f33851) is unknown but expected to be uploaded
2025-09-23 09:42:11,337 [ 681460]   WARN - #c.i.e.wsl - Failed to get WSL mount root for Ubuntu: DOS working directory is expected, but got \\wsl.localhost\Ubuntu\home\oni\audio-transcriber
2025-09-23 09:42:11,786 [ 681909]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: a90e89e0cc1c459a9d09743c1c3ae7be62a59ae71aa94c99f97734d5f4f33851 -> 747858e75d9582b8a4fe8ceb447b0109d40a3bfeb01c460f8b5aaf585051b019
2025-09-23 09:42:12,280 [ 682403]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: a90e89e0cc1c459a9d09743c1c3ae7be62a59ae71aa94c99f97734d5f4f33851 -> 747858e75d9582b8a4fe8ceb447b0109d40a3bfeb01c460f8b5aaf585051b019
2025-09-23 09:42:12,308 [ 682431]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: No exchange with this request ID found. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:42:13,957 [ 684080]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: IntersectionDisplay context not found.  Call setIntersectionContext first. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:42:14,206 [ 684329]   WARN - #com.augmentcode.intellij.webviews.AbstractWebviewMessagingService - Failed to parse webview message as protobuf.
Exception: Missing type url when parsing: {"type":"can-show-terminal","data":{"command":"npm test -- --watchAll=false"}}
Message: {"type":"async-wrapper","requestId":"575a247d-a28a-4480-8ed0-f363d3ad736b","error":null,"baseMsg":{"type":"can-show-terminal","data":{"command":"npm test -- --watchAll=false"}},"destination":"host"}
2025-09-23 09:42:14,213 [ 684336]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_ERROR]: Failed to check if terminal can be shown: Error: Unable to parse webview message: {"type":"async-wrapper","requestId":"575a247d-a28a-4480-8ed0-f363d3ad736b","error":null,"baseMsg":{"type":"can-show-terminal","data":{"command":"npm test -- --watchAll=false"}},"destination":"host"} [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:42:15,721 [ 685844]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (18135d167dfe6035c61c0646c3e26e1e2e19f04056b81121bc9a4774a5943e24) is unknown but expected to be uploaded
2025-09-23 09:42:15,721 [ 685844]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (18135d167dfe6035c61c0646c3e26e1e2e19f04056b81121bc9a4774a5943e24) is unknown but expected to be uploaded
2025-09-23 09:42:15,721 [ 685844]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (18135d167dfe6035c61c0646c3e26e1e2e19f04056b81121bc9a4774a5943e24) is unknown but expected to be uploaded
2025-09-23 09:42:15,721 [ 685844]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (18135d167dfe6035c61c0646c3e26e1e2e19f04056b81121bc9a4774a5943e24) is unknown but expected to be uploaded
2025-09-23 09:42:15,721 [ 685844]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (18135d167dfe6035c61c0646c3e26e1e2e19f04056b81121bc9a4774a5943e24) is unknown but expected to be uploaded
2025-09-23 09:42:15,721 [ 685844]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (18135d167dfe6035c61c0646c3e26e1e2e19f04056b81121bc9a4774a5943e24) is unknown but expected to be uploaded
2025-09-23 09:42:15,721 [ 685844]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (18135d167dfe6035c61c0646c3e26e1e2e19f04056b81121bc9a4774a5943e24) is unknown but expected to be uploaded
2025-09-23 09:42:15,771 [ 685894]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: a90e89e0cc1c459a9d09743c1c3ae7be62a59ae71aa94c99f97734d5f4f33851 -> 747858e75d9582b8a4fe8ceb447b0109d40a3bfeb01c460f8b5aaf585051b019
2025-09-23 09:42:16,289 [ 686412]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 18135d167dfe6035c61c0646c3e26e1e2e19f04056b81121bc9a4774a5943e24 -> 747858e75d9582b8a4fe8ceb447b0109d40a3bfeb01c460f8b5aaf585051b019
2025-09-23 09:42:16,509 [ 686632]   WARN - #com.augmentcode.intellij.webviews.AbstractWebviewMessagingService - Failed to parse webview message as protobuf.
Exception: Missing type url when parsing: {"type":"can-show-terminal","data":{"command":"npm test -- --watchAll=false"}}
Message: {"type":"async-wrapper","requestId":"9c4ff8ba-d93e-46ef-97e2-cf2a832d0229","error":null,"baseMsg":{"type":"can-show-terminal","data":{"command":"npm test -- --watchAll=false"}},"destination":"host"}
2025-09-23 09:42:16,538 [ 686661]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_ERROR]: Failed to check if terminal can be shown: Error: Unable to parse webview message: {"type":"async-wrapper","requestId":"9c4ff8ba-d93e-46ef-97e2-cf2a832d0229","error":null,"baseMsg":{"type":"can-show-terminal","data":{"command":"npm test -- --watchAll=false"}},"destination":"host"} [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:42:16,830 [ 686953]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 18135d167dfe6035c61c0646c3e26e1e2e19f04056b81121bc9a4774a5943e24 -> 747858e75d9582b8a4fe8ceb447b0109d40a3bfeb01c460f8b5aaf585051b019
2025-09-23 09:42:17,108 [ 687231]   WARN - #c.i.e.wsl - Failed to get WSL mount root for Ubuntu: DOS working directory is expected, but got \\wsl.localhost\Ubuntu\home\oni\audio-transcriber
2025-09-23 09:42:17,439 [ 687562]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 18135d167dfe6035c61c0646c3e26e1e2e19f04056b81121bc9a4774a5943e24 -> 227592c7278cd1010743589e0f27e0050f09e44482129400fc7e0ff0d120ebd8
2025-09-23 09:42:18,078 [ 688201]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 18135d167dfe6035c61c0646c3e26e1e2e19f04056b81121bc9a4774a5943e24 -> 227592c7278cd1010743589e0f27e0050f09e44482129400fc7e0ff0d120ebd8
2025-09-23 09:42:18,644 [ 688767]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 18135d167dfe6035c61c0646c3e26e1e2e19f04056b81121bc9a4774a5943e24 -> 227592c7278cd1010743589e0f27e0050f09e44482129400fc7e0ff0d120ebd8
2025-09-23 09:42:18,907 [ 689030] SEVERE - com.jediterm.terminal.model.TerminalTextBuffer - Attempt to get line out of bounds: 11 >= 11
2025-09-23 09:42:18,908 [ 689031] SEVERE - com.jediterm.terminal.model.TerminalTextBuffer - Attempt to get line out of bounds: 11 >= 11
2025-09-23 09:42:18,952 [ 689075]   WARN - #com.augmentcode.intellij.chat.ChatWebviewMessageClient$Companion - Workspace guidelines are empty, not including in chat request. 
2025-09-23 09:42:19,133 [ 689256]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 18135d167dfe6035c61c0646c3e26e1e2e19f04056b81121bc9a4774a5943e24 -> 227592c7278cd1010743589e0f27e0050f09e44482129400fc7e0ff0d120ebd8
2025-09-23 09:42:19,547 [ 689670]   WARN - #c.i.e.wsl - Failed to get WSL mount root for Ubuntu: DOS working directory is expected, but got \\wsl.localhost\Ubuntu\home\oni\audio-transcriber
2025-09-23 09:42:21,600 [ 691723]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 18135d167dfe6035c61c0646c3e26e1e2e19f04056b81121bc9a4774a5943e24 -> 222caf524693dc7acd46dba2727bf193495cd1a398716e26730f0fa1bc3edcea
2025-09-23 09:42:22,610 [ 692733]   WARN - #c.i.e.wsl - Failed to get WSL mount root for Ubuntu: DOS working directory is expected, but got \\wsl.localhost\Ubuntu\home\oni\audio-transcriber
2025-09-23 09:42:22,746 [ 692869]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: No exchange with this request ID found. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:42:25,011 [ 695134]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (747858e75d9582b8a4fe8ceb447b0109d40a3bfeb01c460f8b5aaf585051b019) is unknown but expected to be uploaded
2025-09-23 09:42:25,011 [ 695134]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (747858e75d9582b8a4fe8ceb447b0109d40a3bfeb01c460f8b5aaf585051b019) is unknown but expected to be uploaded
2025-09-23 09:42:25,011 [ 695134]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (747858e75d9582b8a4fe8ceb447b0109d40a3bfeb01c460f8b5aaf585051b019) is unknown but expected to be uploaded
2025-09-23 09:42:25,011 [ 695134]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (747858e75d9582b8a4fe8ceb447b0109d40a3bfeb01c460f8b5aaf585051b019) is unknown but expected to be uploaded
2025-09-23 09:42:25,011 [ 695134]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (747858e75d9582b8a4fe8ceb447b0109d40a3bfeb01c460f8b5aaf585051b019) is unknown but expected to be uploaded
2025-09-23 09:42:25,011 [ 695134]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (747858e75d9582b8a4fe8ceb447b0109d40a3bfeb01c460f8b5aaf585051b019) is unknown but expected to be uploaded
2025-09-23 09:42:25,011 [ 695134]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (747858e75d9582b8a4fe8ceb447b0109d40a3bfeb01c460f8b5aaf585051b019) is unknown but expected to be uploaded
2025-09-23 09:42:25,527 [ 695650]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 747858e75d9582b8a4fe8ceb447b0109d40a3bfeb01c460f8b5aaf585051b019 -> 222caf524693dc7acd46dba2727bf193495cd1a398716e26730f0fa1bc3edcea
2025-09-23 09:42:25,996 [ 696119]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 747858e75d9582b8a4fe8ceb447b0109d40a3bfeb01c460f8b5aaf585051b019 -> 222caf524693dc7acd46dba2727bf193495cd1a398716e26730f0fa1bc3edcea
2025-09-23 09:42:26,532 [ 696655]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 747858e75d9582b8a4fe8ceb447b0109d40a3bfeb01c460f8b5aaf585051b019 -> 222caf524693dc7acd46dba2727bf193495cd1a398716e26730f0fa1bc3edcea
2025-09-23 09:42:27,041 [ 697164]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 747858e75d9582b8a4fe8ceb447b0109d40a3bfeb01c460f8b5aaf585051b019 -> 222caf524693dc7acd46dba2727bf193495cd1a398716e26730f0fa1bc3edcea
2025-09-23 09:42:27,813 [ 697936]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 747858e75d9582b8a4fe8ceb447b0109d40a3bfeb01c460f8b5aaf585051b019 -> 222caf524693dc7acd46dba2727bf193495cd1a398716e26730f0fa1bc3edcea
2025-09-23 09:42:28,320 [ 698443]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 747858e75d9582b8a4fe8ceb447b0109d40a3bfeb01c460f8b5aaf585051b019 -> 222caf524693dc7acd46dba2727bf193495cd1a398716e26730f0fa1bc3edcea
2025-09-23 09:42:31,779 [ 701902]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (a90e89e0cc1c459a9d09743c1c3ae7be62a59ae71aa94c99f97734d5f4f33851) is unknown but expected to be uploaded
2025-09-23 09:42:31,779 [ 701902]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (a90e89e0cc1c459a9d09743c1c3ae7be62a59ae71aa94c99f97734d5f4f33851) is unknown but expected to be uploaded
2025-09-23 09:42:31,779 [ 701902]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (a90e89e0cc1c459a9d09743c1c3ae7be62a59ae71aa94c99f97734d5f4f33851) is unknown but expected to be uploaded
2025-09-23 09:42:31,779 [ 701902]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (222caf524693dc7acd46dba2727bf193495cd1a398716e26730f0fa1bc3edcea) is unknown but expected to be uploaded
2025-09-23 09:42:31,779 [ 701902]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (222caf524693dc7acd46dba2727bf193495cd1a398716e26730f0fa1bc3edcea) is unknown but expected to be uploaded
2025-09-23 09:42:31,779 [ 701902]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (222caf524693dc7acd46dba2727bf193495cd1a398716e26730f0fa1bc3edcea) is unknown but expected to be uploaded
2025-09-23 09:42:31,779 [ 701902]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (222caf524693dc7acd46dba2727bf193495cd1a398716e26730f0fa1bc3edcea) is unknown but expected to be uploaded
2025-09-23 09:42:31,779 [ 701902]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (222caf524693dc7acd46dba2727bf193495cd1a398716e26730f0fa1bc3edcea) is unknown but expected to be uploaded
2025-09-23 09:42:31,779 [ 701902]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (222caf524693dc7acd46dba2727bf193495cd1a398716e26730f0fa1bc3edcea) is unknown but expected to be uploaded
2025-09-23 09:42:31,779 [ 701902]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (222caf524693dc7acd46dba2727bf193495cd1a398716e26730f0fa1bc3edcea) is unknown but expected to be uploaded
2025-09-23 09:42:31,779 [ 701902]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (222caf524693dc7acd46dba2727bf193495cd1a398716e26730f0fa1bc3edcea) is unknown but expected to be uploaded
2025-09-23 09:42:31,814 [ 701937]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 747858e75d9582b8a4fe8ceb447b0109d40a3bfeb01c460f8b5aaf585051b019 -> 222caf524693dc7acd46dba2727bf193495cd1a398716e26730f0fa1bc3edcea
2025-09-23 09:42:32,267 [ 702390]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: a90e89e0cc1c459a9d09743c1c3ae7be62a59ae71aa94c99f97734d5f4f33851 -> 222caf524693dc7acd46dba2727bf193495cd1a398716e26730f0fa1bc3edcea
2025-09-23 09:42:33,041 [ 703164]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: a90e89e0cc1c459a9d09743c1c3ae7be62a59ae71aa94c99f97734d5f4f33851 -> 222caf524693dc7acd46dba2727bf193495cd1a398716e26730f0fa1bc3edcea
2025-09-23 09:42:34,991 [ 705114]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: IntersectionDisplay context not found.  Call setIntersectionContext first. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:42:36,056 [ 706179]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: Loading changes  for bc469c48-fe48-49d8-a10e-d4b34f647581 retry 0:  Error: No changes found [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:42:36,058 [ 706181]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: Loading changes  for bc469c48-fe48-49d8-a10e-d4b34f647581 retry 1:  Error: No changes found [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:42:36,084 [ 706207]   WARN - #com.augmentcode.intellij.chat.ChatWebviewMessageClient$Companion - Workspace guidelines are empty, not including in chat request. 
2025-09-23 09:42:36,410 [ 706533]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: a90e89e0cc1c459a9d09743c1c3ae7be62a59ae71aa94c99f97734d5f4f33851 -> 222caf524693dc7acd46dba2727bf193495cd1a398716e26730f0fa1bc3edcea
2025-09-23 09:42:36,560 [ 706683]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: Loading changes  for bc469c48-fe48-49d8-a10e-d4b34f647581 retry 2:  Error: No changes found [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:42:36,562 [ 706685]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: Loading changes  for bc469c48-fe48-49d8-a10e-d4b34f647581 retry 3:  Error: No changes found [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:42:36,562 [ 706685]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: Loading changes failed for bc469c48-fe48-49d8-a10e-d4b34f647581:  Error: No changes found [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:42:37,064 [ 707187]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: Loading changes  for bc469c48-fe48-49d8-a10e-d4b34f647581 retry 4:  Error: No changes found [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:42:37,064 [ 707187]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: Loading changes failed for bc469c48-fe48-49d8-a10e-d4b34f647581:  Error: No changes found [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:42:40,493 [ 710616]   WARN - #com.augmentcode.intellij.api.AugmentHttpClient$Companion - Failed to call record-request-events (e0827ce9-f18a-463b-a00f-e34ea13d23a9)
io.ktor.client.plugins.HttpRequestTimeoutException: Request timeout has expired [url=https://i1.api.augmentcode.com/record-request-events, request_timeout=60000 ms]
	at io.ktor.client.plugins.HttpTimeout$Plugin$install$1$1$killer$1.invokeSuspend(HttpTimeout.kt:165)
	at kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)
	at kotlinx.coroutines.DispatchedTask.run(DispatchedTask.kt:100)
	at kotlinx.coroutines.internal.SoftLimitedDispatcher$Worker.run(SoftLimitedDispatcher.kt:130)
	at kotlinx.coroutines.scheduling.TaskImpl.run(Tasks.kt:89)
	at kotlinx.coroutines.scheduling.CoroutineScheduler.runSafely(CoroutineScheduler.kt:613)
	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.executeTask(CoroutineScheduler.kt:1183)
	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.runWorker(CoroutineScheduler.kt:778)
	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.run(CoroutineScheduler.kt:765)
2025-09-23 09:42:40,493 [ 710616]   WARN - #com.augmentcode.intellij.sidecar.SidecarService - Failed to record request events: Failed to make network call to record-request-events with request ID e0827ce9-f18a-463b-a00f-e34ea13d23a9: Request timeout has expired [url=https://i1.api.augmentcode.com/record-request-events, request_timeout=60000 ms]
java.lang.IllegalStateException: Failed to make network call to record-request-events with request ID e0827ce9-f18a-463b-a00f-e34ea13d23a9: Request timeout has expired [url=https://i1.api.augmentcode.com/record-request-events, request_timeout=60000 ms]
	at com.augmentcode.intellij.api.AugmentHttpClient.wrapNetworkError(AugmentHttpClient.kt:223)
	at com.augmentcode.intellij.api.AugmentHttpClient.access$wrapNetworkError(AugmentHttpClient.kt:31)
	at com.augmentcode.intellij.api.AugmentHttpClient$post$2.invokeSuspend(AugmentHttpClient.kt:180)
	at kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)
	at kotlinx.coroutines.DispatchedTask.run(DispatchedTask.kt:98)
	at kotlinx.coroutines.scheduling.CoroutineScheduler.runSafely(CoroutineScheduler.kt:613)
	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.executeTask(CoroutineScheduler.kt:1189)
	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.runWorker(CoroutineScheduler.kt:778)
	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.run(CoroutineScheduler.kt:765)
Caused by: io.ktor.client.plugins.HttpRequestTimeoutException: Request timeout has expired [url=https://i1.api.augmentcode.com/record-request-events, request_timeout=60000 ms]
	at io.ktor.client.plugins.HttpTimeout$Plugin$install$1$1$killer$1.invokeSuspend(HttpTimeout.kt:165)
	at kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)
	at kotlinx.coroutines.DispatchedTask.run(DispatchedTask.kt:100)
	at kotlinx.coroutines.internal.SoftLimitedDispatcher$Worker.run(SoftLimitedDispatcher.kt:130)
	at kotlinx.coroutines.scheduling.TaskImpl.run(Tasks.kt:89)
	at kotlinx.coroutines.scheduling.CoroutineScheduler.runSafely(CoroutineScheduler.kt:613)
	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.executeTask(CoroutineScheduler.kt:1183)
	... 2 more
2025-09-23 09:42:42,098 [ 712221]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (222caf524693dc7acd46dba2727bf193495cd1a398716e26730f0fa1bc3edcea) is unknown but expected to be uploaded
2025-09-23 09:42:42,098 [ 712221]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (222caf524693dc7acd46dba2727bf193495cd1a398716e26730f0fa1bc3edcea) is unknown but expected to be uploaded
2025-09-23 09:42:42,098 [ 712221]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (222caf524693dc7acd46dba2727bf193495cd1a398716e26730f0fa1bc3edcea) is unknown but expected to be uploaded
2025-09-23 09:42:42,098 [ 712221]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (222caf524693dc7acd46dba2727bf193495cd1a398716e26730f0fa1bc3edcea) is unknown but expected to be uploaded
2025-09-23 09:42:42,098 [ 712221]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (222caf524693dc7acd46dba2727bf193495cd1a398716e26730f0fa1bc3edcea) is unknown but expected to be uploaded
2025-09-23 09:42:42,098 [ 712221]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (222caf524693dc7acd46dba2727bf193495cd1a398716e26730f0fa1bc3edcea) is unknown but expected to be uploaded
2025-09-23 09:42:42,098 [ 712221]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (222caf524693dc7acd46dba2727bf193495cd1a398716e26730f0fa1bc3edcea) is unknown but expected to be uploaded
2025-09-23 09:42:42,098 [ 712221]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (222caf524693dc7acd46dba2727bf193495cd1a398716e26730f0fa1bc3edcea) is unknown but expected to be uploaded
2025-09-23 09:42:42,098 [ 712221]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (222caf524693dc7acd46dba2727bf193495cd1a398716e26730f0fa1bc3edcea) is unknown but expected to be uploaded
2025-09-23 09:42:42,098 [ 712221]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (222caf524693dc7acd46dba2727bf193495cd1a398716e26730f0fa1bc3edcea) is unknown but expected to be uploaded
2025-09-23 09:42:42,098 [ 712221]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (222caf524693dc7acd46dba2727bf193495cd1a398716e26730f0fa1bc3edcea) is unknown but expected to be uploaded
2025-09-23 09:42:42,204 [ 712327]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 222caf524693dc7acd46dba2727bf193495cd1a398716e26730f0fa1bc3edcea -> c6c9bdced16f6ce05b5350e956befaf38265074ed960cfe949a09f0e0a645ee8
2025-09-23 09:42:42,750 [ 712873]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: No exchange with this request ID found. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:42:42,882 [ 713005]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 222caf524693dc7acd46dba2727bf193495cd1a398716e26730f0fa1bc3edcea -> c6c9bdced16f6ce05b5350e956befaf38265074ed960cfe949a09f0e0a645ee8
2025-09-23 09:42:43,345 [ 713468]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 222caf524693dc7acd46dba2727bf193495cd1a398716e26730f0fa1bc3edcea -> c6c9bdced16f6ce05b5350e956befaf38265074ed960cfe949a09f0e0a645ee8
2025-09-23 09:42:44,030 [ 714153]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 222caf524693dc7acd46dba2727bf193495cd1a398716e26730f0fa1bc3edcea -> c6c9bdced16f6ce05b5350e956befaf38265074ed960cfe949a09f0e0a645ee8
2025-09-23 09:42:44,785 [ 714908]   WARN - #c.i.e.wsl - Failed to get WSL mount root for Ubuntu: DOS working directory is expected, but got \\wsl.localhost\Ubuntu\home\oni\audio-transcriber
2025-09-23 09:42:44,880 [ 715003]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 222caf524693dc7acd46dba2727bf193495cd1a398716e26730f0fa1bc3edcea -> c6c9bdced16f6ce05b5350e956befaf38265074ed960cfe949a09f0e0a645ee8
2025-09-23 09:42:45,479 [ 715602]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: IntersectionDisplay context not found.  Call setIntersectionContext first. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:42:45,580 [ 715703]   WARN - #com.augmentcode.intellij.chat.ChatWebviewMessageClient$Companion - Workspace guidelines are empty, not including in chat request. 
2025-09-23 09:42:45,645 [ 715768]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 222caf524693dc7acd46dba2727bf193495cd1a398716e26730f0fa1bc3edcea -> 1de745564c8569a1e71da97ad024dcc18c5fb8f52fde4569f1b96c423d69fc13
2025-09-23 09:42:46,364 [ 716487]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 222caf524693dc7acd46dba2727bf193495cd1a398716e26730f0fa1bc3edcea -> 1de745564c8569a1e71da97ad024dcc18c5fb8f52fde4569f1b96c423d69fc13
2025-09-23 09:42:46,550 [ 716673]   WARN - #com.augmentcode.intellij.api.AugmentHttpClient$Companion - Failed to call record-session-events (62083610-a693-42a5-a739-16880c527e4e): 400 Bad Request
2025-09-23 09:42:46,550 [ 716673]   WARN - #com.augmentcode.intellij.api.AugmentAPI$Companion - Failed to record session events: 400 Bad Request
2025-09-23 09:42:46,874 [ 716997]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 222caf524693dc7acd46dba2727bf193495cd1a398716e26730f0fa1bc3edcea -> 1de745564c8569a1e71da97ad024dcc18c5fb8f52fde4569f1b96c423d69fc13
2025-09-23 09:42:46,995 [ 717118]   WARN - #c.i.e.wsl - Failed to get WSL mount root for Ubuntu: DOS working directory is expected, but got \\wsl.localhost\Ubuntu\home\oni\audio-transcriber
2025-09-23 09:42:47,894 [ 718017]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 222caf524693dc7acd46dba2727bf193495cd1a398716e26730f0fa1bc3edcea -> d387a49230e6ed0ab4cd153212737e22c9d8d22910b3ed85c921afa21b73bd4b
2025-09-23 09:42:48,424 [ 718547]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 1de745564c8569a1e71da97ad024dcc18c5fb8f52fde4569f1b96c423d69fc13 -> d387a49230e6ed0ab4cd153212737e22c9d8d22910b3ed85c921afa21b73bd4b
2025-09-23 09:42:48,920 [ 719043]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 222caf524693dc7acd46dba2727bf193495cd1a398716e26730f0fa1bc3edcea -> d387a49230e6ed0ab4cd153212737e22c9d8d22910b3ed85c921afa21b73bd4b
2025-09-23 09:42:49,474 [ 719597]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 222caf524693dc7acd46dba2727bf193495cd1a398716e26730f0fa1bc3edcea -> d387a49230e6ed0ab4cd153212737e22c9d8d22910b3ed85c921afa21b73bd4b
2025-09-23 09:42:49,683 [ 719806]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: No exchange with this request ID found. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:42:49,845 [ 719968]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: IntersectionDisplay context not found.  Call setIntersectionContext first. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:42:49,899 [ 720022]   WARN - #com.augmentcode.intellij.chat.ChatWebviewMessageClient$Companion - Workspace guidelines are empty, not including in chat request. 
2025-09-23 09:42:52,969 [ 723092]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 222caf524693dc7acd46dba2727bf193495cd1a398716e26730f0fa1bc3edcea -> d387a49230e6ed0ab4cd153212737e22c9d8d22910b3ed85c921afa21b73bd4b
2025-09-23 09:42:53,185 [ 723308]   WARN - #c.i.e.wsl - Failed to get WSL mount root for Ubuntu: DOS working directory is expected, but got \\wsl.localhost\Ubuntu\home\oni\audio-transcriber
2025-09-23 09:42:53,458 [ 723581]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: No exchange with this request ID found. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:42:55,649 [ 725772]   WARN - #c.i.e.wsl - Failed to get WSL mount root for Ubuntu: DOS working directory is expected, but got \\wsl.localhost\Ubuntu\home\oni\audio-transcriber
2025-09-23 09:42:56,448 [ 726571]   WARN - #com.augmentcode.intellij.api.AugmentHttpClient$Companion - Failed to call record-session-events (88346624-6016-40c5-9ff7-2222c36814bd): 400 Bad Request
2025-09-23 09:42:56,448 [ 726571]   WARN - #com.augmentcode.intellij.api.AugmentAPI$Companion - Failed to record session events: 400 Bad Request
2025-09-23 09:42:57,251 [ 727374]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (c6c9bdced16f6ce05b5350e956befaf38265074ed960cfe949a09f0e0a645ee8) is unknown but expected to be uploaded
2025-09-23 09:42:57,251 [ 727374]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (c6c9bdced16f6ce05b5350e956befaf38265074ed960cfe949a09f0e0a645ee8) is unknown but expected to be uploaded
2025-09-23 09:42:57,251 [ 727374]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (c6c9bdced16f6ce05b5350e956befaf38265074ed960cfe949a09f0e0a645ee8) is unknown but expected to be uploaded
2025-09-23 09:42:57,251 [ 727374]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (c6c9bdced16f6ce05b5350e956befaf38265074ed960cfe949a09f0e0a645ee8) is unknown but expected to be uploaded
2025-09-23 09:42:57,251 [ 727374]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (c6c9bdced16f6ce05b5350e956befaf38265074ed960cfe949a09f0e0a645ee8) is unknown but expected to be uploaded
2025-09-23 09:42:57,251 [ 727374]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (c6c9bdced16f6ce05b5350e956befaf38265074ed960cfe949a09f0e0a645ee8) is unknown but expected to be uploaded
2025-09-23 09:42:57,251 [ 727374]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (1de745564c8569a1e71da97ad024dcc18c5fb8f52fde4569f1b96c423d69fc13) is unknown but expected to be uploaded
2025-09-23 09:42:57,251 [ 727374]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (1de745564c8569a1e71da97ad024dcc18c5fb8f52fde4569f1b96c423d69fc13) is unknown but expected to be uploaded
2025-09-23 09:42:57,251 [ 727374]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (1de745564c8569a1e71da97ad024dcc18c5fb8f52fde4569f1b96c423d69fc13) is unknown but expected to be uploaded
2025-09-23 09:42:57,251 [ 727374]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (1de745564c8569a1e71da97ad024dcc18c5fb8f52fde4569f1b96c423d69fc13) is unknown but expected to be uploaded
2025-09-23 09:42:57,251 [ 727374]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (d387a49230e6ed0ab4cd153212737e22c9d8d22910b3ed85c921afa21b73bd4b) is unknown but expected to be uploaded
2025-09-23 09:42:57,251 [ 727374]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (d387a49230e6ed0ab4cd153212737e22c9d8d22910b3ed85c921afa21b73bd4b) is unknown but expected to be uploaded
2025-09-23 09:42:57,251 [ 727374]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (d387a49230e6ed0ab4cd153212737e22c9d8d22910b3ed85c921afa21b73bd4b) is unknown but expected to be uploaded
2025-09-23 09:42:57,251 [ 727374]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (d387a49230e6ed0ab4cd153212737e22c9d8d22910b3ed85c921afa21b73bd4b) is unknown but expected to be uploaded
2025-09-23 09:42:57,251 [ 727374]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (d387a49230e6ed0ab4cd153212737e22c9d8d22910b3ed85c921afa21b73bd4b) is unknown but expected to be uploaded
2025-09-23 09:42:57,251 [ 727374]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (d387a49230e6ed0ab4cd153212737e22c9d8d22910b3ed85c921afa21b73bd4b) is unknown but expected to be uploaded
2025-09-23 09:42:58,385 [ 728508]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: c6c9bdced16f6ce05b5350e956befaf38265074ed960cfe949a09f0e0a645ee8 -> a25fa3ea8c84b9645f66c76fe4042c9479b99fe1b982584466320362239c54c9
2025-09-23 09:42:58,828 [ 728951]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: c6c9bdced16f6ce05b5350e956befaf38265074ed960cfe949a09f0e0a645ee8 -> a25fa3ea8c84b9645f66c76fe4042c9479b99fe1b982584466320362239c54c9
2025-09-23 09:42:59,343 [ 729466]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: c6c9bdced16f6ce05b5350e956befaf38265074ed960cfe949a09f0e0a645ee8 -> a25fa3ea8c84b9645f66c76fe4042c9479b99fe1b982584466320362239c54c9
2025-09-23 09:42:59,430 [ 729553]   WARN - #com.augmentcode.intellij.sentry.SentryMetadataCollector - Git ls-files command failed with exit code: 128
2025-09-23 09:42:59,837 [ 729960]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 1de745564c8569a1e71da97ad024dcc18c5fb8f52fde4569f1b96c423d69fc13 -> a25fa3ea8c84b9645f66c76fe4042c9479b99fe1b982584466320362239c54c9
2025-09-23 09:43:00,303 [ 730426]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 1de745564c8569a1e71da97ad024dcc18c5fb8f52fde4569f1b96c423d69fc13 -> a25fa3ea8c84b9645f66c76fe4042c9479b99fe1b982584466320362239c54c9
2025-09-23 09:43:00,841 [ 730964]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: d387a49230e6ed0ab4cd153212737e22c9d8d22910b3ed85c921afa21b73bd4b -> a25fa3ea8c84b9645f66c76fe4042c9479b99fe1b982584466320362239c54c9
2025-09-23 09:43:01,754 [ 731877]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: d387a49230e6ed0ab4cd153212737e22c9d8d22910b3ed85c921afa21b73bd4b -> a25fa3ea8c84b9645f66c76fe4042c9479b99fe1b982584466320362239c54c9
2025-09-23 09:43:02,288 [ 732411]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: d387a49230e6ed0ab4cd153212737e22c9d8d22910b3ed85c921afa21b73bd4b -> a25fa3ea8c84b9645f66c76fe4042c9479b99fe1b982584466320362239c54c9
2025-09-23 09:43:02,742 [ 732865]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: c6c9bdced16f6ce05b5350e956befaf38265074ed960cfe949a09f0e0a645ee8 -> a25fa3ea8c84b9645f66c76fe4042c9479b99fe1b982584466320362239c54c9
2025-09-23 09:43:03,268 [ 733391]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 1de745564c8569a1e71da97ad024dcc18c5fb8f52fde4569f1b96c423d69fc13 -> a25fa3ea8c84b9645f66c76fe4042c9479b99fe1b982584466320362239c54c9
2025-09-23 09:43:03,760 [ 733883]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: d387a49230e6ed0ab4cd153212737e22c9d8d22910b3ed85c921afa21b73bd4b -> a25fa3ea8c84b9645f66c76fe4042c9479b99fe1b982584466320362239c54c9
2025-09-23 09:43:04,232 [ 734355]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: d387a49230e6ed0ab4cd153212737e22c9d8d22910b3ed85c921afa21b73bd4b -> a25fa3ea8c84b9645f66c76fe4042c9479b99fe1b982584466320362239c54c9
2025-09-23 09:43:05,298 [ 735421]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: c6c9bdced16f6ce05b5350e956befaf38265074ed960cfe949a09f0e0a645ee8 -> a25fa3ea8c84b9645f66c76fe4042c9479b99fe1b982584466320362239c54c9
2025-09-23 09:43:05,804 [ 735927]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: d387a49230e6ed0ab4cd153212737e22c9d8d22910b3ed85c921afa21b73bd4b -> a25fa3ea8c84b9645f66c76fe4042c9479b99fe1b982584466320362239c54c9
2025-09-23 09:43:06,367 [ 736490]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 1de745564c8569a1e71da97ad024dcc18c5fb8f52fde4569f1b96c423d69fc13 -> a25fa3ea8c84b9645f66c76fe4042c9479b99fe1b982584466320362239c54c9
2025-09-23 09:43:09,833 [ 739956]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: c6c9bdced16f6ce05b5350e956befaf38265074ed960cfe949a09f0e0a645ee8 -> a25fa3ea8c84b9645f66c76fe4042c9479b99fe1b982584466320362239c54c9
2025-09-23 09:43:09,884 [ 740007]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (a25fa3ea8c84b9645f66c76fe4042c9479b99fe1b982584466320362239c54c9) is unknown but expected to be uploaded
2025-09-23 09:43:09,884 [ 740007]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (a25fa3ea8c84b9645f66c76fe4042c9479b99fe1b982584466320362239c54c9) is unknown but expected to be uploaded
2025-09-23 09:43:09,885 [ 740008]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (a25fa3ea8c84b9645f66c76fe4042c9479b99fe1b982584466320362239c54c9) is unknown but expected to be uploaded
2025-09-23 09:43:09,885 [ 740008]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (a25fa3ea8c84b9645f66c76fe4042c9479b99fe1b982584466320362239c54c9) is unknown but expected to be uploaded
2025-09-23 09:43:09,885 [ 740008]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (a25fa3ea8c84b9645f66c76fe4042c9479b99fe1b982584466320362239c54c9) is unknown but expected to be uploaded
2025-09-23 09:43:09,885 [ 740008]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (a25fa3ea8c84b9645f66c76fe4042c9479b99fe1b982584466320362239c54c9) is unknown but expected to be uploaded
2025-09-23 09:43:09,885 [ 740008]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (a25fa3ea8c84b9645f66c76fe4042c9479b99fe1b982584466320362239c54c9) is unknown but expected to be uploaded
2025-09-23 09:43:09,885 [ 740008]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (a25fa3ea8c84b9645f66c76fe4042c9479b99fe1b982584466320362239c54c9) is unknown but expected to be uploaded
2025-09-23 09:43:09,885 [ 740008]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (a25fa3ea8c84b9645f66c76fe4042c9479b99fe1b982584466320362239c54c9) is unknown but expected to be uploaded
2025-09-23 09:43:09,885 [ 740008]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (a25fa3ea8c84b9645f66c76fe4042c9479b99fe1b982584466320362239c54c9) is unknown but expected to be uploaded
2025-09-23 09:43:09,885 [ 740008]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (a25fa3ea8c84b9645f66c76fe4042c9479b99fe1b982584466320362239c54c9) is unknown but expected to be uploaded
2025-09-23 09:43:09,885 [ 740008]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (a25fa3ea8c84b9645f66c76fe4042c9479b99fe1b982584466320362239c54c9) is unknown but expected to be uploaded
2025-09-23 09:43:09,885 [ 740008]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (a25fa3ea8c84b9645f66c76fe4042c9479b99fe1b982584466320362239c54c9) is unknown but expected to be uploaded
2025-09-23 09:43:09,885 [ 740008]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (a25fa3ea8c84b9645f66c76fe4042c9479b99fe1b982584466320362239c54c9) is unknown but expected to be uploaded
2025-09-23 09:43:09,885 [ 740008]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (a25fa3ea8c84b9645f66c76fe4042c9479b99fe1b982584466320362239c54c9) is unknown but expected to be uploaded
2025-09-23 09:43:09,885 [ 740008]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (a25fa3ea8c84b9645f66c76fe4042c9479b99fe1b982584466320362239c54c9) is unknown but expected to be uploaded
2025-09-23 09:43:23,103 [ 753226]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (a25fa3ea8c84b9645f66c76fe4042c9479b99fe1b982584466320362239c54c9) is unknown but expected to be uploaded
2025-09-23 09:43:23,103 [ 753226]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (a25fa3ea8c84b9645f66c76fe4042c9479b99fe1b982584466320362239c54c9) is unknown but expected to be uploaded
2025-09-23 09:43:23,103 [ 753226]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (a25fa3ea8c84b9645f66c76fe4042c9479b99fe1b982584466320362239c54c9) is unknown but expected to be uploaded
2025-09-23 09:43:23,103 [ 753226]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (a25fa3ea8c84b9645f66c76fe4042c9479b99fe1b982584466320362239c54c9) is unknown but expected to be uploaded
2025-09-23 09:43:23,103 [ 753226]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (a25fa3ea8c84b9645f66c76fe4042c9479b99fe1b982584466320362239c54c9) is unknown but expected to be uploaded
2025-09-23 09:43:23,103 [ 753226]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (a25fa3ea8c84b9645f66c76fe4042c9479b99fe1b982584466320362239c54c9) is unknown but expected to be uploaded
2025-09-23 09:43:23,103 [ 753226]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (a25fa3ea8c84b9645f66c76fe4042c9479b99fe1b982584466320362239c54c9) is unknown but expected to be uploaded
2025-09-23 09:43:23,103 [ 753226]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (a25fa3ea8c84b9645f66c76fe4042c9479b99fe1b982584466320362239c54c9) is unknown but expected to be uploaded
2025-09-23 09:43:23,103 [ 753226]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (a25fa3ea8c84b9645f66c76fe4042c9479b99fe1b982584466320362239c54c9) is unknown but expected to be uploaded
2025-09-23 09:43:23,103 [ 753226]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (a25fa3ea8c84b9645f66c76fe4042c9479b99fe1b982584466320362239c54c9) is unknown but expected to be uploaded
2025-09-23 09:43:23,103 [ 753226]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (a25fa3ea8c84b9645f66c76fe4042c9479b99fe1b982584466320362239c54c9) is unknown but expected to be uploaded
2025-09-23 09:43:23,103 [ 753226]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (a25fa3ea8c84b9645f66c76fe4042c9479b99fe1b982584466320362239c54c9) is unknown but expected to be uploaded
2025-09-23 09:43:23,103 [ 753226]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (a25fa3ea8c84b9645f66c76fe4042c9479b99fe1b982584466320362239c54c9) is unknown but expected to be uploaded
2025-09-23 09:43:23,103 [ 753226]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (a25fa3ea8c84b9645f66c76fe4042c9479b99fe1b982584466320362239c54c9) is unknown but expected to be uploaded
2025-09-23 09:43:23,103 [ 753226]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (a25fa3ea8c84b9645f66c76fe4042c9479b99fe1b982584466320362239c54c9) is unknown but expected to be uploaded
2025-09-23 09:43:23,103 [ 753226]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (a25fa3ea8c84b9645f66c76fe4042c9479b99fe1b982584466320362239c54c9) is unknown but expected to be uploaded
2025-09-23 09:43:33,582 [ 763705]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (a25fa3ea8c84b9645f66c76fe4042c9479b99fe1b982584466320362239c54c9) is unknown but expected to be uploaded
2025-09-23 09:43:33,582 [ 763705]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (a25fa3ea8c84b9645f66c76fe4042c9479b99fe1b982584466320362239c54c9) is unknown but expected to be uploaded
2025-09-23 09:43:33,582 [ 763705]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (a25fa3ea8c84b9645f66c76fe4042c9479b99fe1b982584466320362239c54c9) is unknown but expected to be uploaded
2025-09-23 09:43:33,582 [ 763705]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (a25fa3ea8c84b9645f66c76fe4042c9479b99fe1b982584466320362239c54c9) is unknown but expected to be uploaded
2025-09-23 09:43:33,582 [ 763705]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (a25fa3ea8c84b9645f66c76fe4042c9479b99fe1b982584466320362239c54c9) is unknown but expected to be uploaded
2025-09-23 09:43:33,582 [ 763705]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (a25fa3ea8c84b9645f66c76fe4042c9479b99fe1b982584466320362239c54c9) is unknown but expected to be uploaded
2025-09-23 09:43:33,582 [ 763705]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (a25fa3ea8c84b9645f66c76fe4042c9479b99fe1b982584466320362239c54c9) is unknown but expected to be uploaded
2025-09-23 09:43:33,582 [ 763705]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (a25fa3ea8c84b9645f66c76fe4042c9479b99fe1b982584466320362239c54c9) is unknown but expected to be uploaded
2025-09-23 09:43:33,582 [ 763705]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (a25fa3ea8c84b9645f66c76fe4042c9479b99fe1b982584466320362239c54c9) is unknown but expected to be uploaded
2025-09-23 09:43:33,582 [ 763705]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (a25fa3ea8c84b9645f66c76fe4042c9479b99fe1b982584466320362239c54c9) is unknown but expected to be uploaded
2025-09-23 09:43:33,582 [ 763705]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (a25fa3ea8c84b9645f66c76fe4042c9479b99fe1b982584466320362239c54c9) is unknown but expected to be uploaded
2025-09-23 09:43:33,582 [ 763705]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (a25fa3ea8c84b9645f66c76fe4042c9479b99fe1b982584466320362239c54c9) is unknown but expected to be uploaded
2025-09-23 09:43:33,582 [ 763705]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (a25fa3ea8c84b9645f66c76fe4042c9479b99fe1b982584466320362239c54c9) is unknown but expected to be uploaded
2025-09-23 09:43:44,406 [ 774529]   WARN - #c.i.e.wsl - Failed to get WSL mount root for Ubuntu: DOS working directory is expected, but got \\wsl.localhost\Ubuntu\home\oni\audio-transcriber
2025-09-23 09:43:59,587 [ 789710]   WARN - #com.augmentcode.intellij.sentry.SentryMetadataCollector - Git ls-files command failed with exit code: 128
2025-09-23 09:44:05,100 [ 795223]   WARN - #c.i.e.wsl - Failed to get WSL mount root for Ubuntu: DOS working directory is expected, but got \\wsl.localhost\Ubuntu\home\oni\audio-transcriber
2025-09-23 09:44:30,160 [ 820283]   WARN - #com.augmentcode.intellij.api.AugmentHttpClient$Companion - Failed to call batch-upload (517e091b-b3ae-467c-8c0f-a33ff5561371)
io.ktor.client.plugins.HttpRequestTimeoutException: Request timeout has expired [url=https://i1.api.augmentcode.com/batch-upload, request_timeout=60000 ms]
	at io.ktor.client.plugins.HttpTimeout$Plugin$install$1$1$killer$1.invokeSuspend(HttpTimeout.kt:165)
	at kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)
	at kotlinx.coroutines.DispatchedTask.run(DispatchedTask.kt:100)
	at kotlinx.coroutines.internal.SoftLimitedDispatcher$Worker.run(SoftLimitedDispatcher.kt:130)
	at kotlinx.coroutines.scheduling.TaskImpl.run(Tasks.kt:89)
	at kotlinx.coroutines.scheduling.CoroutineScheduler.runSafely(CoroutineScheduler.kt:613)
	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.executeTask(CoroutineScheduler.kt:1183)
	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.runWorker(CoroutineScheduler.kt:778)
	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.run(CoroutineScheduler.kt:765)
2025-09-23 09:44:30,162 [ 820285]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Failed to process upload batch
java.lang.IllegalStateException: Failed to make network call to batch-upload with request ID 517e091b-b3ae-467c-8c0f-a33ff5561371: Request timeout has expired [url=https://i1.api.augmentcode.com/batch-upload, request_timeout=60000 ms]
	at com.augmentcode.intellij.api.AugmentHttpClient.wrapNetworkError(AugmentHttpClient.kt:223)
	at com.augmentcode.intellij.api.AugmentHttpClient.access$wrapNetworkError(AugmentHttpClient.kt:31)
	at com.augmentcode.intellij.api.AugmentHttpClient$post$2.invokeSuspend(AugmentHttpClient.kt:180)
	at kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)
	at kotlinx.coroutines.DispatchedTask.run(DispatchedTask.kt:98)
	at kotlinx.coroutines.scheduling.CoroutineScheduler.runSafely(CoroutineScheduler.kt:613)
	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.executeTask(CoroutineScheduler.kt:1189)
	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.runWorker(CoroutineScheduler.kt:778)
	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.run(CoroutineScheduler.kt:765)
Caused by: io.ktor.client.plugins.HttpRequestTimeoutException: Request timeout has expired [url=https://i1.api.augmentcode.com/batch-upload, request_timeout=60000 ms]
	at io.ktor.client.plugins.HttpTimeout$Plugin$install$1$1$killer$1.invokeSuspend(HttpTimeout.kt:165)
	at kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)
	at kotlinx.coroutines.DispatchedTask.run(DispatchedTask.kt:100)
	at kotlinx.coroutines.internal.SoftLimitedDispatcher$Worker.run(SoftLimitedDispatcher.kt:130)
	at kotlinx.coroutines.scheduling.TaskImpl.run(Tasks.kt:89)
	at kotlinx.coroutines.scheduling.CoroutineScheduler.runSafely(CoroutineScheduler.kt:613)
	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.executeTask(CoroutineScheduler.kt:1183)
	... 2 more
2025-09-23 09:44:31,002 [ 821125]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: a25fa3ea8c84b9645f66c76fe4042c9479b99fe1b982584466320362239c54c9 -> 5f0bc2257df490895e656adc0c5d775716c5e389caa93b8e85bea859ba98ff8a
2025-09-23 09:44:31,480 [ 821603]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: a25fa3ea8c84b9645f66c76fe4042c9479b99fe1b982584466320362239c54c9 -> 5f0bc2257df490895e656adc0c5d775716c5e389caa93b8e85bea859ba98ff8a
2025-09-23 09:44:31,952 [ 822075]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: a25fa3ea8c84b9645f66c76fe4042c9479b99fe1b982584466320362239c54c9 -> 5f0bc2257df490895e656adc0c5d775716c5e389caa93b8e85bea859ba98ff8a
2025-09-23 09:44:32,504 [ 822627]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: a25fa3ea8c84b9645f66c76fe4042c9479b99fe1b982584466320362239c54c9 -> 5f0bc2257df490895e656adc0c5d775716c5e389caa93b8e85bea859ba98ff8a
2025-09-23 09:44:33,187 [ 823310]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: a25fa3ea8c84b9645f66c76fe4042c9479b99fe1b982584466320362239c54c9 -> 5f0bc2257df490895e656adc0c5d775716c5e389caa93b8e85bea859ba98ff8a
2025-09-23 09:44:34,072 [ 824195]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: a25fa3ea8c84b9645f66c76fe4042c9479b99fe1b982584466320362239c54c9 -> 5f0bc2257df490895e656adc0c5d775716c5e389caa93b8e85bea859ba98ff8a
2025-09-23 09:44:34,522 [ 824645]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: a25fa3ea8c84b9645f66c76fe4042c9479b99fe1b982584466320362239c54c9 -> 5f0bc2257df490895e656adc0c5d775716c5e389caa93b8e85bea859ba98ff8a
2025-09-23 09:44:35,132 [ 825255]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: a25fa3ea8c84b9645f66c76fe4042c9479b99fe1b982584466320362239c54c9 -> 5f0bc2257df490895e656adc0c5d775716c5e389caa93b8e85bea859ba98ff8a
2025-09-23 09:44:35,666 [ 825789]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: ca24b3227ad4d572ecf088b1a9bd724e0be5b1a6dded94fa0c4739e023ee2d16 -> 5f0bc2257df490895e656adc0c5d775716c5e389caa93b8e85bea859ba98ff8a
2025-09-23 09:44:36,294 [ 826417]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: a25fa3ea8c84b9645f66c76fe4042c9479b99fe1b982584466320362239c54c9 -> 5f0bc2257df490895e656adc0c5d775716c5e389caa93b8e85bea859ba98ff8a
2025-09-23 09:44:36,770 [ 826893]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: a25fa3ea8c84b9645f66c76fe4042c9479b99fe1b982584466320362239c54c9 -> 5f0bc2257df490895e656adc0c5d775716c5e389caa93b8e85bea859ba98ff8a
2025-09-23 09:44:37,468 [ 827591]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: a25fa3ea8c84b9645f66c76fe4042c9479b99fe1b982584466320362239c54c9 -> 5f0bc2257df490895e656adc0c5d775716c5e389caa93b8e85bea859ba98ff8a
2025-09-23 09:44:37,936 [ 828059]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: a25fa3ea8c84b9645f66c76fe4042c9479b99fe1b982584466320362239c54c9 -> 5f0bc2257df490895e656adc0c5d775716c5e389caa93b8e85bea859ba98ff8a
2025-09-23 09:44:39,143 [ 829266]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: a25fa3ea8c84b9645f66c76fe4042c9479b99fe1b982584466320362239c54c9 -> 5f0bc2257df490895e656adc0c5d775716c5e389caa93b8e85bea859ba98ff8a
2025-09-23 09:44:39,678 [ 829801]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: a25fa3ea8c84b9645f66c76fe4042c9479b99fe1b982584466320362239c54c9 -> 5f0bc2257df490895e656adc0c5d775716c5e389caa93b8e85bea859ba98ff8a
2025-09-23 09:44:40,327 [ 830450]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: a25fa3ea8c84b9645f66c76fe4042c9479b99fe1b982584466320362239c54c9 -> 5f0bc2257df490895e656adc0c5d775716c5e389caa93b8e85bea859ba98ff8a
2025-09-23 09:44:43,811 [ 833934]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (5f0bc2257df490895e656adc0c5d775716c5e389caa93b8e85bea859ba98ff8a) is unknown but expected to be uploaded
2025-09-23 09:44:43,811 [ 833934]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (5f0bc2257df490895e656adc0c5d775716c5e389caa93b8e85bea859ba98ff8a) is unknown but expected to be uploaded
2025-09-23 09:44:43,811 [ 833934]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (5f0bc2257df490895e656adc0c5d775716c5e389caa93b8e85bea859ba98ff8a) is unknown but expected to be uploaded
2025-09-23 09:44:43,811 [ 833934]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (5f0bc2257df490895e656adc0c5d775716c5e389caa93b8e85bea859ba98ff8a) is unknown but expected to be uploaded
2025-09-23 09:44:43,811 [ 833934]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (5f0bc2257df490895e656adc0c5d775716c5e389caa93b8e85bea859ba98ff8a) is unknown but expected to be uploaded
2025-09-23 09:44:43,811 [ 833934]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (5f0bc2257df490895e656adc0c5d775716c5e389caa93b8e85bea859ba98ff8a) is unknown but expected to be uploaded
2025-09-23 09:44:43,811 [ 833934]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (5f0bc2257df490895e656adc0c5d775716c5e389caa93b8e85bea859ba98ff8a) is unknown but expected to be uploaded
2025-09-23 09:44:43,811 [ 833934]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (5f0bc2257df490895e656adc0c5d775716c5e389caa93b8e85bea859ba98ff8a) is unknown but expected to be uploaded
2025-09-23 09:44:43,811 [ 833934]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (5f0bc2257df490895e656adc0c5d775716c5e389caa93b8e85bea859ba98ff8a) is unknown but expected to be uploaded
2025-09-23 09:44:43,811 [ 833934]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (5f0bc2257df490895e656adc0c5d775716c5e389caa93b8e85bea859ba98ff8a) is unknown but expected to be uploaded
2025-09-23 09:44:43,811 [ 833934]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (5f0bc2257df490895e656adc0c5d775716c5e389caa93b8e85bea859ba98ff8a) is unknown but expected to be uploaded
2025-09-23 09:44:43,811 [ 833934]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (5f0bc2257df490895e656adc0c5d775716c5e389caa93b8e85bea859ba98ff8a) is unknown but expected to be uploaded
2025-09-23 09:44:43,811 [ 833934]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (5f0bc2257df490895e656adc0c5d775716c5e389caa93b8e85bea859ba98ff8a) is unknown but expected to be uploaded
2025-09-23 09:44:43,811 [ 833934]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (5f0bc2257df490895e656adc0c5d775716c5e389caa93b8e85bea859ba98ff8a) is unknown but expected to be uploaded
2025-09-23 09:44:43,811 [ 833934]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (5f0bc2257df490895e656adc0c5d775716c5e389caa93b8e85bea859ba98ff8a) is unknown but expected to be uploaded
2025-09-23 09:44:43,811 [ 833934]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (5f0bc2257df490895e656adc0c5d775716c5e389caa93b8e85bea859ba98ff8a) is unknown but expected to be uploaded
2025-09-23 09:44:43,811 [ 833934]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (5f0bc2257df490895e656adc0c5d775716c5e389caa93b8e85bea859ba98ff8a) is unknown but expected to be uploaded
2025-09-23 09:44:43,933 [ 834056]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: a25fa3ea8c84b9645f66c76fe4042c9479b99fe1b982584466320362239c54c9 -> 5f0bc2257df490895e656adc0c5d775716c5e389caa93b8e85bea859ba98ff8a
2025-09-23 09:44:47,944 [ 838067]   WARN - #c.i.e.wsl - Failed to get WSL mount root for Ubuntu: DOS working directory is expected, but got \\wsl.localhost\Ubuntu\home\oni\audio-transcriber
2025-09-23 09:44:48,613 [ 838736]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (5f0bc2257df490895e656adc0c5d775716c5e389caa93b8e85bea859ba98ff8a) is unknown but expected to be uploaded
2025-09-23 09:44:48,613 [ 838736]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (5f0bc2257df490895e656adc0c5d775716c5e389caa93b8e85bea859ba98ff8a) is unknown but expected to be uploaded
2025-09-23 09:44:48,613 [ 838736]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (5f0bc2257df490895e656adc0c5d775716c5e389caa93b8e85bea859ba98ff8a) is unknown but expected to be uploaded
2025-09-23 09:44:57,170 [ 847293]   WARN - #c.i.e.wsl - Failed to get WSL mount root for Ubuntu: DOS working directory is expected, but got \\wsl.localhost\Ubuntu\home\oni\audio-transcriber
2025-09-23 09:44:59,756 [ 849879]   WARN - #com.augmentcode.intellij.sentry.SentryMetadataCollector - Git ls-files command failed with exit code: 128
2025-09-23 09:45:05,608 [ 855731]   WARN - #c.i.e.wsl - Failed to get WSL mount root for Ubuntu: DOS working directory is expected, but got \\wsl.localhost\Ubuntu\home\oni\audio-transcriber
2025-09-23 09:45:09,444 [ 859567]   WARN - #c.i.e.wsl - Failed to get WSL mount root for Ubuntu: DOS working directory is expected, but got \\wsl.localhost\Ubuntu\home\oni\audio-transcriber
2025-09-23 09:45:17,553 [ 867676]   WARN - #c.i.e.wsl - Failed to get WSL mount root for Ubuntu: DOS working directory is expected, but got \\wsl.localhost\Ubuntu\home\oni\audio-transcriber
2025-09-23 09:45:29,737 [ 879860]   WARN - #c.i.e.wsl - Failed to get WSL mount root for Ubuntu: DOS working directory is expected, but got \\wsl.localhost\Ubuntu\home\oni\audio-transcriber
2025-09-23 09:45:40,302 [ 890425]   WARN - #c.i.e.wsl - Failed to get WSL mount root for Ubuntu: DOS working directory is expected, but got \\wsl.localhost\Ubuntu\home\oni\audio-transcriber
2025-09-23 09:45:42,846 [ 892969]   WARN - #c.i.e.wsl - Failed to get WSL mount root for Ubuntu: DOS working directory is expected, but got \\wsl.localhost\Ubuntu\home\oni\audio-transcriber
2025-09-23 09:45:45,064 [ 895187]   WARN - #com.augmentcode.intellij.api.AugmentHttpClient$Companion - Suppressed a frequent exception logged for the 2nd time: Request timeout has expired [url=https://i1.api.augmentcode.com/batch-upload, request_timeout=60000 ms]
2025-09-23 09:45:45,064 [ 895187]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Failed to process upload batch
java.lang.IllegalStateException: Failed to make network call to batch-upload with request ID 79522b54-5f13-4375-9455-d6df26562d35: Request timeout has expired [url=https://i1.api.augmentcode.com/batch-upload, request_timeout=60000 ms]
	at com.augmentcode.intellij.api.AugmentHttpClient.wrapNetworkError(AugmentHttpClient.kt:223)
	at com.augmentcode.intellij.api.AugmentHttpClient.access$wrapNetworkError(AugmentHttpClient.kt:31)
	at com.augmentcode.intellij.api.AugmentHttpClient$post$2.invokeSuspend(AugmentHttpClient.kt:180)
	at kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)
	at kotlinx.coroutines.DispatchedTask.run(DispatchedTask.kt:98)
	at kotlinx.coroutines.scheduling.CoroutineScheduler.runSafely(CoroutineScheduler.kt:613)
	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.executeTask(CoroutineScheduler.kt:1189)
	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.runWorker(CoroutineScheduler.kt:778)
	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.run(CoroutineScheduler.kt:765)
Caused by: io.ktor.client.plugins.HttpRequestTimeoutException: Request timeout has expired [url=https://i1.api.augmentcode.com/batch-upload, request_timeout=60000 ms]
	at io.ktor.client.plugins.HttpTimeout$Plugin$install$1$1$killer$1.invokeSuspend(HttpTimeout.kt:165)
	at kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)
	at kotlinx.coroutines.DispatchedTask.run(DispatchedTask.kt:100)
	at kotlinx.coroutines.internal.SoftLimitedDispatcher$Worker.run(SoftLimitedDispatcher.kt:130)
	at kotlinx.coroutines.scheduling.TaskImpl.run(Tasks.kt:89)
	at kotlinx.coroutines.scheduling.CoroutineScheduler.runSafely(CoroutineScheduler.kt:613)
	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.executeTask(CoroutineScheduler.kt:1183)
	... 2 more
2025-09-23 09:45:45,699 [ 895822]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 5f0bc2257df490895e656adc0c5d775716c5e389caa93b8e85bea859ba98ff8a -> d6cf5679127afe11f8c05b9a214d4b602c3a44ba6ef2a64b2b48b74bbb8524a9
2025-09-23 09:45:46,150 [ 896273]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 5f0bc2257df490895e656adc0c5d775716c5e389caa93b8e85bea859ba98ff8a -> d6cf5679127afe11f8c05b9a214d4b602c3a44ba6ef2a64b2b48b74bbb8524a9
2025-09-23 09:45:46,902 [ 897025]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 5f0bc2257df490895e656adc0c5d775716c5e389caa93b8e85bea859ba98ff8a -> d6cf5679127afe11f8c05b9a214d4b602c3a44ba6ef2a64b2b48b74bbb8524a9
2025-09-23 09:45:47,597 [ 897720]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 5f0bc2257df490895e656adc0c5d775716c5e389caa93b8e85bea859ba98ff8a -> d6cf5679127afe11f8c05b9a214d4b602c3a44ba6ef2a64b2b48b74bbb8524a9
2025-09-23 09:45:48,080 [ 898203]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 5f0bc2257df490895e656adc0c5d775716c5e389caa93b8e85bea859ba98ff8a -> d6cf5679127afe11f8c05b9a214d4b602c3a44ba6ef2a64b2b48b74bbb8524a9
2025-09-23 09:45:48,428 [ 898551]   WARN - #c.i.e.wsl - Failed to get WSL mount root for Ubuntu: DOS working directory is expected, but got \\wsl.localhost\Ubuntu\home\oni\audio-transcriber
2025-09-23 09:45:48,578 [ 898701]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 5f0bc2257df490895e656adc0c5d775716c5e389caa93b8e85bea859ba98ff8a -> d6cf5679127afe11f8c05b9a214d4b602c3a44ba6ef2a64b2b48b74bbb8524a9
2025-09-23 09:45:49,270 [ 899393]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 5f0bc2257df490895e656adc0c5d775716c5e389caa93b8e85bea859ba98ff8a -> ca9d23eafc995729e0a9b64dff5e0e41ab307657e4b018fce82fdc59bc0a1624
2025-09-23 09:45:49,764 [ 899887]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 5f0bc2257df490895e656adc0c5d775716c5e389caa93b8e85bea859ba98ff8a -> ca9d23eafc995729e0a9b64dff5e0e41ab307657e4b018fce82fdc59bc0a1624
2025-09-23 09:45:50,282 [ 900405]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 5f0bc2257df490895e656adc0c5d775716c5e389caa93b8e85bea859ba98ff8a -> ca9d23eafc995729e0a9b64dff5e0e41ab307657e4b018fce82fdc59bc0a1624
2025-09-23 09:45:50,716 [ 900839]   WARN - #c.i.e.wsl - Failed to get WSL mount root for Ubuntu: DOS working directory is expected, but got \\wsl.localhost\Ubuntu\home\oni\audio-transcriber
2025-09-23 09:45:50,949 [ 901072]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: e002cd4ab5d39c52bf4fcc92480e161ef65f422af398bda8865f9e31d337e0ed -> ca9d23eafc995729e0a9b64dff5e0e41ab307657e4b018fce82fdc59bc0a1624
2025-09-23 09:45:51,532 [ 901655]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 3bdcbc6af616b52f9ac95299854ebabc0a81a765a35be9b26b2408443f1d4a7e -> 0cbcf959d301edc8592e433cae35bbc04b4501f748187cf5e56cbaa6525c9ca7
2025-09-23 09:45:52,026 [ 902149]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 272f371700842c5bfc08c88f474ca052cd1250fedc83c5d05fab0bf724c4d35e -> 0cbcf959d301edc8592e433cae35bbc04b4501f748187cf5e56cbaa6525c9ca7
2025-09-23 09:45:52,482 [ 902605]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: d6cf5679127afe11f8c05b9a214d4b602c3a44ba6ef2a64b2b48b74bbb8524a9 -> 0cbcf959d301edc8592e433cae35bbc04b4501f748187cf5e56cbaa6525c9ca7
2025-09-23 09:45:52,983 [ 903106]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 5f0bc2257df490895e656adc0c5d775716c5e389caa93b8e85bea859ba98ff8a -> 0cbcf959d301edc8592e433cae35bbc04b4501f748187cf5e56cbaa6525c9ca7
2025-09-23 09:45:53,661 [ 903784]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 5f0bc2257df490895e656adc0c5d775716c5e389caa93b8e85bea859ba98ff8a -> 0cbcf959d301edc8592e433cae35bbc04b4501f748187cf5e56cbaa6525c9ca7
2025-09-23 09:45:54,124 [ 904247]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 5f0bc2257df490895e656adc0c5d775716c5e389caa93b8e85bea859ba98ff8a -> 0cbcf959d301edc8592e433cae35bbc04b4501f748187cf5e56cbaa6525c9ca7
2025-09-23 09:45:54,647 [ 904770]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 5f0bc2257df490895e656adc0c5d775716c5e389caa93b8e85bea859ba98ff8a -> 0cbcf959d301edc8592e433cae35bbc04b4501f748187cf5e56cbaa6525c9ca7
2025-09-23 09:45:55,454 [ 905577]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 751f3f2d72e1e2a462c2d2e313a496e7d183a067dfa28c92b9b09a2ef9947dbf -> 0cbcf959d301edc8592e433cae35bbc04b4501f748187cf5e56cbaa6525c9ca7
2025-09-23 09:45:56,615 [ 906738]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 5f0bc2257df490895e656adc0c5d775716c5e389caa93b8e85bea859ba98ff8a -> 0cbcf959d301edc8592e433cae35bbc04b4501f748187cf5e56cbaa6525c9ca7
2025-09-23 09:45:57,060 [ 907183]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 5f0bc2257df490895e656adc0c5d775716c5e389caa93b8e85bea859ba98ff8a -> 0cbcf959d301edc8592e433cae35bbc04b4501f748187cf5e56cbaa6525c9ca7
2025-09-23 09:45:57,635 [ 907758]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 3244e8bff06493af0a31dc095673badf6e7402b9a8bb2464c90edc4424b6b358 -> 0cbcf959d301edc8592e433cae35bbc04b4501f748187cf5e56cbaa6525c9ca7
2025-09-23 09:45:58,567 [ 908690]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 1fb6fe01996b6bcf1470d544872ca6472a73cdb0857b386d2919fc68994af774 -> 0cbcf959d301edc8592e433cae35bbc04b4501f748187cf5e56cbaa6525c9ca7
2025-09-23 09:45:59,077 [ 909200]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 5f0bc2257df490895e656adc0c5d775716c5e389caa93b8e85bea859ba98ff8a -> 0cbcf959d301edc8592e433cae35bbc04b4501f748187cf5e56cbaa6525c9ca7
2025-09-23 09:45:59,584 [ 909707]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 5f0bc2257df490895e656adc0c5d775716c5e389caa93b8e85bea859ba98ff8a -> 0cbcf959d301edc8592e433cae35bbc04b4501f748187cf5e56cbaa6525c9ca7
2025-09-23 09:45:59,954 [ 910077]   WARN - #com.augmentcode.intellij.sentry.SentryMetadataCollector - Git ls-files command failed with exit code: 128
2025-09-23 09:46:01,274 [ 911397]   WARN - #c.i.e.wsl - Failed to get WSL mount root for Ubuntu: DOS working directory is expected, but got \\wsl.localhost\Ubuntu\home\oni\audio-transcriber
2025-09-23 09:46:02,227 [ 912350]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 01eb8998b7b848a4cb4f745928fbcda3bfff1b3773c810bbeb97142fc98e848b -> 027c1110d73b080c8f1d29f8b7326ff6ef6bdb66934ace0aebd4d9e6bdcef4f4
2025-09-23 09:46:05,721 [ 915844]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (d6cf5679127afe11f8c05b9a214d4b602c3a44ba6ef2a64b2b48b74bbb8524a9) is unknown but expected to be uploaded
2025-09-23 09:46:05,721 [ 915844]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (d6cf5679127afe11f8c05b9a214d4b602c3a44ba6ef2a64b2b48b74bbb8524a9) is unknown but expected to be uploaded
2025-09-23 09:46:05,721 [ 915844]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (d6cf5679127afe11f8c05b9a214d4b602c3a44ba6ef2a64b2b48b74bbb8524a9) is unknown but expected to be uploaded
2025-09-23 09:46:05,721 [ 915844]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (d6cf5679127afe11f8c05b9a214d4b602c3a44ba6ef2a64b2b48b74bbb8524a9) is unknown but expected to be uploaded
2025-09-23 09:46:05,721 [ 915844]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (d6cf5679127afe11f8c05b9a214d4b602c3a44ba6ef2a64b2b48b74bbb8524a9) is unknown but expected to be uploaded
2025-09-23 09:46:05,721 [ 915844]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (d6cf5679127afe11f8c05b9a214d4b602c3a44ba6ef2a64b2b48b74bbb8524a9) is unknown but expected to be uploaded
2025-09-23 09:46:05,721 [ 915844]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (ca9d23eafc995729e0a9b64dff5e0e41ab307657e4b018fce82fdc59bc0a1624) is unknown but expected to be uploaded
2025-09-23 09:46:05,721 [ 915844]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (ca9d23eafc995729e0a9b64dff5e0e41ab307657e4b018fce82fdc59bc0a1624) is unknown but expected to be uploaded
2025-09-23 09:46:05,721 [ 915844]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (ca9d23eafc995729e0a9b64dff5e0e41ab307657e4b018fce82fdc59bc0a1624) is unknown but expected to be uploaded
2025-09-23 09:46:05,721 [ 915844]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (ca9d23eafc995729e0a9b64dff5e0e41ab307657e4b018fce82fdc59bc0a1624) is unknown but expected to be uploaded
2025-09-23 09:46:05,721 [ 915844]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (ca9d23eafc995729e0a9b64dff5e0e41ab307657e4b018fce82fdc59bc0a1624) is unknown but expected to be uploaded
2025-09-23 09:46:06,789 [ 916912]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: d6cf5679127afe11f8c05b9a214d4b602c3a44ba6ef2a64b2b48b74bbb8524a9 -> 027c1110d73b080c8f1d29f8b7326ff6ef6bdb66934ace0aebd4d9e6bdcef4f4
2025-09-23 09:46:07,488 [ 917611]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: d6cf5679127afe11f8c05b9a214d4b602c3a44ba6ef2a64b2b48b74bbb8524a9 -> 027c1110d73b080c8f1d29f8b7326ff6ef6bdb66934ace0aebd4d9e6bdcef4f4
2025-09-23 09:46:08,011 [ 918134]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: d6cf5679127afe11f8c05b9a214d4b602c3a44ba6ef2a64b2b48b74bbb8524a9 -> 027c1110d73b080c8f1d29f8b7326ff6ef6bdb66934ace0aebd4d9e6bdcef4f4
2025-09-23 09:46:08,524 [ 918647]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: ca9d23eafc995729e0a9b64dff5e0e41ab307657e4b018fce82fdc59bc0a1624 -> 027c1110d73b080c8f1d29f8b7326ff6ef6bdb66934ace0aebd4d9e6bdcef4f4
2025-09-23 09:46:09,244 [ 919367]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: ca9d23eafc995729e0a9b64dff5e0e41ab307657e4b018fce82fdc59bc0a1624 -> 027c1110d73b080c8f1d29f8b7326ff6ef6bdb66934ace0aebd4d9e6bdcef4f4
2025-09-23 09:46:09,713 [ 919836]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: ca9d23eafc995729e0a9b64dff5e0e41ab307657e4b018fce82fdc59bc0a1624 -> 027c1110d73b080c8f1d29f8b7326ff6ef6bdb66934ace0aebd4d9e6bdcef4f4
2025-09-23 09:46:10,457 [ 920580]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: d6cf5679127afe11f8c05b9a214d4b602c3a44ba6ef2a64b2b48b74bbb8524a9 -> 027c1110d73b080c8f1d29f8b7326ff6ef6bdb66934ace0aebd4d9e6bdcef4f4
2025-09-23 09:46:10,930 [ 921053]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: ca9d23eafc995729e0a9b64dff5e0e41ab307657e4b018fce82fdc59bc0a1624 -> 027c1110d73b080c8f1d29f8b7326ff6ef6bdb66934ace0aebd4d9e6bdcef4f4
2025-09-23 09:46:11,420 [ 921543]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: d6cf5679127afe11f8c05b9a214d4b602c3a44ba6ef2a64b2b48b74bbb8524a9 -> 027c1110d73b080c8f1d29f8b7326ff6ef6bdb66934ace0aebd4d9e6bdcef4f4
2025-09-23 09:46:12,078 [ 922201]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: ca9d23eafc995729e0a9b64dff5e0e41ab307657e4b018fce82fdc59bc0a1624 -> 027c1110d73b080c8f1d29f8b7326ff6ef6bdb66934ace0aebd4d9e6bdcef4f4
2025-09-23 09:46:12,577 [ 922700]   WARN - #c.i.e.wsl - Failed to get WSL mount root for Ubuntu: DOS working directory is expected, but got \\wsl.localhost\Ubuntu\home\oni\audio-transcriber
2025-09-23 09:46:13,804 [ 923927]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: d6cf5679127afe11f8c05b9a214d4b602c3a44ba6ef2a64b2b48b74bbb8524a9 -> b27dc84adede086601c93ccf5d324f49e4f5624169217915058f84d1bcefbfd8
2025-09-23 09:46:17,190 [ 927313]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (0cbcf959d301edc8592e433cae35bbc04b4501f748187cf5e56cbaa6525c9ca7) is unknown but expected to be uploaded
2025-09-23 09:46:17,190 [ 927313]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (0cbcf959d301edc8592e433cae35bbc04b4501f748187cf5e56cbaa6525c9ca7) is unknown but expected to be uploaded
2025-09-23 09:46:17,190 [ 927313]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (0cbcf959d301edc8592e433cae35bbc04b4501f748187cf5e56cbaa6525c9ca7) is unknown but expected to be uploaded
2025-09-23 09:46:17,190 [ 927313]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (0cbcf959d301edc8592e433cae35bbc04b4501f748187cf5e56cbaa6525c9ca7) is unknown but expected to be uploaded
2025-09-23 09:46:17,190 [ 927313]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (0cbcf959d301edc8592e433cae35bbc04b4501f748187cf5e56cbaa6525c9ca7) is unknown but expected to be uploaded
2025-09-23 09:46:17,190 [ 927313]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (0cbcf959d301edc8592e433cae35bbc04b4501f748187cf5e56cbaa6525c9ca7) is unknown but expected to be uploaded
2025-09-23 09:46:17,190 [ 927313]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (0cbcf959d301edc8592e433cae35bbc04b4501f748187cf5e56cbaa6525c9ca7) is unknown but expected to be uploaded
2025-09-23 09:46:17,190 [ 927313]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (0cbcf959d301edc8592e433cae35bbc04b4501f748187cf5e56cbaa6525c9ca7) is unknown but expected to be uploaded
2025-09-23 09:46:17,190 [ 927313]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (0cbcf959d301edc8592e433cae35bbc04b4501f748187cf5e56cbaa6525c9ca7) is unknown but expected to be uploaded
2025-09-23 09:46:17,190 [ 927313]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (0cbcf959d301edc8592e433cae35bbc04b4501f748187cf5e56cbaa6525c9ca7) is unknown but expected to be uploaded
2025-09-23 09:46:17,190 [ 927313]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (0cbcf959d301edc8592e433cae35bbc04b4501f748187cf5e56cbaa6525c9ca7) is unknown but expected to be uploaded
2025-09-23 09:46:17,190 [ 927313]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (0cbcf959d301edc8592e433cae35bbc04b4501f748187cf5e56cbaa6525c9ca7) is unknown but expected to be uploaded
2025-09-23 09:46:17,190 [ 927313]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (0cbcf959d301edc8592e433cae35bbc04b4501f748187cf5e56cbaa6525c9ca7) is unknown but expected to be uploaded
2025-09-23 09:46:17,190 [ 927313]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (0cbcf959d301edc8592e433cae35bbc04b4501f748187cf5e56cbaa6525c9ca7) is unknown but expected to be uploaded
2025-09-23 09:46:17,190 [ 927313]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (0cbcf959d301edc8592e433cae35bbc04b4501f748187cf5e56cbaa6525c9ca7) is unknown but expected to be uploaded
2025-09-23 09:46:17,190 [ 927313]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (027c1110d73b080c8f1d29f8b7326ff6ef6bdb66934ace0aebd4d9e6bdcef4f4) is unknown but expected to be uploaded
2025-09-23 09:46:17,190 [ 927313]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (027c1110d73b080c8f1d29f8b7326ff6ef6bdb66934ace0aebd4d9e6bdcef4f4) is unknown but expected to be uploaded
2025-09-23 09:46:17,190 [ 927313]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (027c1110d73b080c8f1d29f8b7326ff6ef6bdb66934ace0aebd4d9e6bdcef4f4) is unknown but expected to be uploaded
2025-09-23 09:46:17,190 [ 927313]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (027c1110d73b080c8f1d29f8b7326ff6ef6bdb66934ace0aebd4d9e6bdcef4f4) is unknown but expected to be uploaded
2025-09-23 09:46:17,190 [ 927313]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (027c1110d73b080c8f1d29f8b7326ff6ef6bdb66934ace0aebd4d9e6bdcef4f4) is unknown but expected to be uploaded
2025-09-23 09:46:17,190 [ 927313]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (027c1110d73b080c8f1d29f8b7326ff6ef6bdb66934ace0aebd4d9e6bdcef4f4) is unknown but expected to be uploaded
2025-09-23 09:46:17,190 [ 927313]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (027c1110d73b080c8f1d29f8b7326ff6ef6bdb66934ace0aebd4d9e6bdcef4f4) is unknown but expected to be uploaded
2025-09-23 09:46:17,190 [ 927313]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (027c1110d73b080c8f1d29f8b7326ff6ef6bdb66934ace0aebd4d9e6bdcef4f4) is unknown but expected to be uploaded
2025-09-23 09:46:17,190 [ 927313]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (027c1110d73b080c8f1d29f8b7326ff6ef6bdb66934ace0aebd4d9e6bdcef4f4) is unknown but expected to be uploaded
2025-09-23 09:46:17,190 [ 927313]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (027c1110d73b080c8f1d29f8b7326ff6ef6bdb66934ace0aebd4d9e6bdcef4f4) is unknown but expected to be uploaded
2025-09-23 09:46:17,190 [ 927313]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (027c1110d73b080c8f1d29f8b7326ff6ef6bdb66934ace0aebd4d9e6bdcef4f4) is unknown but expected to be uploaded
2025-09-23 09:46:17,190 [ 927313]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (027c1110d73b080c8f1d29f8b7326ff6ef6bdb66934ace0aebd4d9e6bdcef4f4) is unknown but expected to be uploaded
2025-09-23 09:46:18,208 [ 928331]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 0cbcf959d301edc8592e433cae35bbc04b4501f748187cf5e56cbaa6525c9ca7 -> b27dc84adede086601c93ccf5d324f49e4f5624169217915058f84d1bcefbfd8
2025-09-23 09:46:18,958 [ 929081]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 0cbcf959d301edc8592e433cae35bbc04b4501f748187cf5e56cbaa6525c9ca7 -> b27dc84adede086601c93ccf5d324f49e4f5624169217915058f84d1bcefbfd8
2025-09-23 09:46:19,483 [ 929606]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 0cbcf959d301edc8592e433cae35bbc04b4501f748187cf5e56cbaa6525c9ca7 -> b27dc84adede086601c93ccf5d324f49e4f5624169217915058f84d1bcefbfd8
2025-09-23 09:46:20,146 [ 930269]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 0cbcf959d301edc8592e433cae35bbc04b4501f748187cf5e56cbaa6525c9ca7 -> b27dc84adede086601c93ccf5d324f49e4f5624169217915058f84d1bcefbfd8
2025-09-23 09:46:20,963 [ 931086]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 0cbcf959d301edc8592e433cae35bbc04b4501f748187cf5e56cbaa6525c9ca7 -> b27dc84adede086601c93ccf5d324f49e4f5624169217915058f84d1bcefbfd8
2025-09-23 09:46:21,462 [ 931585]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 0cbcf959d301edc8592e433cae35bbc04b4501f748187cf5e56cbaa6525c9ca7 -> b27dc84adede086601c93ccf5d324f49e4f5624169217915058f84d1bcefbfd8
2025-09-23 09:46:21,929 [ 932052]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 0cbcf959d301edc8592e433cae35bbc04b4501f748187cf5e56cbaa6525c9ca7 -> b27dc84adede086601c93ccf5d324f49e4f5624169217915058f84d1bcefbfd8
2025-09-23 09:46:22,420 [ 932543]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 0cbcf959d301edc8592e433cae35bbc04b4501f748187cf5e56cbaa6525c9ca7 -> b27dc84adede086601c93ccf5d324f49e4f5624169217915058f84d1bcefbfd8
2025-09-23 09:46:23,079 [ 933202]   WARN - #c.i.e.wsl - Failed to get WSL mount root for Ubuntu: DOS working directory is expected, but got \\wsl.localhost\Ubuntu\home\oni\audio-transcriber
2025-09-23 09:46:23,318 [ 933441]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 027c1110d73b080c8f1d29f8b7326ff6ef6bdb66934ace0aebd4d9e6bdcef4f4 -> b27dc84adede086601c93ccf5d324f49e4f5624169217915058f84d1bcefbfd8
2025-09-23 09:46:24,059 [ 934182]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 027c1110d73b080c8f1d29f8b7326ff6ef6bdb66934ace0aebd4d9e6bdcef4f4 -> 55c48aaa002ca56e05dd4c55245e5d9dbab12f2945169717bbaf1631645c6b9b
2025-09-23 09:46:24,717 [ 934840]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 027c1110d73b080c8f1d29f8b7326ff6ef6bdb66934ace0aebd4d9e6bdcef4f4 -> 55c48aaa002ca56e05dd4c55245e5d9dbab12f2945169717bbaf1631645c6b9b
2025-09-23 09:46:25,064 [ 935187]   WARN - #c.i.e.wsl - Failed to get WSL mount root for Ubuntu: DOS working directory is expected, but got \\wsl.localhost\Ubuntu\home\oni\audio-transcriber
2025-09-23 09:46:25,273 [ 935396]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 027c1110d73b080c8f1d29f8b7326ff6ef6bdb66934ace0aebd4d9e6bdcef4f4 -> 55c48aaa002ca56e05dd4c55245e5d9dbab12f2945169717bbaf1631645c6b9b
2025-09-23 09:46:25,938 [ 936061]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 027c1110d73b080c8f1d29f8b7326ff6ef6bdb66934ace0aebd4d9e6bdcef4f4 -> 7ac6366ace4c775cc7425c8451fe3bfb9394d9b7c699dc8f8f12f908c63cc40f
2025-09-23 09:46:26,607 [ 936730]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 027c1110d73b080c8f1d29f8b7326ff6ef6bdb66934ace0aebd4d9e6bdcef4f4 -> 7ac6366ace4c775cc7425c8451fe3bfb9394d9b7c699dc8f8f12f908c63cc40f
2025-09-23 09:46:27,159 [ 937282]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 0cbcf959d301edc8592e433cae35bbc04b4501f748187cf5e56cbaa6525c9ca7 -> 7ac6366ace4c775cc7425c8451fe3bfb9394d9b7c699dc8f8f12f908c63cc40f
2025-09-23 09:46:27,677 [ 937800]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 0cbcf959d301edc8592e433cae35bbc04b4501f748187cf5e56cbaa6525c9ca7 -> 7ac6366ace4c775cc7425c8451fe3bfb9394d9b7c699dc8f8f12f908c63cc40f
2025-09-23 09:46:28,191 [ 938314]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 0cbcf959d301edc8592e433cae35bbc04b4501f748187cf5e56cbaa6525c9ca7 -> 7ac6366ace4c775cc7425c8451fe3bfb9394d9b7c699dc8f8f12f908c63cc40f
2025-09-23 09:46:28,734 [ 938857]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 027c1110d73b080c8f1d29f8b7326ff6ef6bdb66934ace0aebd4d9e6bdcef4f4 -> 7ac6366ace4c775cc7425c8451fe3bfb9394d9b7c699dc8f8f12f908c63cc40f
2025-09-23 09:46:29,197 [ 939320]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 027c1110d73b080c8f1d29f8b7326ff6ef6bdb66934ace0aebd4d9e6bdcef4f4 -> 7ac6366ace4c775cc7425c8451fe3bfb9394d9b7c699dc8f8f12f908c63cc40f
2025-09-23 09:46:29,841 [ 939964]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 027c1110d73b080c8f1d29f8b7326ff6ef6bdb66934ace0aebd4d9e6bdcef4f4 -> 7ac6366ace4c775cc7425c8451fe3bfb9394d9b7c699dc8f8f12f908c63cc40f
2025-09-23 09:46:30,840 [ 940963]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 0cbcf959d301edc8592e433cae35bbc04b4501f748187cf5e56cbaa6525c9ca7 -> 7ac6366ace4c775cc7425c8451fe3bfb9394d9b7c699dc8f8f12f908c63cc40f
2025-09-23 09:46:31,289 [ 941412]   WARN - #com.augmentcode.intellij.chat.ChatWebviewMessageClient$Companion - Workspace guidelines are empty, not including in chat request. 
2025-09-23 09:46:31,315 [ 941438]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 0cbcf959d301edc8592e433cae35bbc04b4501f748187cf5e56cbaa6525c9ca7 -> 7ac6366ace4c775cc7425c8451fe3bfb9394d9b7c699dc8f8f12f908c63cc40f
2025-09-23 09:46:31,821 [ 941944]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 027c1110d73b080c8f1d29f8b7326ff6ef6bdb66934ace0aebd4d9e6bdcef4f4 -> 7ac6366ace4c775cc7425c8451fe3bfb9394d9b7c699dc8f8f12f908c63cc40f
2025-09-23 09:46:32,440 [ 942563]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 0cbcf959d301edc8592e433cae35bbc04b4501f748187cf5e56cbaa6525c9ca7 -> 7ac6366ace4c775cc7425c8451fe3bfb9394d9b7c699dc8f8f12f908c63cc40f
2025-09-23 09:46:33,199 [ 943322]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 027c1110d73b080c8f1d29f8b7326ff6ef6bdb66934ace0aebd4d9e6bdcef4f4 -> 7ac6366ace4c775cc7425c8451fe3bfb9394d9b7c699dc8f8f12f908c63cc40f
2025-09-23 09:46:34,191 [ 944314]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 0cbcf959d301edc8592e433cae35bbc04b4501f748187cf5e56cbaa6525c9ca7 -> 7ac6366ace4c775cc7425c8451fe3bfb9394d9b7c699dc8f8f12f908c63cc40f
2025-09-23 09:46:34,821 [ 944944]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: No exchange with this request ID found. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:46:35,238 [ 945361]   WARN - #c.i.e.wsl - Failed to get WSL mount root for Ubuntu: DOS working directory is expected, but got \\wsl.localhost\Ubuntu\home\oni\audio-transcriber
2025-09-23 09:46:36,376 [ 946499]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 027c1110d73b080c8f1d29f8b7326ff6ef6bdb66934ace0aebd4d9e6bdcef4f4 -> 2b3d29920bf4b9f4b3a2881705188521c10f9006e088c7d584120c8d23aae0e1
2025-09-23 09:46:37,815 [ 947938]   WARN - #c.i.e.wsl - Failed to get WSL mount root for Ubuntu: DOS working directory is expected, but got \\wsl.localhost\Ubuntu\home\oni\audio-transcriber
2025-09-23 09:46:38,035 [ 948158]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: IntersectionDisplay context not found.  Call setIntersectionContext first. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:46:38,076 [ 948199]   WARN - #com.augmentcode.intellij.chat.ChatWebviewMessageClient$Companion - Workspace guidelines are empty, not including in chat request. 
2025-09-23 09:46:38,448 [ 948571]   WARN - #com.augmentcode.intellij.webviews.AbstractWebviewMessagingService - Failed to parse webview message as protobuf.
Exception: Missing type url when parsing: {"type":"can-show-terminal","data":{"command":"npm install @tailwindcss/postcss"}}
Message: {"type":"async-wrapper","requestId":"39a696cf-d40f-4c4c-acfa-09e987633498","error":null,"baseMsg":{"type":"can-show-terminal","data":{"command":"npm install @tailwindcss/postcss"}},"destination":"host"}
2025-09-23 09:46:38,449 [ 948572]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_ERROR]: Failed to check if terminal can be shown: Error: Unable to parse webview message: {"type":"async-wrapper","requestId":"39a696cf-d40f-4c4c-acfa-09e987633498","error":null,"baseMsg":{"type":"can-show-terminal","data":{"command":"npm install @tailwindcss/postcss"}},"destination":"host"} [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:46:38,798 [ 948921]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: No exchange with this request ID found. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:46:39,389 [ 949512]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 2b3d29920bf4b9f4b3a2881705188521c10f9006e088c7d584120c8d23aae0e1 -> a2b16b1405b9345f738970738920ab7e9e4fcae772bfbf76e7d8227c21d75e3e
2025-09-23 09:46:39,575 [ 949698]   WARN - #c.i.e.wsl - Failed to get WSL mount root for Ubuntu: DOS working directory is expected, but got \\wsl.localhost\Ubuntu\home\oni\audio-transcriber
2025-09-23 09:46:39,813 [ 949936]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (55c48aaa002ca56e05dd4c55245e5d9dbab12f2945169717bbaf1631645c6b9b) is unknown but expected to be uploaded
2025-09-23 09:46:39,813 [ 949936]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (55c48aaa002ca56e05dd4c55245e5d9dbab12f2945169717bbaf1631645c6b9b) is unknown but expected to be uploaded
2025-09-23 09:46:39,813 [ 949936]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (55c48aaa002ca56e05dd4c55245e5d9dbab12f2945169717bbaf1631645c6b9b) is unknown but expected to be uploaded
2025-09-23 09:46:39,813 [ 949936]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (7ac6366ace4c775cc7425c8451fe3bfb9394d9b7c699dc8f8f12f908c63cc40f) is unknown but expected to be uploaded
2025-09-23 09:46:39,813 [ 949936]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (7ac6366ace4c775cc7425c8451fe3bfb9394d9b7c699dc8f8f12f908c63cc40f) is unknown but expected to be uploaded
2025-09-23 09:46:39,813 [ 949936]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (7ac6366ace4c775cc7425c8451fe3bfb9394d9b7c699dc8f8f12f908c63cc40f) is unknown but expected to be uploaded
2025-09-23 09:46:39,813 [ 949936]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (7ac6366ace4c775cc7425c8451fe3bfb9394d9b7c699dc8f8f12f908c63cc40f) is unknown but expected to be uploaded
2025-09-23 09:46:39,813 [ 949936]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (7ac6366ace4c775cc7425c8451fe3bfb9394d9b7c699dc8f8f12f908c63cc40f) is unknown but expected to be uploaded
2025-09-23 09:46:39,813 [ 949936]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (7ac6366ace4c775cc7425c8451fe3bfb9394d9b7c699dc8f8f12f908c63cc40f) is unknown but expected to be uploaded
2025-09-23 09:46:39,813 [ 949936]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (7ac6366ace4c775cc7425c8451fe3bfb9394d9b7c699dc8f8f12f908c63cc40f) is unknown but expected to be uploaded
2025-09-23 09:46:39,813 [ 949936]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (7ac6366ace4c775cc7425c8451fe3bfb9394d9b7c699dc8f8f12f908c63cc40f) is unknown but expected to be uploaded
2025-09-23 09:46:39,813 [ 949936]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (7ac6366ace4c775cc7425c8451fe3bfb9394d9b7c699dc8f8f12f908c63cc40f) is unknown but expected to be uploaded
2025-09-23 09:46:39,813 [ 949936]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (7ac6366ace4c775cc7425c8451fe3bfb9394d9b7c699dc8f8f12f908c63cc40f) is unknown but expected to be uploaded
2025-09-23 09:46:39,813 [ 949936]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (7ac6366ace4c775cc7425c8451fe3bfb9394d9b7c699dc8f8f12f908c63cc40f) is unknown but expected to be uploaded
2025-09-23 09:46:39,813 [ 949936]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (7ac6366ace4c775cc7425c8451fe3bfb9394d9b7c699dc8f8f12f908c63cc40f) is unknown but expected to be uploaded
2025-09-23 09:46:39,813 [ 949936]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (7ac6366ace4c775cc7425c8451fe3bfb9394d9b7c699dc8f8f12f908c63cc40f) is unknown but expected to be uploaded
2025-09-23 09:46:39,813 [ 949936]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (7ac6366ace4c775cc7425c8451fe3bfb9394d9b7c699dc8f8f12f908c63cc40f) is unknown but expected to be uploaded
2025-09-23 09:46:39,813 [ 949936]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (7ac6366ace4c775cc7425c8451fe3bfb9394d9b7c699dc8f8f12f908c63cc40f) is unknown but expected to be uploaded
2025-09-23 09:46:40,877 [ 951000]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: a2b16b1405b9345f738970738920ab7e9e4fcae772bfbf76e7d8227c21d75e3e -> f5cfa3ee1396158bc7a667135065bbdba208592d7019f458e52ca733e1438abe
2025-09-23 09:46:41,217 [ 951340]   WARN - #c.i.e.wsl - Failed to get WSL mount root for Ubuntu: DOS working directory is expected, but got \\wsl.localhost\Ubuntu\home\oni\audio-transcriber
2025-09-23 09:46:41,409 [ 951532]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 55c48aaa002ca56e05dd4c55245e5d9dbab12f2945169717bbaf1631645c6b9b -> f5cfa3ee1396158bc7a667135065bbdba208592d7019f458e52ca733e1438abe
2025-09-23 09:46:42,047 [ 952170]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 7ac6366ace4c775cc7425c8451fe3bfb9394d9b7c699dc8f8f12f908c63cc40f -> e30a8884e6e4526615535c812331df74efef2ee4918eaa43b2be2dcf23b6303d
2025-09-23 09:46:42,526 [ 952649]   WARN - #com.augmentcode.intellij.webviews.AbstractWebviewMessagingService - Failed to parse webview message as protobuf.
Exception: Missing type url when parsing: {"type":"can-show-terminal","data":{"command":"npm install @tailwindcss/postcss"}}
Message: {"type":"async-wrapper","requestId":"4a9a77d2-a9b5-4016-b117-441cf43d47d0","error":null,"baseMsg":{"type":"can-show-terminal","data":{"command":"npm install @tailwindcss/postcss"}},"destination":"host"}
2025-09-23 09:46:42,554 [ 952677]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_ERROR]: Failed to check if terminal can be shown: Error: Unable to parse webview message: {"type":"async-wrapper","requestId":"4a9a77d2-a9b5-4016-b117-441cf43d47d0","error":null,"baseMsg":{"type":"can-show-terminal","data":{"command":"npm install @tailwindcss/postcss"}},"destination":"host"} [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:46:43,021 [ 953144]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 7ac6366ace4c775cc7425c8451fe3bfb9394d9b7c699dc8f8f12f908c63cc40f -> e30a8884e6e4526615535c812331df74efef2ee4918eaa43b2be2dcf23b6303d
2025-09-23 09:46:43,643 [ 953766]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 7ac6366ace4c775cc7425c8451fe3bfb9394d9b7c699dc8f8f12f908c63cc40f -> e30a8884e6e4526615535c812331df74efef2ee4918eaa43b2be2dcf23b6303d
2025-09-23 09:46:44,192 [ 954315]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 7ac6366ace4c775cc7425c8451fe3bfb9394d9b7c699dc8f8f12f908c63cc40f -> e30a8884e6e4526615535c812331df74efef2ee4918eaa43b2be2dcf23b6303d
2025-09-23 09:46:44,677 [ 954800]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 7ac6366ace4c775cc7425c8451fe3bfb9394d9b7c699dc8f8f12f908c63cc40f -> e30a8884e6e4526615535c812331df74efef2ee4918eaa43b2be2dcf23b6303d
2025-09-23 09:46:45,409 [ 955532]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 7ac6366ace4c775cc7425c8451fe3bfb9394d9b7c699dc8f8f12f908c63cc40f -> e30a8884e6e4526615535c812331df74efef2ee4918eaa43b2be2dcf23b6303d
2025-09-23 09:46:45,927 [ 956050]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 7ac6366ace4c775cc7425c8451fe3bfb9394d9b7c699dc8f8f12f908c63cc40f -> e30a8884e6e4526615535c812331df74efef2ee4918eaa43b2be2dcf23b6303d
2025-09-23 09:46:46,756 [ 956879]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 7ac6366ace4c775cc7425c8451fe3bfb9394d9b7c699dc8f8f12f908c63cc40f -> e30a8884e6e4526615535c812331df74efef2ee4918eaa43b2be2dcf23b6303d
2025-09-23 09:46:47,445 [ 957568]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 55c48aaa002ca56e05dd4c55245e5d9dbab12f2945169717bbaf1631645c6b9b -> e30a8884e6e4526615535c812331df74efef2ee4918eaa43b2be2dcf23b6303d
2025-09-23 09:46:48,517 [ 958640]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 7ac6366ace4c775cc7425c8451fe3bfb9394d9b7c699dc8f8f12f908c63cc40f -> e30a8884e6e4526615535c812331df74efef2ee4918eaa43b2be2dcf23b6303d
2025-09-23 09:46:49,195 [ 959318]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 7ac6366ace4c775cc7425c8451fe3bfb9394d9b7c699dc8f8f12f908c63cc40f -> e30a8884e6e4526615535c812331df74efef2ee4918eaa43b2be2dcf23b6303d
2025-09-23 09:46:49,799 [ 959922]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 7ac6366ace4c775cc7425c8451fe3bfb9394d9b7c699dc8f8f12f908c63cc40f -> e30a8884e6e4526615535c812331df74efef2ee4918eaa43b2be2dcf23b6303d
2025-09-23 09:46:50,262 [ 960385]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 7ac6366ace4c775cc7425c8451fe3bfb9394d9b7c699dc8f8f12f908c63cc40f -> e30a8884e6e4526615535c812331df74efef2ee4918eaa43b2be2dcf23b6303d
2025-09-23 09:46:50,829 [ 960952]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 7ac6366ace4c775cc7425c8451fe3bfb9394d9b7c699dc8f8f12f908c63cc40f -> e30a8884e6e4526615535c812331df74efef2ee4918eaa43b2be2dcf23b6303d
2025-09-23 09:46:51,561 [ 961684]   WARN - #c.i.e.wsl - Failed to get WSL mount root for Ubuntu: DOS working directory is expected, but got \\wsl.localhost\Ubuntu\home\oni\audio-transcriber
2025-09-23 09:46:51,603 [ 961726]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 7ac6366ace4c775cc7425c8451fe3bfb9394d9b7c699dc8f8f12f908c63cc40f -> e30a8884e6e4526615535c812331df74efef2ee4918eaa43b2be2dcf23b6303d
2025-09-23 09:46:52,114 [ 962237]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 55c48aaa002ca56e05dd4c55245e5d9dbab12f2945169717bbaf1631645c6b9b -> e30a8884e6e4526615535c812331df74efef2ee4918eaa43b2be2dcf23b6303d
2025-09-23 09:46:54,710 [ 964833] SEVERE - com.jediterm.terminal.model.TerminalTextBuffer - Attempt to get line out of bounds: 11 >= 11
2025-09-23 09:46:54,710 [ 964833] SEVERE - com.jediterm.terminal.model.TerminalTextBuffer - Attempt to get line out of bounds: 11 >= 11
2025-09-23 09:46:54,824 [ 964947]   WARN - #com.augmentcode.intellij.chat.ChatWebviewMessageClient$Companion - Workspace guidelines are empty, not including in chat request. 
2025-09-23 09:46:55,506 [ 965629]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (a2b16b1405b9345f738970738920ab7e9e4fcae772bfbf76e7d8227c21d75e3e) is unknown but expected to be uploaded
2025-09-23 09:46:55,506 [ 965629]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (a2b16b1405b9345f738970738920ab7e9e4fcae772bfbf76e7d8227c21d75e3e) is unknown but expected to be uploaded
2025-09-23 09:46:55,506 [ 965629]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (f5cfa3ee1396158bc7a667135065bbdba208592d7019f458e52ca733e1438abe) is unknown but expected to be uploaded
2025-09-23 09:46:55,506 [ 965629]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (f5cfa3ee1396158bc7a667135065bbdba208592d7019f458e52ca733e1438abe) is unknown but expected to be uploaded
2025-09-23 09:46:55,578 [ 965701]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 7ac6366ace4c775cc7425c8451fe3bfb9394d9b7c699dc8f8f12f908c63cc40f -> e30a8884e6e4526615535c812331df74efef2ee4918eaa43b2be2dcf23b6303d
2025-09-23 09:46:56,077 [ 966200]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: a2b16b1405b9345f738970738920ab7e9e4fcae772bfbf76e7d8227c21d75e3e -> e30a8884e6e4526615535c812331df74efef2ee4918eaa43b2be2dcf23b6303d
2025-09-23 09:46:56,077 [ 966200]   WARN - #c.i.e.wsl - Failed to get WSL mount root for Ubuntu: DOS working directory is expected, but got \\wsl.localhost\Ubuntu\home\oni\audio-transcriber
2025-09-23 09:46:56,665 [ 966788]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: f5cfa3ee1396158bc7a667135065bbdba208592d7019f458e52ca733e1438abe -> 4e1f56736021a89085c344e3137d04eb0ac881cee490ae9eb4261d0c91dbc850
2025-09-23 09:46:57,214 [ 967337]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: a2b16b1405b9345f738970738920ab7e9e4fcae772bfbf76e7d8227c21d75e3e -> 4e1f56736021a89085c344e3137d04eb0ac881cee490ae9eb4261d0c91dbc850
2025-09-23 09:46:57,780 [ 967903]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: f5cfa3ee1396158bc7a667135065bbdba208592d7019f458e52ca733e1438abe -> 4e1f56736021a89085c344e3137d04eb0ac881cee490ae9eb4261d0c91dbc850
2025-09-23 09:46:58,126 [ 968249]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: No exchange with this request ID found. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:47:00,180 [ 970303]   WARN - #com.augmentcode.intellij.sentry.SentryMetadataCollector - Git ls-files command failed with exit code: 128
2025-09-23 09:47:01,179 [ 971302]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (e30a8884e6e4526615535c812331df74efef2ee4918eaa43b2be2dcf23b6303d) is unknown but expected to be uploaded
2025-09-23 09:47:01,180 [ 971303]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (e30a8884e6e4526615535c812331df74efef2ee4918eaa43b2be2dcf23b6303d) is unknown but expected to be uploaded
2025-09-23 09:47:01,180 [ 971303]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (e30a8884e6e4526615535c812331df74efef2ee4918eaa43b2be2dcf23b6303d) is unknown but expected to be uploaded
2025-09-23 09:47:01,180 [ 971303]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (e30a8884e6e4526615535c812331df74efef2ee4918eaa43b2be2dcf23b6303d) is unknown but expected to be uploaded
2025-09-23 09:47:01,180 [ 971303]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (e30a8884e6e4526615535c812331df74efef2ee4918eaa43b2be2dcf23b6303d) is unknown but expected to be uploaded
2025-09-23 09:47:01,180 [ 971303]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (e30a8884e6e4526615535c812331df74efef2ee4918eaa43b2be2dcf23b6303d) is unknown but expected to be uploaded
2025-09-23 09:47:01,180 [ 971303]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (e30a8884e6e4526615535c812331df74efef2ee4918eaa43b2be2dcf23b6303d) is unknown but expected to be uploaded
2025-09-23 09:47:01,180 [ 971303]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (e30a8884e6e4526615535c812331df74efef2ee4918eaa43b2be2dcf23b6303d) is unknown but expected to be uploaded
2025-09-23 09:47:01,180 [ 971303]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (e30a8884e6e4526615535c812331df74efef2ee4918eaa43b2be2dcf23b6303d) is unknown but expected to be uploaded
2025-09-23 09:47:01,180 [ 971303]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (e30a8884e6e4526615535c812331df74efef2ee4918eaa43b2be2dcf23b6303d) is unknown but expected to be uploaded
2025-09-23 09:47:01,180 [ 971303]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (e30a8884e6e4526615535c812331df74efef2ee4918eaa43b2be2dcf23b6303d) is unknown but expected to be uploaded
2025-09-23 09:47:01,180 [ 971303]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (e30a8884e6e4526615535c812331df74efef2ee4918eaa43b2be2dcf23b6303d) is unknown but expected to be uploaded
2025-09-23 09:47:01,180 [ 971303]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (e30a8884e6e4526615535c812331df74efef2ee4918eaa43b2be2dcf23b6303d) is unknown but expected to be uploaded
2025-09-23 09:47:01,180 [ 971303]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (e30a8884e6e4526615535c812331df74efef2ee4918eaa43b2be2dcf23b6303d) is unknown but expected to be uploaded
2025-09-23 09:47:01,180 [ 971303]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (e30a8884e6e4526615535c812331df74efef2ee4918eaa43b2be2dcf23b6303d) is unknown but expected to be uploaded
2025-09-23 09:47:01,180 [ 971303]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (e30a8884e6e4526615535c812331df74efef2ee4918eaa43b2be2dcf23b6303d) is unknown but expected to be uploaded
2025-09-23 09:47:01,180 [ 971303]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (e30a8884e6e4526615535c812331df74efef2ee4918eaa43b2be2dcf23b6303d) is unknown but expected to be uploaded
2025-09-23 09:47:01,180 [ 971303]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (e30a8884e6e4526615535c812331df74efef2ee4918eaa43b2be2dcf23b6303d) is unknown but expected to be uploaded
2025-09-23 09:47:01,180 [ 971303]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (e30a8884e6e4526615535c812331df74efef2ee4918eaa43b2be2dcf23b6303d) is unknown but expected to be uploaded
2025-09-23 09:47:01,180 [ 971303]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (e30a8884e6e4526615535c812331df74efef2ee4918eaa43b2be2dcf23b6303d) is unknown but expected to be uploaded
2025-09-23 09:47:01,763 [ 971886]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: e30a8884e6e4526615535c812331df74efef2ee4918eaa43b2be2dcf23b6303d -> 4e1f56736021a89085c344e3137d04eb0ac881cee490ae9eb4261d0c91dbc850
2025-09-23 09:47:01,829 [ 971952]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: IntersectionDisplay context not found.  Call setIntersectionContext first. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:47:02,297 [ 972420]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: e30a8884e6e4526615535c812331df74efef2ee4918eaa43b2be2dcf23b6303d -> 4e1f56736021a89085c344e3137d04eb0ac881cee490ae9eb4261d0c91dbc850
2025-09-23 09:47:02,785 [ 972908]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: e30a8884e6e4526615535c812331df74efef2ee4918eaa43b2be2dcf23b6303d -> 4e1f56736021a89085c344e3137d04eb0ac881cee490ae9eb4261d0c91dbc850
2025-09-23 09:47:02,953 [ 973076]   WARN - #com.augmentcode.intellij.chat.ChatWebviewMessageClient$Companion - Workspace guidelines are empty, not including in chat request. 
2025-09-23 09:47:03,370 [ 973493]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: e30a8884e6e4526615535c812331df74efef2ee4918eaa43b2be2dcf23b6303d -> 4e1f56736021a89085c344e3137d04eb0ac881cee490ae9eb4261d0c91dbc850
2025-09-23 09:47:03,989 [ 974112]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: e30a8884e6e4526615535c812331df74efef2ee4918eaa43b2be2dcf23b6303d -> 4e1f56736021a89085c344e3137d04eb0ac881cee490ae9eb4261d0c91dbc850
2025-09-23 09:47:04,713 [ 974836]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: e30a8884e6e4526615535c812331df74efef2ee4918eaa43b2be2dcf23b6303d -> 4e1f56736021a89085c344e3137d04eb0ac881cee490ae9eb4261d0c91dbc850
2025-09-23 09:47:05,184 [ 975307]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: e30a8884e6e4526615535c812331df74efef2ee4918eaa43b2be2dcf23b6303d -> 4e1f56736021a89085c344e3137d04eb0ac881cee490ae9eb4261d0c91dbc850
2025-09-23 09:47:05,655 [ 975778]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: e30a8884e6e4526615535c812331df74efef2ee4918eaa43b2be2dcf23b6303d -> 4e1f56736021a89085c344e3137d04eb0ac881cee490ae9eb4261d0c91dbc850
2025-09-23 09:47:06,215 [ 976338]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: e30a8884e6e4526615535c812331df74efef2ee4918eaa43b2be2dcf23b6303d -> 4e1f56736021a89085c344e3137d04eb0ac881cee490ae9eb4261d0c91dbc850
2025-09-23 09:47:06,907 [ 977030]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: e30a8884e6e4526615535c812331df74efef2ee4918eaa43b2be2dcf23b6303d -> 2e2c34efc97e9a7761d354361a7f2c24cc67161531bdd129d67a98f437e26b25
2025-09-23 09:47:07,098 [ 977221]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: No exchange with this request ID found. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:47:07,430 [ 977553]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: e30a8884e6e4526615535c812331df74efef2ee4918eaa43b2be2dcf23b6303d -> 2e2c34efc97e9a7761d354361a7f2c24cc67161531bdd129d67a98f437e26b25
2025-09-23 09:47:07,924 [ 978047]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: e30a8884e6e4526615535c812331df74efef2ee4918eaa43b2be2dcf23b6303d -> 2e2c34efc97e9a7761d354361a7f2c24cc67161531bdd129d67a98f437e26b25
2025-09-23 09:47:08,616 [ 978739]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: IntersectionDisplay context not found.  Call setIntersectionContext first. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:47:08,743 [ 978866]   WARN - #com.augmentcode.intellij.chat.ChatWebviewMessageClient$Companion - Workspace guidelines are empty, not including in chat request. 
2025-09-23 09:47:08,748 [ 978871]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: e30a8884e6e4526615535c812331df74efef2ee4918eaa43b2be2dcf23b6303d -> 2e2c34efc97e9a7761d354361a7f2c24cc67161531bdd129d67a98f437e26b25
2025-09-23 09:47:09,358 [ 979481]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: e30a8884e6e4526615535c812331df74efef2ee4918eaa43b2be2dcf23b6303d -> 2e2c34efc97e9a7761d354361a7f2c24cc67161531bdd129d67a98f437e26b25
2025-09-23 09:47:10,066 [ 980189]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: e30a8884e6e4526615535c812331df74efef2ee4918eaa43b2be2dcf23b6303d -> 2e2c34efc97e9a7761d354361a7f2c24cc67161531bdd129d67a98f437e26b25
2025-09-23 09:47:10,730 [ 980853]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: e30a8884e6e4526615535c812331df74efef2ee4918eaa43b2be2dcf23b6303d -> 2e2c34efc97e9a7761d354361a7f2c24cc67161531bdd129d67a98f437e26b25
2025-09-23 09:47:11,243 [ 981366]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: e30a8884e6e4526615535c812331df74efef2ee4918eaa43b2be2dcf23b6303d -> 2e2c34efc97e9a7761d354361a7f2c24cc67161531bdd129d67a98f437e26b25
2025-09-23 09:47:11,718 [ 981841]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: e30a8884e6e4526615535c812331df74efef2ee4918eaa43b2be2dcf23b6303d -> 2e2c34efc97e9a7761d354361a7f2c24cc67161531bdd129d67a98f437e26b25
2025-09-23 09:47:11,748 [ 981871]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: No exchange with this request ID found. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:47:12,725 [ 982848]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: e30a8884e6e4526615535c812331df74efef2ee4918eaa43b2be2dcf23b6303d -> 2e2c34efc97e9a7761d354361a7f2c24cc67161531bdd129d67a98f437e26b25
2025-09-23 09:47:13,262 [ 983385]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: e30a8884e6e4526615535c812331df74efef2ee4918eaa43b2be2dcf23b6303d -> 2e2c34efc97e9a7761d354361a7f2c24cc67161531bdd129d67a98f437e26b25
2025-09-23 09:47:16,783 [ 986906]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (4e1f56736021a89085c344e3137d04eb0ac881cee490ae9eb4261d0c91dbc850) is unknown but expected to be uploaded
2025-09-23 09:47:16,783 [ 986906]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (4e1f56736021a89085c344e3137d04eb0ac881cee490ae9eb4261d0c91dbc850) is unknown but expected to be uploaded
2025-09-23 09:47:16,783 [ 986906]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (4e1f56736021a89085c344e3137d04eb0ac881cee490ae9eb4261d0c91dbc850) is unknown but expected to be uploaded
2025-09-23 09:47:16,783 [ 986906]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (4e1f56736021a89085c344e3137d04eb0ac881cee490ae9eb4261d0c91dbc850) is unknown but expected to be uploaded
2025-09-23 09:47:16,783 [ 986906]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (4e1f56736021a89085c344e3137d04eb0ac881cee490ae9eb4261d0c91dbc850) is unknown but expected to be uploaded
2025-09-23 09:47:16,783 [ 986906]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (4e1f56736021a89085c344e3137d04eb0ac881cee490ae9eb4261d0c91dbc850) is unknown but expected to be uploaded
2025-09-23 09:47:16,783 [ 986906]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (4e1f56736021a89085c344e3137d04eb0ac881cee490ae9eb4261d0c91dbc850) is unknown but expected to be uploaded
2025-09-23 09:47:16,783 [ 986906]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (4e1f56736021a89085c344e3137d04eb0ac881cee490ae9eb4261d0c91dbc850) is unknown but expected to be uploaded
2025-09-23 09:47:16,783 [ 986906]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (4e1f56736021a89085c344e3137d04eb0ac881cee490ae9eb4261d0c91dbc850) is unknown but expected to be uploaded
2025-09-23 09:47:16,783 [ 986906]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (4e1f56736021a89085c344e3137d04eb0ac881cee490ae9eb4261d0c91dbc850) is unknown but expected to be uploaded
2025-09-23 09:47:16,783 [ 986906]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (4e1f56736021a89085c344e3137d04eb0ac881cee490ae9eb4261d0c91dbc850) is unknown but expected to be uploaded
2025-09-23 09:47:16,783 [ 986906]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (4e1f56736021a89085c344e3137d04eb0ac881cee490ae9eb4261d0c91dbc850) is unknown but expected to be uploaded
2025-09-23 09:47:16,783 [ 986906]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (2e2c34efc97e9a7761d354361a7f2c24cc67161531bdd129d67a98f437e26b25) is unknown but expected to be uploaded
2025-09-23 09:47:16,783 [ 986906]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (2e2c34efc97e9a7761d354361a7f2c24cc67161531bdd129d67a98f437e26b25) is unknown but expected to be uploaded
2025-09-23 09:47:16,783 [ 986906]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (2e2c34efc97e9a7761d354361a7f2c24cc67161531bdd129d67a98f437e26b25) is unknown but expected to be uploaded
2025-09-23 09:47:16,783 [ 986906]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (2e2c34efc97e9a7761d354361a7f2c24cc67161531bdd129d67a98f437e26b25) is unknown but expected to be uploaded
2025-09-23 09:47:16,783 [ 986906]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (2e2c34efc97e9a7761d354361a7f2c24cc67161531bdd129d67a98f437e26b25) is unknown but expected to be uploaded
2025-09-23 09:47:16,783 [ 986906]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (2e2c34efc97e9a7761d354361a7f2c24cc67161531bdd129d67a98f437e26b25) is unknown but expected to be uploaded
2025-09-23 09:47:16,783 [ 986906]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (2e2c34efc97e9a7761d354361a7f2c24cc67161531bdd129d67a98f437e26b25) is unknown but expected to be uploaded
2025-09-23 09:47:16,783 [ 986906]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (2e2c34efc97e9a7761d354361a7f2c24cc67161531bdd129d67a98f437e26b25) is unknown but expected to be uploaded
2025-09-23 09:47:16,783 [ 986906]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (2e2c34efc97e9a7761d354361a7f2c24cc67161531bdd129d67a98f437e26b25) is unknown but expected to be uploaded
2025-09-23 09:47:16,783 [ 986906]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (2e2c34efc97e9a7761d354361a7f2c24cc67161531bdd129d67a98f437e26b25) is unknown but expected to be uploaded
2025-09-23 09:47:16,783 [ 986906]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (2e2c34efc97e9a7761d354361a7f2c24cc67161531bdd129d67a98f437e26b25) is unknown but expected to be uploaded
2025-09-23 09:47:16,785 [ 986908]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (2e2c34efc97e9a7761d354361a7f2c24cc67161531bdd129d67a98f437e26b25) is unknown but expected to be uploaded
2025-09-23 09:47:16,800 [ 986923]   WARN - #c.i.e.wsl - Failed to get WSL mount root for Ubuntu: DOS working directory is expected, but got \\wsl.localhost\Ubuntu\home\oni\audio-transcriber
2025-09-23 09:47:17,169 [ 987292]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: IntersectionDisplay context not found.  Call setIntersectionContext first. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:47:17,403 [ 987526]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 4e1f56736021a89085c344e3137d04eb0ac881cee490ae9eb4261d0c91dbc850 -> 122a9a9319f27d7c5991611ac7a403ba8c5680b18c30cb8cba65dcd56c2de492
2025-09-23 09:47:17,911 [ 988034]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 4e1f56736021a89085c344e3137d04eb0ac881cee490ae9eb4261d0c91dbc850 -> 122a9a9319f27d7c5991611ac7a403ba8c5680b18c30cb8cba65dcd56c2de492
2025-09-23 09:47:18,244 [ 988367]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: Loading changes  for de1bfd4e-bd1e-4cc3-843e-e9f3c07ed8e5 retry 0:  Error: No changes found [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:47:18,246 [ 988369]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: Loading changes  for de1bfd4e-bd1e-4cc3-843e-e9f3c07ed8e5 retry 1:  Error: No changes found [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:47:18,262 [ 988385]   WARN - #com.augmentcode.intellij.chat.ChatWebviewMessageClient$Companion - Workspace guidelines are empty, not including in chat request. 
2025-09-23 09:47:18,421 [ 988544]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 4e1f56736021a89085c344e3137d04eb0ac881cee490ae9eb4261d0c91dbc850 -> 122a9a9319f27d7c5991611ac7a403ba8c5680b18c30cb8cba65dcd56c2de492
2025-09-23 09:47:18,748 [ 988871]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: Loading changes  for de1bfd4e-bd1e-4cc3-843e-e9f3c07ed8e5 retry 2:  Error: No changes found [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:47:18,750 [ 988873]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: Loading changes  for de1bfd4e-bd1e-4cc3-843e-e9f3c07ed8e5 retry 3:  Error: No changes found [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:47:18,750 [ 988873]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: Loading changes failed for de1bfd4e-bd1e-4cc3-843e-e9f3c07ed8e5:  Error: No changes found [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:47:18,955 [ 989078]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 4e1f56736021a89085c344e3137d04eb0ac881cee490ae9eb4261d0c91dbc850 -> 122a9a9319f27d7c5991611ac7a403ba8c5680b18c30cb8cba65dcd56c2de492
2025-09-23 09:47:19,251 [ 989374]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: Loading changes  for de1bfd4e-bd1e-4cc3-843e-e9f3c07ed8e5 retry 4:  Error: No changes found [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:47:19,251 [ 989374]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: Loading changes failed for de1bfd4e-bd1e-4cc3-843e-e9f3c07ed8e5:  Error: No changes found [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:47:19,688 [ 989811]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 4e1f56736021a89085c344e3137d04eb0ac881cee490ae9eb4261d0c91dbc850 -> 122a9a9319f27d7c5991611ac7a403ba8c5680b18c30cb8cba65dcd56c2de492
2025-09-23 09:47:20,227 [ 990350]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 4e1f56736021a89085c344e3137d04eb0ac881cee490ae9eb4261d0c91dbc850 -> 122a9a9319f27d7c5991611ac7a403ba8c5680b18c30cb8cba65dcd56c2de492
2025-09-23 09:47:20,488 [ 990611]   WARN - #c.i.e.wsl - Failed to get WSL mount root for Ubuntu: DOS working directory is expected, but got \\wsl.localhost\Ubuntu\home\oni\audio-transcriber
2025-09-23 09:47:20,788 [ 990911]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 2e2c34efc97e9a7761d354361a7f2c24cc67161531bdd129d67a98f437e26b25 -> 122a9a9319f27d7c5991611ac7a403ba8c5680b18c30cb8cba65dcd56c2de492
2025-09-23 09:47:21,253 [ 991376]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 2e2c34efc97e9a7761d354361a7f2c24cc67161531bdd129d67a98f437e26b25 -> 122a9a9319f27d7c5991611ac7a403ba8c5680b18c30cb8cba65dcd56c2de492
2025-09-23 09:47:21,955 [ 992078]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: No exchange with this request ID found. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:47:22,174 [ 992297]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 2e2c34efc97e9a7761d354361a7f2c24cc67161531bdd129d67a98f437e26b25 -> 122a9a9319f27d7c5991611ac7a403ba8c5680b18c30cb8cba65dcd56c2de492
2025-09-23 09:47:22,996 [ 993119]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 2e2c34efc97e9a7761d354361a7f2c24cc67161531bdd129d67a98f437e26b25 -> 122a9a9319f27d7c5991611ac7a403ba8c5680b18c30cb8cba65dcd56c2de492
2025-09-23 09:47:23,705 [ 993828]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 2e2c34efc97e9a7761d354361a7f2c24cc67161531bdd129d67a98f437e26b25 -> 122a9a9319f27d7c5991611ac7a403ba8c5680b18c30cb8cba65dcd56c2de492
2025-09-23 09:47:24,270 [ 994393]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 2e2c34efc97e9a7761d354361a7f2c24cc67161531bdd129d67a98f437e26b25 -> 122a9a9319f27d7c5991611ac7a403ba8c5680b18c30cb8cba65dcd56c2de492
2025-09-23 09:47:24,972 [ 995095]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 4e1f56736021a89085c344e3137d04eb0ac881cee490ae9eb4261d0c91dbc850 -> 122a9a9319f27d7c5991611ac7a403ba8c5680b18c30cb8cba65dcd56c2de492
2025-09-23 09:47:25,993 [ 996116]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 4e1f56736021a89085c344e3137d04eb0ac881cee490ae9eb4261d0c91dbc850 -> 122a9a9319f27d7c5991611ac7a403ba8c5680b18c30cb8cba65dcd56c2de492
2025-09-23 09:47:26,095 [ 996218]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: IntersectionDisplay context not found.  Call setIntersectionContext first. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:47:27,094 [ 997217]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 4e1f56736021a89085c344e3137d04eb0ac881cee490ae9eb4261d0c91dbc850 -> 122a9a9319f27d7c5991611ac7a403ba8c5680b18c30cb8cba65dcd56c2de492
2025-09-23 09:47:27,263 [ 997386]   WARN - #com.augmentcode.intellij.chat.ChatWebviewMessageClient$Companion - Workspace guidelines are empty, not including in chat request. 
2025-09-23 09:47:27,731 [ 997854]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 2e2c34efc97e9a7761d354361a7f2c24cc67161531bdd129d67a98f437e26b25 -> 122a9a9319f27d7c5991611ac7a403ba8c5680b18c30cb8cba65dcd56c2de492
2025-09-23 09:47:28,840 [ 998963]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 2e2c34efc97e9a7761d354361a7f2c24cc67161531bdd129d67a98f437e26b25 -> 122a9a9319f27d7c5991611ac7a403ba8c5680b18c30cb8cba65dcd56c2de492
2025-09-23 09:47:29,372 [ 999495]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 2e2c34efc97e9a7761d354361a7f2c24cc67161531bdd129d67a98f437e26b25 -> 122a9a9319f27d7c5991611ac7a403ba8c5680b18c30cb8cba65dcd56c2de492
2025-09-23 09:47:30,582 [1000705]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 4e1f56736021a89085c344e3137d04eb0ac881cee490ae9eb4261d0c91dbc850 -> 122a9a9319f27d7c5991611ac7a403ba8c5680b18c30cb8cba65dcd56c2de492
2025-09-23 09:47:31,096 [1001219]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 2e2c34efc97e9a7761d354361a7f2c24cc67161531bdd129d67a98f437e26b25 -> 122a9a9319f27d7c5991611ac7a403ba8c5680b18c30cb8cba65dcd56c2de492
2025-09-23 09:47:31,102 [1001225]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: No exchange with this request ID found. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:47:32,419 [1002542]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 4e1f56736021a89085c344e3137d04eb0ac881cee490ae9eb4261d0c91dbc850 -> 122a9a9319f27d7c5991611ac7a403ba8c5680b18c30cb8cba65dcd56c2de492
2025-09-23 09:47:32,686 [1002809]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: IntersectionDisplay context not found.  Call setIntersectionContext first. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:47:32,791 [1002914]   WARN - #com.augmentcode.intellij.chat.ChatWebviewMessageClient$Companion - Workspace guidelines are empty, not including in chat request. 
2025-09-23 09:47:32,926 [1003049]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 2e2c34efc97e9a7761d354361a7f2c24cc67161531bdd129d67a98f437e26b25 -> 122a9a9319f27d7c5991611ac7a403ba8c5680b18c30cb8cba65dcd56c2de492
2025-09-23 09:47:33,774 [1003897]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 4e1f56736021a89085c344e3137d04eb0ac881cee490ae9eb4261d0c91dbc850 -> 122a9a9319f27d7c5991611ac7a403ba8c5680b18c30cb8cba65dcd56c2de492
2025-09-23 09:47:37,534 [1007657]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 2e2c34efc97e9a7761d354361a7f2c24cc67161531bdd129d67a98f437e26b25 -> f56ba58e35f3be3ac4a5cebe970ce5becf426bc7bfc605053e50288f513b8361
2025-09-23 09:47:37,605 [1007728]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: No exchange with this request ID found. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:47:39,029 [1009152]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: IntersectionDisplay context not found.  Call setIntersectionContext first. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:47:43,985 [1014108]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (122a9a9319f27d7c5991611ac7a403ba8c5680b18c30cb8cba65dcd56c2de492) is unknown but expected to be uploaded
2025-09-23 09:47:43,985 [1014108]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (122a9a9319f27d7c5991611ac7a403ba8c5680b18c30cb8cba65dcd56c2de492) is unknown but expected to be uploaded
2025-09-23 09:47:43,985 [1014108]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (122a9a9319f27d7c5991611ac7a403ba8c5680b18c30cb8cba65dcd56c2de492) is unknown but expected to be uploaded
2025-09-23 09:47:43,985 [1014108]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (122a9a9319f27d7c5991611ac7a403ba8c5680b18c30cb8cba65dcd56c2de492) is unknown but expected to be uploaded
2025-09-23 09:47:43,985 [1014108]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (122a9a9319f27d7c5991611ac7a403ba8c5680b18c30cb8cba65dcd56c2de492) is unknown but expected to be uploaded
2025-09-23 09:47:43,985 [1014108]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (122a9a9319f27d7c5991611ac7a403ba8c5680b18c30cb8cba65dcd56c2de492) is unknown but expected to be uploaded
2025-09-23 09:47:43,985 [1014108]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (122a9a9319f27d7c5991611ac7a403ba8c5680b18c30cb8cba65dcd56c2de492) is unknown but expected to be uploaded
2025-09-23 09:47:43,985 [1014108]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (122a9a9319f27d7c5991611ac7a403ba8c5680b18c30cb8cba65dcd56c2de492) is unknown but expected to be uploaded
2025-09-23 09:47:43,985 [1014108]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (122a9a9319f27d7c5991611ac7a403ba8c5680b18c30cb8cba65dcd56c2de492) is unknown but expected to be uploaded
2025-09-23 09:47:43,985 [1014108]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (122a9a9319f27d7c5991611ac7a403ba8c5680b18c30cb8cba65dcd56c2de492) is unknown but expected to be uploaded
2025-09-23 09:47:43,985 [1014108]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (122a9a9319f27d7c5991611ac7a403ba8c5680b18c30cb8cba65dcd56c2de492) is unknown but expected to be uploaded
2025-09-23 09:47:43,985 [1014108]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (122a9a9319f27d7c5991611ac7a403ba8c5680b18c30cb8cba65dcd56c2de492) is unknown but expected to be uploaded
2025-09-23 09:47:43,985 [1014108]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (122a9a9319f27d7c5991611ac7a403ba8c5680b18c30cb8cba65dcd56c2de492) is unknown but expected to be uploaded
2025-09-23 09:47:43,985 [1014108]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (122a9a9319f27d7c5991611ac7a403ba8c5680b18c30cb8cba65dcd56c2de492) is unknown but expected to be uploaded
2025-09-23 09:47:43,985 [1014108]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (122a9a9319f27d7c5991611ac7a403ba8c5680b18c30cb8cba65dcd56c2de492) is unknown but expected to be uploaded
2025-09-23 09:47:43,985 [1014108]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (122a9a9319f27d7c5991611ac7a403ba8c5680b18c30cb8cba65dcd56c2de492) is unknown but expected to be uploaded
2025-09-23 09:47:43,985 [1014108]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (122a9a9319f27d7c5991611ac7a403ba8c5680b18c30cb8cba65dcd56c2de492) is unknown but expected to be uploaded
2025-09-23 09:47:43,985 [1014108]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (122a9a9319f27d7c5991611ac7a403ba8c5680b18c30cb8cba65dcd56c2de492) is unknown but expected to be uploaded
2025-09-23 09:47:43,985 [1014108]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (122a9a9319f27d7c5991611ac7a403ba8c5680b18c30cb8cba65dcd56c2de492) is unknown but expected to be uploaded
2025-09-23 09:47:43,985 [1014108]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (122a9a9319f27d7c5991611ac7a403ba8c5680b18c30cb8cba65dcd56c2de492) is unknown but expected to be uploaded
2025-09-23 09:47:43,985 [1014108]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (122a9a9319f27d7c5991611ac7a403ba8c5680b18c30cb8cba65dcd56c2de492) is unknown but expected to be uploaded
2025-09-23 09:47:43,985 [1014108]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (122a9a9319f27d7c5991611ac7a403ba8c5680b18c30cb8cba65dcd56c2de492) is unknown but expected to be uploaded
2025-09-23 09:47:43,985 [1014108]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (122a9a9319f27d7c5991611ac7a403ba8c5680b18c30cb8cba65dcd56c2de492) is unknown but expected to be uploaded
2025-09-23 09:47:43,985 [1014108]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (122a9a9319f27d7c5991611ac7a403ba8c5680b18c30cb8cba65dcd56c2de492) is unknown but expected to be uploaded
2025-09-23 09:47:43,986 [1014109]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (f56ba58e35f3be3ac4a5cebe970ce5becf426bc7bfc605053e50288f513b8361) is unknown but expected to be uploaded
2025-09-23 09:47:43,986 [1014109]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (f56ba58e35f3be3ac4a5cebe970ce5becf426bc7bfc605053e50288f513b8361) is unknown but expected to be uploaded
2025-09-23 09:47:44,493 [1014616]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 122a9a9319f27d7c5991611ac7a403ba8c5680b18c30cb8cba65dcd56c2de492 -> f56ba58e35f3be3ac4a5cebe970ce5becf426bc7bfc605053e50288f513b8361
2025-09-23 09:47:45,054 [1015177]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 122a9a9319f27d7c5991611ac7a403ba8c5680b18c30cb8cba65dcd56c2de492 -> f56ba58e35f3be3ac4a5cebe970ce5becf426bc7bfc605053e50288f513b8361
2025-09-23 09:47:45,608 [1015731]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 122a9a9319f27d7c5991611ac7a403ba8c5680b18c30cb8cba65dcd56c2de492 -> f56ba58e35f3be3ac4a5cebe970ce5becf426bc7bfc605053e50288f513b8361
2025-09-23 09:47:46,161 [1016284]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 122a9a9319f27d7c5991611ac7a403ba8c5680b18c30cb8cba65dcd56c2de492 -> f56ba58e35f3be3ac4a5cebe970ce5becf426bc7bfc605053e50288f513b8361
2025-09-23 09:47:46,729 [1016852]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 122a9a9319f27d7c5991611ac7a403ba8c5680b18c30cb8cba65dcd56c2de492 -> f56ba58e35f3be3ac4a5cebe970ce5becf426bc7bfc605053e50288f513b8361
2025-09-23 09:47:47,215 [1017338]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 122a9a9319f27d7c5991611ac7a403ba8c5680b18c30cb8cba65dcd56c2de492 -> f56ba58e35f3be3ac4a5cebe970ce5becf426bc7bfc605053e50288f513b8361
2025-09-23 09:47:48,118 [1018241]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 122a9a9319f27d7c5991611ac7a403ba8c5680b18c30cb8cba65dcd56c2de492 -> f56ba58e35f3be3ac4a5cebe970ce5becf426bc7bfc605053e50288f513b8361
2025-09-23 09:47:48,634 [1018757]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 122a9a9319f27d7c5991611ac7a403ba8c5680b18c30cb8cba65dcd56c2de492 -> f56ba58e35f3be3ac4a5cebe970ce5becf426bc7bfc605053e50288f513b8361
2025-09-23 09:47:49,132 [1019255]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 122a9a9319f27d7c5991611ac7a403ba8c5680b18c30cb8cba65dcd56c2de492 -> f56ba58e35f3be3ac4a5cebe970ce5becf426bc7bfc605053e50288f513b8361
2025-09-23 09:47:49,687 [1019810]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 122a9a9319f27d7c5991611ac7a403ba8c5680b18c30cb8cba65dcd56c2de492 -> f56ba58e35f3be3ac4a5cebe970ce5becf426bc7bfc605053e50288f513b8361
2025-09-23 09:47:50,583 [1020706]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 122a9a9319f27d7c5991611ac7a403ba8c5680b18c30cb8cba65dcd56c2de492 -> f56ba58e35f3be3ac4a5cebe970ce5becf426bc7bfc605053e50288f513b8361
2025-09-23 09:47:51,301 [1021424]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 122a9a9319f27d7c5991611ac7a403ba8c5680b18c30cb8cba65dcd56c2de492 -> 94026e4c962fe0f771d84bca3c81dadf62205186690aa4cbebc264ad43bff017
2025-09-23 09:47:51,825 [1021948]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: f56ba58e35f3be3ac4a5cebe970ce5becf426bc7bfc605053e50288f513b8361 -> 94026e4c962fe0f771d84bca3c81dadf62205186690aa4cbebc264ad43bff017
2025-09-23 09:47:52,459 [1022582]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 122a9a9319f27d7c5991611ac7a403ba8c5680b18c30cb8cba65dcd56c2de492 -> 94026e4c962fe0f771d84bca3c81dadf62205186690aa4cbebc264ad43bff017
2025-09-23 09:47:52,975 [1023098]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 122a9a9319f27d7c5991611ac7a403ba8c5680b18c30cb8cba65dcd56c2de492 -> 94026e4c962fe0f771d84bca3c81dadf62205186690aa4cbebc264ad43bff017
2025-09-23 09:47:53,539 [1023662]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 122a9a9319f27d7c5991611ac7a403ba8c5680b18c30cb8cba65dcd56c2de492 -> 94026e4c962fe0f771d84bca3c81dadf62205186690aa4cbebc264ad43bff017
2025-09-23 09:47:54,226 [1024349]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 122a9a9319f27d7c5991611ac7a403ba8c5680b18c30cb8cba65dcd56c2de492 -> 94026e4c962fe0f771d84bca3c81dadf62205186690aa4cbebc264ad43bff017
2025-09-23 09:47:54,723 [1024846]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 122a9a9319f27d7c5991611ac7a403ba8c5680b18c30cb8cba65dcd56c2de492 -> 94026e4c962fe0f771d84bca3c81dadf62205186690aa4cbebc264ad43bff017
2025-09-23 09:47:55,279 [1025402]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 122a9a9319f27d7c5991611ac7a403ba8c5680b18c30cb8cba65dcd56c2de492 -> 94026e4c962fe0f771d84bca3c81dadf62205186690aa4cbebc264ad43bff017
2025-09-23 09:47:55,904 [1026027]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: f56ba58e35f3be3ac4a5cebe970ce5becf426bc7bfc605053e50288f513b8361 -> 94026e4c962fe0f771d84bca3c81dadf62205186690aa4cbebc264ad43bff017
2025-09-23 09:47:56,426 [1026549]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 122a9a9319f27d7c5991611ac7a403ba8c5680b18c30cb8cba65dcd56c2de492 -> 94026e4c962fe0f771d84bca3c81dadf62205186690aa4cbebc264ad43bff017
2025-09-23 09:47:56,961 [1027084]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 122a9a9319f27d7c5991611ac7a403ba8c5680b18c30cb8cba65dcd56c2de492 -> 94026e4c962fe0f771d84bca3c81dadf62205186690aa4cbebc264ad43bff017
2025-09-23 09:47:57,651 [1027774]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 122a9a9319f27d7c5991611ac7a403ba8c5680b18c30cb8cba65dcd56c2de492 -> 94026e4c962fe0f771d84bca3c81dadf62205186690aa4cbebc264ad43bff017
2025-09-23 09:47:58,187 [1028310]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 122a9a9319f27d7c5991611ac7a403ba8c5680b18c30cb8cba65dcd56c2de492 -> 94026e4c962fe0f771d84bca3c81dadf62205186690aa4cbebc264ad43bff017
2025-09-23 09:47:58,683 [1028806]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 122a9a9319f27d7c5991611ac7a403ba8c5680b18c30cb8cba65dcd56c2de492 -> 94026e4c962fe0f771d84bca3c81dadf62205186690aa4cbebc264ad43bff017
2025-09-23 09:48:00,341 [1030464]   WARN - #com.augmentcode.intellij.sentry.SentryMetadataCollector - Git ls-files command failed with exit code: 128
2025-09-23 09:48:02,167 [1032290]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (f56ba58e35f3be3ac4a5cebe970ce5becf426bc7bfc605053e50288f513b8361) is unknown but expected to be uploaded
2025-09-23 09:48:02,168 [1032291]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (f56ba58e35f3be3ac4a5cebe970ce5becf426bc7bfc605053e50288f513b8361) is unknown but expected to be uploaded
2025-09-23 09:48:02,168 [1032291]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (f56ba58e35f3be3ac4a5cebe970ce5becf426bc7bfc605053e50288f513b8361) is unknown but expected to be uploaded
2025-09-23 09:48:02,168 [1032291]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (f56ba58e35f3be3ac4a5cebe970ce5becf426bc7bfc605053e50288f513b8361) is unknown but expected to be uploaded
2025-09-23 09:48:02,168 [1032291]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (f56ba58e35f3be3ac4a5cebe970ce5becf426bc7bfc605053e50288f513b8361) is unknown but expected to be uploaded
2025-09-23 09:48:02,168 [1032291]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (f56ba58e35f3be3ac4a5cebe970ce5becf426bc7bfc605053e50288f513b8361) is unknown but expected to be uploaded
2025-09-23 09:48:02,168 [1032291]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (f56ba58e35f3be3ac4a5cebe970ce5becf426bc7bfc605053e50288f513b8361) is unknown but expected to be uploaded
2025-09-23 09:48:02,168 [1032291]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (f56ba58e35f3be3ac4a5cebe970ce5becf426bc7bfc605053e50288f513b8361) is unknown but expected to be uploaded
2025-09-23 09:48:02,168 [1032291]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (f56ba58e35f3be3ac4a5cebe970ce5becf426bc7bfc605053e50288f513b8361) is unknown but expected to be uploaded
2025-09-23 09:48:02,168 [1032291]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (f56ba58e35f3be3ac4a5cebe970ce5becf426bc7bfc605053e50288f513b8361) is unknown but expected to be uploaded
2025-09-23 09:48:02,168 [1032291]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (f56ba58e35f3be3ac4a5cebe970ce5becf426bc7bfc605053e50288f513b8361) is unknown but expected to be uploaded
2025-09-23 09:48:02,168 [1032291]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (94026e4c962fe0f771d84bca3c81dadf62205186690aa4cbebc264ad43bff017) is unknown but expected to be uploaded
2025-09-23 09:48:02,168 [1032291]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (94026e4c962fe0f771d84bca3c81dadf62205186690aa4cbebc264ad43bff017) is unknown but expected to be uploaded
2025-09-23 09:48:02,168 [1032291]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (94026e4c962fe0f771d84bca3c81dadf62205186690aa4cbebc264ad43bff017) is unknown but expected to be uploaded
2025-09-23 09:48:02,168 [1032291]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (94026e4c962fe0f771d84bca3c81dadf62205186690aa4cbebc264ad43bff017) is unknown but expected to be uploaded
2025-09-23 09:48:02,168 [1032291]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (94026e4c962fe0f771d84bca3c81dadf62205186690aa4cbebc264ad43bff017) is unknown but expected to be uploaded
2025-09-23 09:48:02,168 [1032291]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (94026e4c962fe0f771d84bca3c81dadf62205186690aa4cbebc264ad43bff017) is unknown but expected to be uploaded
2025-09-23 09:48:02,168 [1032291]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (94026e4c962fe0f771d84bca3c81dadf62205186690aa4cbebc264ad43bff017) is unknown but expected to be uploaded
2025-09-23 09:48:02,168 [1032291]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (94026e4c962fe0f771d84bca3c81dadf62205186690aa4cbebc264ad43bff017) is unknown but expected to be uploaded
2025-09-23 09:48:02,168 [1032291]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (94026e4c962fe0f771d84bca3c81dadf62205186690aa4cbebc264ad43bff017) is unknown but expected to be uploaded
2025-09-23 09:48:02,168 [1032291]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (94026e4c962fe0f771d84bca3c81dadf62205186690aa4cbebc264ad43bff017) is unknown but expected to be uploaded
2025-09-23 09:48:02,168 [1032291]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (94026e4c962fe0f771d84bca3c81dadf62205186690aa4cbebc264ad43bff017) is unknown but expected to be uploaded
2025-09-23 09:48:02,168 [1032291]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (94026e4c962fe0f771d84bca3c81dadf62205186690aa4cbebc264ad43bff017) is unknown but expected to be uploaded
2025-09-23 09:48:02,168 [1032291]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (94026e4c962fe0f771d84bca3c81dadf62205186690aa4cbebc264ad43bff017) is unknown but expected to be uploaded
2025-09-23 09:48:02,168 [1032291]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (94026e4c962fe0f771d84bca3c81dadf62205186690aa4cbebc264ad43bff017) is unknown but expected to be uploaded
2025-09-23 09:48:02,168 [1032291]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (94026e4c962fe0f771d84bca3c81dadf62205186690aa4cbebc264ad43bff017) is unknown but expected to be uploaded
2025-09-23 09:48:02,444 [1032567]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 122a9a9319f27d7c5991611ac7a403ba8c5680b18c30cb8cba65dcd56c2de492 -> 94026e4c962fe0f771d84bca3c81dadf62205186690aa4cbebc264ad43bff017
2025-09-23 09:48:03,000 [1033123]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: f56ba58e35f3be3ac4a5cebe970ce5becf426bc7bfc605053e50288f513b8361 -> 94026e4c962fe0f771d84bca3c81dadf62205186690aa4cbebc264ad43bff017
2025-09-23 09:48:03,662 [1033785]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: f56ba58e35f3be3ac4a5cebe970ce5becf426bc7bfc605053e50288f513b8361 -> 94026e4c962fe0f771d84bca3c81dadf62205186690aa4cbebc264ad43bff017
2025-09-23 09:48:04,944 [1035067]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: f56ba58e35f3be3ac4a5cebe970ce5becf426bc7bfc605053e50288f513b8361 -> 94026e4c962fe0f771d84bca3c81dadf62205186690aa4cbebc264ad43bff017
2025-09-23 09:48:05,709 [1035832]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: f56ba58e35f3be3ac4a5cebe970ce5becf426bc7bfc605053e50288f513b8361 -> 94026e4c962fe0f771d84bca3c81dadf62205186690aa4cbebc264ad43bff017
2025-09-23 09:48:06,541 [1036664]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: f56ba58e35f3be3ac4a5cebe970ce5becf426bc7bfc605053e50288f513b8361 -> efece068d0aa8516cecdc6330ea74e4e53b5a7282fe35a8149e2a1596277c1e5
2025-09-23 09:48:07,161 [1037284]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: f56ba58e35f3be3ac4a5cebe970ce5becf426bc7bfc605053e50288f513b8361 -> efece068d0aa8516cecdc6330ea74e4e53b5a7282fe35a8149e2a1596277c1e5
2025-09-23 09:48:07,944 [1038067]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 94026e4c962fe0f771d84bca3c81dadf62205186690aa4cbebc264ad43bff017 -> efece068d0aa8516cecdc6330ea74e4e53b5a7282fe35a8149e2a1596277c1e5
2025-09-23 09:48:08,849 [1038972]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 94026e4c962fe0f771d84bca3c81dadf62205186690aa4cbebc264ad43bff017 -> efece068d0aa8516cecdc6330ea74e4e53b5a7282fe35a8149e2a1596277c1e5
2025-09-23 09:48:09,526 [1039649]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 94026e4c962fe0f771d84bca3c81dadf62205186690aa4cbebc264ad43bff017 -> efece068d0aa8516cecdc6330ea74e4e53b5a7282fe35a8149e2a1596277c1e5
2025-09-23 09:48:10,101 [1040224]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 94026e4c962fe0f771d84bca3c81dadf62205186690aa4cbebc264ad43bff017 -> efece068d0aa8516cecdc6330ea74e4e53b5a7282fe35a8149e2a1596277c1e5
2025-09-23 09:48:10,597 [1040720]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 94026e4c962fe0f771d84bca3c81dadf62205186690aa4cbebc264ad43bff017 -> efece068d0aa8516cecdc6330ea74e4e53b5a7282fe35a8149e2a1596277c1e5
2025-09-23 09:48:11,159 [1041282]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 94026e4c962fe0f771d84bca3c81dadf62205186690aa4cbebc264ad43bff017 -> efece068d0aa8516cecdc6330ea74e4e53b5a7282fe35a8149e2a1596277c1e5
2025-09-23 09:48:11,652 [1041775]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 94026e4c962fe0f771d84bca3c81dadf62205186690aa4cbebc264ad43bff017 -> efece068d0aa8516cecdc6330ea74e4e53b5a7282fe35a8149e2a1596277c1e5
2025-09-23 09:48:12,180 [1042303]   WARN - #c.i.e.wsl - Failed to get WSL mount root for Ubuntu: DOS working directory is expected, but got \\wsl.localhost\Ubuntu\home\oni\audio-transcriber
2025-09-23 09:48:12,225 [1042348]   WARN - #com.augmentcode.intellij.webviews.AbstractWebviewMessagingService - Failed to parse webview message as protobuf.
Exception: Missing type url when parsing: {"type":"can-show-terminal","data":{"command":"lsof -i :5000"}}
Message: {"type":"async-wrapper","requestId":"2e3b3d8a-9852-47f1-b7c1-86639d3ccc8b","error":null,"baseMsg":{"type":"can-show-terminal","data":{"command":"lsof -i :5000"}},"destination":"host"}
2025-09-23 09:48:12,229 [1042352]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_ERROR]: Failed to check if terminal can be shown: Error: Unable to parse webview message: {"type":"async-wrapper","requestId":"2e3b3d8a-9852-47f1-b7c1-86639d3ccc8b","error":null,"baseMsg":{"type":"can-show-terminal","data":{"command":"lsof -i :5000"}},"destination":"host"} [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:48:13,585 [1043708]   WARN - #com.augmentcode.intellij.webviews.AbstractWebviewMessagingService - Failed to parse webview message as protobuf.
Exception: Missing type url when parsing: {"type":"can-show-terminal","data":{"command":"lsof -i :5000"}}
Message: {"type":"async-wrapper","requestId":"3f263835-4e92-47f0-b6ec-b29991b95abe","error":null,"baseMsg":{"type":"can-show-terminal","data":{"command":"lsof -i :5000"}},"destination":"host"}
2025-09-23 09:48:13,608 [1043731]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: f56ba58e35f3be3ac4a5cebe970ce5becf426bc7bfc605053e50288f513b8361 -> efece068d0aa8516cecdc6330ea74e4e53b5a7282fe35a8149e2a1596277c1e5
2025-09-23 09:48:13,623 [1043746]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_ERROR]: Failed to check if terminal can be shown: Error: Unable to parse webview message: {"type":"async-wrapper","requestId":"3f263835-4e92-47f0-b6ec-b29991b95abe","error":null,"baseMsg":{"type":"can-show-terminal","data":{"command":"lsof -i :5000"}},"destination":"host"} [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:48:13,712 [1043835]   WARN - #com.augmentcode.intellij.chat.ChatWebviewMessageClient$Companion - Workspace guidelines are empty, not including in chat request. 
2025-09-23 09:48:14,288 [1044411]   WARN - #c.i.e.wsl - Failed to get WSL mount root for Ubuntu: DOS working directory is expected, but got \\wsl.localhost\Ubuntu\home\oni\audio-transcriber
2025-09-23 09:48:14,332 [1044455]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: f56ba58e35f3be3ac4a5cebe970ce5becf426bc7bfc605053e50288f513b8361 -> c8534ffd2220b3a06be68ec86296d4964007a5ec48932c4685115a832ed08bca
2025-09-23 09:48:15,161 [1045284]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: f56ba58e35f3be3ac4a5cebe970ce5becf426bc7bfc605053e50288f513b8361 -> 74899664a62dbd6ad64bd33a1818b55aed7003c5e085ebacadb505aeab0563c7
2025-09-23 09:48:15,888 [1046011]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 94026e4c962fe0f771d84bca3c81dadf62205186690aa4cbebc264ad43bff017 -> 74899664a62dbd6ad64bd33a1818b55aed7003c5e085ebacadb505aeab0563c7
2025-09-23 09:48:16,412 [1046535]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 94026e4c962fe0f771d84bca3c81dadf62205186690aa4cbebc264ad43bff017 -> 74899664a62dbd6ad64bd33a1818b55aed7003c5e085ebacadb505aeab0563c7
2025-09-23 09:48:17,122 [1047245]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 94026e4c962fe0f771d84bca3c81dadf62205186690aa4cbebc264ad43bff017 -> 74899664a62dbd6ad64bd33a1818b55aed7003c5e085ebacadb505aeab0563c7
2025-09-23 09:48:17,349 [1047472]   WARN - #c.i.e.wsl - Failed to get WSL mount root for Ubuntu: DOS working directory is expected, but got \\wsl.localhost\Ubuntu\home\oni\audio-transcriber
2025-09-23 09:48:17,842 [1047965]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: No exchange with this request ID found. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:48:17,933 [1048056]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 94026e4c962fe0f771d84bca3c81dadf62205186690aa4cbebc264ad43bff017 -> 74899664a62dbd6ad64bd33a1818b55aed7003c5e085ebacadb505aeab0563c7
2025-09-23 09:48:18,477 [1048600]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: f56ba58e35f3be3ac4a5cebe970ce5becf426bc7bfc605053e50288f513b8361 -> 74899664a62dbd6ad64bd33a1818b55aed7003c5e085ebacadb505aeab0563c7
2025-09-23 09:48:18,994 [1049117]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 94026e4c962fe0f771d84bca3c81dadf62205186690aa4cbebc264ad43bff017 -> 74899664a62dbd6ad64bd33a1818b55aed7003c5e085ebacadb505aeab0563c7
2025-09-23 09:48:19,762 [1049885]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 94026e4c962fe0f771d84bca3c81dadf62205186690aa4cbebc264ad43bff017 -> 74899664a62dbd6ad64bd33a1818b55aed7003c5e085ebacadb505aeab0563c7
2025-09-23 09:48:20,015 [1050138]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: IntersectionDisplay context not found.  Call setIntersectionContext first. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:48:20,296 [1050419]   WARN - #com.augmentcode.intellij.webviews.AbstractWebviewMessagingService - Failed to parse webview message as protobuf.
Exception: Missing type url when parsing: {"type":"can-show-terminal","data":{"command":"pkill -f \"node server/server.js\""}}
Message: {"type":"async-wrapper","requestId":"8df8021d-2e2e-455f-8672-4f49c75b305c","error":null,"baseMsg":{"type":"can-show-terminal","data":{"command":"pkill -f \"node server/server.js\""}},"destination":"host"}
2025-09-23 09:48:20,297 [1050420]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_ERROR]: Failed to check if terminal can be shown: Error: Unable to parse webview message: {"type":"async-wrapper","requestId":"8df8021d-2e2e-455f-8672-4f49c75b305c","error":null,"baseMsg":{"type":"can-show-terminal","data":{"command":"pkill -f \"node server/server.js\""}},"destination":"host"} [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:48:20,376 [1050499]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 94026e4c962fe0f771d84bca3c81dadf62205186690aa4cbebc264ad43bff017 -> 74899664a62dbd6ad64bd33a1818b55aed7003c5e085ebacadb505aeab0563c7
2025-09-23 09:48:20,885 [1051008]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: c8534ffd2220b3a06be68ec86296d4964007a5ec48932c4685115a832ed08bca -> 74899664a62dbd6ad64bd33a1818b55aed7003c5e085ebacadb505aeab0563c7
2025-09-23 09:48:21,374 [1051497]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 94026e4c962fe0f771d84bca3c81dadf62205186690aa4cbebc264ad43bff017 -> 74899664a62dbd6ad64bd33a1818b55aed7003c5e085ebacadb505aeab0563c7
2025-09-23 09:48:22,829 [1052952]   WARN - #c.i.e.wsl - Failed to get WSL mount root for Ubuntu: DOS working directory is expected, but got \\wsl.localhost\Ubuntu\home\oni\audio-transcriber
2025-09-23 09:48:23,491 [1053614]   WARN - #com.augmentcode.intellij.webviews.AbstractWebviewMessagingService - Failed to parse webview message as protobuf.
Exception: Missing type url when parsing: {"type":"can-show-terminal","data":{"command":"pkill -f \"node server/server.js\""}}
Message: {"type":"async-wrapper","requestId":"ebb3227c-d0f9-40f8-926a-10f83b06b523","error":null,"baseMsg":{"type":"can-show-terminal","data":{"command":"pkill -f \"node server/server.js\""}},"destination":"host"}
2025-09-23 09:48:23,496 [1053619]   WARN - #c.i.o.progress - Suppressed a frequent exception logged for the 20th time: This method is forbidden on EDT because it does not pump the event queue. Switch to a BGT, or use com.intellij.openapi.progress.TasksKt.runWithModalProgressBlocking. 
2025-09-23 09:48:23,496 [1053619]   WARN - #c.i.o.progress - Suppressed a frequent exception logged for the 20th time: This method is forbidden on EDT because it does not pump the event queue. Switch to a BGT, or use com.intellij.openapi.progress.TasksKt.runWithModalProgressBlocking. 
2025-09-23 09:48:23,498 [1053621]   WARN - #c.i.o.progress - Suppressed a frequent exception logged for the 20th time: This method is forbidden on EDT because it does not pump the event queue. Switch to a BGT, or use com.intellij.openapi.progress.TasksKt.runWithModalProgressBlocking. 
2025-09-23 09:48:23,498 [1053621]   WARN - #c.i.o.progress - Suppressed a frequent exception logged for the 20th time: This method is forbidden on EDT because it does not pump the event queue. Switch to a BGT, or use com.intellij.openapi.progress.TasksKt.runWithModalProgressBlocking. 
2025-09-23 09:48:23,499 [1053622]   WARN - #c.i.o.progress - Suppressed a frequent exception logged for the 20th time: This method is forbidden on EDT because it does not pump the event queue. Switch to a BGT, or use com.intellij.openapi.progress.TasksKt.runWithModalProgressBlocking. 
2025-09-23 09:48:23,529 [1053652]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_ERROR]: Failed to check if terminal can be shown: Error: Unable to parse webview message: {"type":"async-wrapper","requestId":"ebb3227c-d0f9-40f8-926a-10f83b06b523","error":null,"baseMsg":{"type":"can-show-terminal","data":{"command":"pkill -f \"node server/server.js\""}},"destination":"host"} [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:48:23,608 [1053731]   WARN - #com.augmentcode.intellij.chat.ChatWebviewMessageClient$Companion - Workspace guidelines are empty, not including in chat request. 
2025-09-23 09:48:24,061 [1054184]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: f56ba58e35f3be3ac4a5cebe970ce5becf426bc7bfc605053e50288f513b8361 -> 3f495b7eb2b72e1aa3d0da2ba0fba3e17b4d38753fff807dc6076ebb1a46ea60
2025-09-23 09:48:24,191 [1054314]   WARN - #c.i.e.wsl - Failed to get WSL mount root for Ubuntu: DOS working directory is expected, but got \\wsl.localhost\Ubuntu\home\oni\audio-transcriber
2025-09-23 09:48:27,479 [1057602]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: No exchange with this request ID found. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:48:27,648 [1057771]   WARN - #c.i.e.wsl - Failed to get WSL mount root for Ubuntu: DOS working directory is expected, but got \\wsl.localhost\Ubuntu\home\oni\audio-transcriber
2025-09-23 09:48:28,224 [1058347]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 3f495b7eb2b72e1aa3d0da2ba0fba3e17b4d38753fff807dc6076ebb1a46ea60 -> 87ff3a9535123beb2708ba9f7b9f48b4c51e82c94ea47c9f462d5c00c40fa3b2
2025-09-23 09:48:28,274 [1058397]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (94026e4c962fe0f771d84bca3c81dadf62205186690aa4cbebc264ad43bff017) is unknown but expected to be uploaded
2025-09-23 09:48:28,274 [1058397]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (94026e4c962fe0f771d84bca3c81dadf62205186690aa4cbebc264ad43bff017) is unknown but expected to be uploaded
2025-09-23 09:48:28,274 [1058397]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (94026e4c962fe0f771d84bca3c81dadf62205186690aa4cbebc264ad43bff017) is unknown but expected to be uploaded
2025-09-23 09:48:28,274 [1058397]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (94026e4c962fe0f771d84bca3c81dadf62205186690aa4cbebc264ad43bff017) is unknown but expected to be uploaded
2025-09-23 09:48:28,274 [1058397]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (94026e4c962fe0f771d84bca3c81dadf62205186690aa4cbebc264ad43bff017) is unknown but expected to be uploaded
2025-09-23 09:48:28,274 [1058397]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (efece068d0aa8516cecdc6330ea74e4e53b5a7282fe35a8149e2a1596277c1e5) is unknown but expected to be uploaded
2025-09-23 09:48:28,274 [1058397]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (efece068d0aa8516cecdc6330ea74e4e53b5a7282fe35a8149e2a1596277c1e5) is unknown but expected to be uploaded
2025-09-23 09:48:28,274 [1058397]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (efece068d0aa8516cecdc6330ea74e4e53b5a7282fe35a8149e2a1596277c1e5) is unknown but expected to be uploaded
2025-09-23 09:48:28,274 [1058397]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (efece068d0aa8516cecdc6330ea74e4e53b5a7282fe35a8149e2a1596277c1e5) is unknown but expected to be uploaded
2025-09-23 09:48:28,274 [1058397]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (efece068d0aa8516cecdc6330ea74e4e53b5a7282fe35a8149e2a1596277c1e5) is unknown but expected to be uploaded
2025-09-23 09:48:28,274 [1058397]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (efece068d0aa8516cecdc6330ea74e4e53b5a7282fe35a8149e2a1596277c1e5) is unknown but expected to be uploaded
2025-09-23 09:48:28,274 [1058397]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (efece068d0aa8516cecdc6330ea74e4e53b5a7282fe35a8149e2a1596277c1e5) is unknown but expected to be uploaded
2025-09-23 09:48:28,274 [1058397]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (efece068d0aa8516cecdc6330ea74e4e53b5a7282fe35a8149e2a1596277c1e5) is unknown but expected to be uploaded
2025-09-23 09:48:28,274 [1058397]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (efece068d0aa8516cecdc6330ea74e4e53b5a7282fe35a8149e2a1596277c1e5) is unknown but expected to be uploaded
2025-09-23 09:48:28,274 [1058397]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (efece068d0aa8516cecdc6330ea74e4e53b5a7282fe35a8149e2a1596277c1e5) is unknown but expected to be uploaded
2025-09-23 09:48:28,274 [1058397]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (efece068d0aa8516cecdc6330ea74e4e53b5a7282fe35a8149e2a1596277c1e5) is unknown but expected to be uploaded
2025-09-23 09:48:28,274 [1058397]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (3f495b7eb2b72e1aa3d0da2ba0fba3e17b4d38753fff807dc6076ebb1a46ea60) is unknown but expected to be uploaded
2025-09-23 09:48:28,274 [1058397]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (3f495b7eb2b72e1aa3d0da2ba0fba3e17b4d38753fff807dc6076ebb1a46ea60) is unknown but expected to be uploaded
2025-09-23 09:48:28,980 [1059103]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: IntersectionDisplay context not found.  Call setIntersectionContext first. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:48:29,262 [1059385]   WARN - #com.augmentcode.intellij.webviews.AbstractWebviewMessagingService - Failed to parse webview message as protobuf.
Exception: Missing type url when parsing: {"type":"can-show-terminal","data":{"command":"npm start"}}
Message: {"type":"async-wrapper","requestId":"33388547-93be-4bdc-a99a-77924b0bece2","error":null,"baseMsg":{"type":"can-show-terminal","data":{"command":"npm start"}},"destination":"host"}
2025-09-23 09:48:29,263 [1059386]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_ERROR]: Failed to check if terminal can be shown: Error: Unable to parse webview message: {"type":"async-wrapper","requestId":"33388547-93be-4bdc-a99a-77924b0bece2","error":null,"baseMsg":{"type":"can-show-terminal","data":{"command":"npm start"}},"destination":"host"} [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:48:29,311 [1059434]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 94026e4c962fe0f771d84bca3c81dadf62205186690aa4cbebc264ad43bff017 -> 87ff3a9535123beb2708ba9f7b9f48b4c51e82c94ea47c9f462d5c00c40fa3b2
2025-09-23 09:48:30,781 [1060904]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 94026e4c962fe0f771d84bca3c81dadf62205186690aa4cbebc264ad43bff017 -> 87ff3a9535123beb2708ba9f7b9f48b4c51e82c94ea47c9f462d5c00c40fa3b2
2025-09-23 09:48:31,287 [1061410]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 94026e4c962fe0f771d84bca3c81dadf62205186690aa4cbebc264ad43bff017 -> 87ff3a9535123beb2708ba9f7b9f48b4c51e82c94ea47c9f462d5c00c40fa3b2
2025-09-23 09:48:31,834 [1061957]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: efece068d0aa8516cecdc6330ea74e4e53b5a7282fe35a8149e2a1596277c1e5 -> 87ff3a9535123beb2708ba9f7b9f48b4c51e82c94ea47c9f462d5c00c40fa3b2
2025-09-23 09:48:32,392 [1062515]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: efece068d0aa8516cecdc6330ea74e4e53b5a7282fe35a8149e2a1596277c1e5 -> 87ff3a9535123beb2708ba9f7b9f48b4c51e82c94ea47c9f462d5c00c40fa3b2
2025-09-23 09:48:32,876 [1062999]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: efece068d0aa8516cecdc6330ea74e4e53b5a7282fe35a8149e2a1596277c1e5 -> 87ff3a9535123beb2708ba9f7b9f48b4c51e82c94ea47c9f462d5c00c40fa3b2
2025-09-23 09:48:33,860 [1063983]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: efece068d0aa8516cecdc6330ea74e4e53b5a7282fe35a8149e2a1596277c1e5 -> 87ff3a9535123beb2708ba9f7b9f48b4c51e82c94ea47c9f462d5c00c40fa3b2
2025-09-23 09:48:34,548 [1064671]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: efece068d0aa8516cecdc6330ea74e4e53b5a7282fe35a8149e2a1596277c1e5 -> 87ff3a9535123beb2708ba9f7b9f48b4c51e82c94ea47c9f462d5c00c40fa3b2
2025-09-23 09:48:35,314 [1065437]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 3f495b7eb2b72e1aa3d0da2ba0fba3e17b4d38753fff807dc6076ebb1a46ea60 -> 87ff3a9535123beb2708ba9f7b9f48b4c51e82c94ea47c9f462d5c00c40fa3b2
2025-09-23 09:48:35,541 [1065664]   WARN - #com.augmentcode.intellij.webviews.AbstractWebviewMessagingService - Failed to parse webview message as protobuf.
Exception: Missing type url when parsing: {"type":"can-show-terminal","data":{"command":"npm start"}}
Message: {"type":"async-wrapper","requestId":"97d1c339-c260-4bbf-8fa0-5d9eb5e7aef1","error":null,"baseMsg":{"type":"can-show-terminal","data":{"command":"npm start"}},"destination":"host"}
2025-09-23 09:48:35,588 [1065711]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_ERROR]: Failed to check if terminal can be shown: Error: Unable to parse webview message: {"type":"async-wrapper","requestId":"97d1c339-c260-4bbf-8fa0-5d9eb5e7aef1","error":null,"baseMsg":{"type":"can-show-terminal","data":{"command":"npm start"}},"destination":"host"} [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:48:35,703 [1065826]   WARN - #com.augmentcode.intellij.chat.ChatWebviewMessageClient$Companion - Workspace guidelines are empty, not including in chat request. 
2025-09-23 09:48:35,851 [1065974]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 94026e4c962fe0f771d84bca3c81dadf62205186690aa4cbebc264ad43bff017 -> 87ff3a9535123beb2708ba9f7b9f48b4c51e82c94ea47c9f462d5c00c40fa3b2
2025-09-23 09:48:36,113 [1066236]   WARN - #c.i.e.wsl - Failed to get WSL mount root for Ubuntu: DOS working directory is expected, but got \\wsl.localhost\Ubuntu\home\oni\audio-transcriber
2025-09-23 09:48:36,748 [1066871]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: efece068d0aa8516cecdc6330ea74e4e53b5a7282fe35a8149e2a1596277c1e5 -> 2d76c3e5c2378039569bc9a388950e6519b0f51bd89732b24a4ef46de356b7c5
2025-09-23 09:48:37,444 [1067567]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: efece068d0aa8516cecdc6330ea74e4e53b5a7282fe35a8149e2a1596277c1e5 -> 2d76c3e5c2378039569bc9a388950e6519b0f51bd89732b24a4ef46de356b7c5
2025-09-23 09:48:38,118 [1068241]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: efece068d0aa8516cecdc6330ea74e4e53b5a7282fe35a8149e2a1596277c1e5 -> 2d76c3e5c2378039569bc9a388950e6519b0f51bd89732b24a4ef46de356b7c5
2025-09-23 09:48:38,638 [1068761]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 3f495b7eb2b72e1aa3d0da2ba0fba3e17b4d38753fff807dc6076ebb1a46ea60 -> 2d76c3e5c2378039569bc9a388950e6519b0f51bd89732b24a4ef46de356b7c5
2025-09-23 09:48:39,302 [1069425]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: efece068d0aa8516cecdc6330ea74e4e53b5a7282fe35a8149e2a1596277c1e5 -> 2d76c3e5c2378039569bc9a388950e6519b0f51bd89732b24a4ef46de356b7c5
2025-09-23 09:48:39,565 [1069688]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: No exchange with this request ID found. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:48:39,876 [1069999]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: efece068d0aa8516cecdc6330ea74e4e53b5a7282fe35a8149e2a1596277c1e5 -> 2d76c3e5c2378039569bc9a388950e6519b0f51bd89732b24a4ef46de356b7c5
2025-09-23 09:48:39,996 [1070119]   WARN - #c.i.e.wsl - Failed to get WSL mount root for Ubuntu: DOS working directory is expected, but got \\wsl.localhost\Ubuntu\home\oni\audio-transcriber
2025-09-23 09:48:40,354 [1070477]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: IntersectionDisplay context not found.  Call setIntersectionContext first. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:48:40,527 [1070650]   WARN - #com.augmentcode.intellij.chat.ChatWebviewMessageClient$Companion - Workspace guidelines are empty, not including in chat request. 
2025-09-23 09:48:40,680 [1070803]   WARN - #com.augmentcode.intellij.webviews.AbstractWebviewMessagingService - Failed to parse webview message as protobuf.
Exception: Missing type url when parsing: {"type":"can-show-terminal","data":{"terminalId":22}}
Message: {"type":"async-wrapper","requestId":"fbaca338-0eb5-4a1e-9ceb-dba6f4750ce0","error":null,"baseMsg":{"type":"can-show-terminal","data":{"terminalId":22}},"destination":"host"}
2025-09-23 09:48:40,681 [1070804]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_ERROR]: Failed to check if terminal can be shown: Error: Unable to parse webview message: {"type":"async-wrapper","requestId":"fbaca338-0eb5-4a1e-9ceb-dba6f4750ce0","error":null,"baseMsg":{"type":"can-show-terminal","data":{"terminalId":22}},"destination":"host"} [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:48:41,003 [1071126]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: efece068d0aa8516cecdc6330ea74e4e53b5a7282fe35a8149e2a1596277c1e5 -> 2d76c3e5c2378039569bc9a388950e6519b0f51bd89732b24a4ef46de356b7c5
2025-09-23 09:48:44,439 [1074562]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (87ff3a9535123beb2708ba9f7b9f48b4c51e82c94ea47c9f462d5c00c40fa3b2) is unknown but expected to be uploaded
2025-09-23 09:48:44,439 [1074562]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (74899664a62dbd6ad64bd33a1818b55aed7003c5e085ebacadb505aeab0563c7) is unknown but expected to be uploaded
2025-09-23 09:48:44,439 [1074562]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (74899664a62dbd6ad64bd33a1818b55aed7003c5e085ebacadb505aeab0563c7) is unknown but expected to be uploaded
2025-09-23 09:48:44,439 [1074562]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (74899664a62dbd6ad64bd33a1818b55aed7003c5e085ebacadb505aeab0563c7) is unknown but expected to be uploaded
2025-09-23 09:48:44,439 [1074562]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (74899664a62dbd6ad64bd33a1818b55aed7003c5e085ebacadb505aeab0563c7) is unknown but expected to be uploaded
2025-09-23 09:48:44,439 [1074562]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (74899664a62dbd6ad64bd33a1818b55aed7003c5e085ebacadb505aeab0563c7) is unknown but expected to be uploaded
2025-09-23 09:48:44,439 [1074562]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (74899664a62dbd6ad64bd33a1818b55aed7003c5e085ebacadb505aeab0563c7) is unknown but expected to be uploaded
2025-09-23 09:48:44,439 [1074562]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (74899664a62dbd6ad64bd33a1818b55aed7003c5e085ebacadb505aeab0563c7) is unknown but expected to be uploaded
2025-09-23 09:48:44,439 [1074562]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (74899664a62dbd6ad64bd33a1818b55aed7003c5e085ebacadb505aeab0563c7) is unknown but expected to be uploaded
2025-09-23 09:48:44,439 [1074562]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (74899664a62dbd6ad64bd33a1818b55aed7003c5e085ebacadb505aeab0563c7) is unknown but expected to be uploaded
2025-09-23 09:48:44,439 [1074562]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (74899664a62dbd6ad64bd33a1818b55aed7003c5e085ebacadb505aeab0563c7) is unknown but expected to be uploaded
2025-09-23 09:48:44,439 [1074562]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (74899664a62dbd6ad64bd33a1818b55aed7003c5e085ebacadb505aeab0563c7) is unknown but expected to be uploaded
2025-09-23 09:48:44,439 [1074562]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (74899664a62dbd6ad64bd33a1818b55aed7003c5e085ebacadb505aeab0563c7) is unknown but expected to be uploaded
2025-09-23 09:48:44,439 [1074562]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (87ff3a9535123beb2708ba9f7b9f48b4c51e82c94ea47c9f462d5c00c40fa3b2) is unknown but expected to be uploaded
2025-09-23 09:48:44,439 [1074562]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (87ff3a9535123beb2708ba9f7b9f48b4c51e82c94ea47c9f462d5c00c40fa3b2) is unknown but expected to be uploaded
2025-09-23 09:48:44,439 [1074562]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (87ff3a9535123beb2708ba9f7b9f48b4c51e82c94ea47c9f462d5c00c40fa3b2) is unknown but expected to be uploaded
2025-09-23 09:48:44,439 [1074562]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (87ff3a9535123beb2708ba9f7b9f48b4c51e82c94ea47c9f462d5c00c40fa3b2) is unknown but expected to be uploaded
2025-09-23 09:48:44,439 [1074562]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (87ff3a9535123beb2708ba9f7b9f48b4c51e82c94ea47c9f462d5c00c40fa3b2) is unknown but expected to be uploaded
2025-09-23 09:48:44,439 [1074562]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (87ff3a9535123beb2708ba9f7b9f48b4c51e82c94ea47c9f462d5c00c40fa3b2) is unknown but expected to be uploaded
2025-09-23 09:48:44,439 [1074562]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (87ff3a9535123beb2708ba9f7b9f48b4c51e82c94ea47c9f462d5c00c40fa3b2) is unknown but expected to be uploaded
2025-09-23 09:48:44,439 [1074562]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (87ff3a9535123beb2708ba9f7b9f48b4c51e82c94ea47c9f462d5c00c40fa3b2) is unknown but expected to be uploaded
2025-09-23 09:48:44,439 [1074562]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (87ff3a9535123beb2708ba9f7b9f48b4c51e82c94ea47c9f462d5c00c40fa3b2) is unknown but expected to be uploaded
2025-09-23 09:48:44,439 [1074562]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (87ff3a9535123beb2708ba9f7b9f48b4c51e82c94ea47c9f462d5c00c40fa3b2) is unknown but expected to be uploaded
2025-09-23 09:48:44,439 [1074562]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (87ff3a9535123beb2708ba9f7b9f48b4c51e82c94ea47c9f462d5c00c40fa3b2) is unknown but expected to be uploaded
2025-09-23 09:48:44,439 [1074562]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (2d76c3e5c2378039569bc9a388950e6519b0f51bd89732b24a4ef46de356b7c5) is unknown but expected to be uploaded
2025-09-23 09:48:44,439 [1074562]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (2d76c3e5c2378039569bc9a388950e6519b0f51bd89732b24a4ef46de356b7c5) is unknown but expected to be uploaded
2025-09-23 09:48:44,439 [1074562]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (2d76c3e5c2378039569bc9a388950e6519b0f51bd89732b24a4ef46de356b7c5) is unknown but expected to be uploaded
2025-09-23 09:48:44,439 [1074562]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (2d76c3e5c2378039569bc9a388950e6519b0f51bd89732b24a4ef46de356b7c5) is unknown but expected to be uploaded
2025-09-23 09:48:44,439 [1074562]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (2d76c3e5c2378039569bc9a388950e6519b0f51bd89732b24a4ef46de356b7c5) is unknown but expected to be uploaded
2025-09-23 09:48:44,439 [1074562]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (2d76c3e5c2378039569bc9a388950e6519b0f51bd89732b24a4ef46de356b7c5) is unknown but expected to be uploaded
2025-09-23 09:48:44,439 [1074562]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (2d76c3e5c2378039569bc9a388950e6519b0f51bd89732b24a4ef46de356b7c5) is unknown but expected to be uploaded
2025-09-23 09:48:44,598 [1074721]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 94026e4c962fe0f771d84bca3c81dadf62205186690aa4cbebc264ad43bff017 -> 2d76c3e5c2378039569bc9a388950e6519b0f51bd89732b24a4ef46de356b7c5
2025-09-23 09:48:44,836 [1074959]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: No exchange with this request ID found. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:48:45,332 [1075455]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 87ff3a9535123beb2708ba9f7b9f48b4c51e82c94ea47c9f462d5c00c40fa3b2 -> 2d76c3e5c2378039569bc9a388950e6519b0f51bd89732b24a4ef46de356b7c5
2025-09-23 09:48:45,861 [1075984]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 74899664a62dbd6ad64bd33a1818b55aed7003c5e085ebacadb505aeab0563c7 -> 2d76c3e5c2378039569bc9a388950e6519b0f51bd89732b24a4ef46de356b7c5
2025-09-23 09:48:45,981 [1076104]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: IntersectionDisplay context not found.  Call setIntersectionContext first. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:48:46,098 [1076221]   WARN - #com.augmentcode.intellij.chat.ChatWebviewMessageClient$Companion - Workspace guidelines are empty, not including in chat request. 
2025-09-23 09:48:46,268 [1076391]   WARN - #com.augmentcode.intellij.webviews.AbstractWebviewMessagingService - Failed to parse webview message as protobuf.
Exception: Missing type url when parsing: {"type":"can-show-terminal","data":{"terminalId":22}}
Message: {"type":"async-wrapper","requestId":"9bb2e00c-35af-4d57-91ae-3e4b014ef817","error":null,"baseMsg":{"type":"can-show-terminal","data":{"terminalId":22}},"destination":"host"}
2025-09-23 09:48:46,271 [1076394]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_ERROR]: Failed to check if terminal can be shown: Error: Unable to parse webview message: {"type":"async-wrapper","requestId":"9bb2e00c-35af-4d57-91ae-3e4b014ef817","error":null,"baseMsg":{"type":"can-show-terminal","data":{"terminalId":22}},"destination":"host"} [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:48:46,590 [1076713]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 74899664a62dbd6ad64bd33a1818b55aed7003c5e085ebacadb505aeab0563c7 -> 2d76c3e5c2378039569bc9a388950e6519b0f51bd89732b24a4ef46de356b7c5
2025-09-23 09:48:47,344 [1077467]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 74899664a62dbd6ad64bd33a1818b55aed7003c5e085ebacadb505aeab0563c7 -> 2d76c3e5c2378039569bc9a388950e6519b0f51bd89732b24a4ef46de356b7c5
2025-09-23 09:48:48,124 [1078247]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 74899664a62dbd6ad64bd33a1818b55aed7003c5e085ebacadb505aeab0563c7 -> 2d76c3e5c2378039569bc9a388950e6519b0f51bd89732b24a4ef46de356b7c5
2025-09-23 09:48:49,010 [1079133]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 74899664a62dbd6ad64bd33a1818b55aed7003c5e085ebacadb505aeab0563c7 -> 2d76c3e5c2378039569bc9a388950e6519b0f51bd89732b24a4ef46de356b7c5
2025-09-23 09:48:49,350 [1079473]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: No exchange with this request ID found. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:48:49,821 [1079944]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 74899664a62dbd6ad64bd33a1818b55aed7003c5e085ebacadb505aeab0563c7 -> 2d76c3e5c2378039569bc9a388950e6519b0f51bd89732b24a4ef46de356b7c5
2025-09-23 09:48:50,770 [1080893]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: IntersectionDisplay context not found.  Call setIntersectionContext first. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:48:50,773 [1080896]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 87ff3a9535123beb2708ba9f7b9f48b4c51e82c94ea47c9f462d5c00c40fa3b2 -> 2d76c3e5c2378039569bc9a388950e6519b0f51bd89732b24a4ef46de356b7c5
2025-09-23 09:48:50,911 [1081034]   WARN - #com.augmentcode.intellij.chat.ChatWebviewMessageClient$Companion - Workspace guidelines are empty, not including in chat request. 
2025-09-23 09:48:51,074 [1081197]   WARN - #com.augmentcode.intellij.webviews.AbstractWebviewMessagingService - Failed to parse webview message as protobuf.
Exception: Missing type url when parsing: {"type":"can-show-terminal","data":{"terminalId":22}}
Message: {"type":"async-wrapper","requestId":"605ea2b5-da93-4229-9c8a-c8ce26d4af3e","error":null,"baseMsg":{"type":"can-show-terminal","data":{"terminalId":22}},"destination":"host"}
2025-09-23 09:48:51,075 [1081198]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_ERROR]: Failed to check if terminal can be shown: Error: Unable to parse webview message: {"type":"async-wrapper","requestId":"605ea2b5-da93-4229-9c8a-c8ce26d4af3e","error":null,"baseMsg":{"type":"can-show-terminal","data":{"terminalId":22}},"destination":"host"} [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:48:51,414 [1081537]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 87ff3a9535123beb2708ba9f7b9f48b4c51e82c94ea47c9f462d5c00c40fa3b2 -> 2d76c3e5c2378039569bc9a388950e6519b0f51bd89732b24a4ef46de356b7c5
2025-09-23 09:48:51,682 [1081805]   WARN - #c.i.e.wsl - Failed to get WSL mount root for Ubuntu: DOS working directory is expected, but got \\wsl.localhost\Ubuntu\home\oni\audio-transcriber
2025-09-23 09:48:52,074 [1082197]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 87ff3a9535123beb2708ba9f7b9f48b4c51e82c94ea47c9f462d5c00c40fa3b2 -> 2ca0e0a90049e6084c6793a003a981988483134749e87c8e5b877835782c5dcc
2025-09-23 09:48:52,761 [1082884]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 87ff3a9535123beb2708ba9f7b9f48b4c51e82c94ea47c9f462d5c00c40fa3b2 -> 2ca0e0a90049e6084c6793a003a981988483134749e87c8e5b877835782c5dcc
2025-09-23 09:48:53,252 [1083375]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 87ff3a9535123beb2708ba9f7b9f48b4c51e82c94ea47c9f462d5c00c40fa3b2 -> 2ca0e0a90049e6084c6793a003a981988483134749e87c8e5b877835782c5dcc
2025-09-23 09:48:53,730 [1083853]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 2d76c3e5c2378039569bc9a388950e6519b0f51bd89732b24a4ef46de356b7c5 -> 2ca0e0a90049e6084c6793a003a981988483134749e87c8e5b877835782c5dcc
2025-09-23 09:48:54,314 [1084437]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 2d76c3e5c2378039569bc9a388950e6519b0f51bd89732b24a4ef46de356b7c5 -> 2ca0e0a90049e6084c6793a003a981988483134749e87c8e5b877835782c5dcc
2025-09-23 09:48:54,716 [1084839]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: No exchange with this request ID found. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:48:55,090 [1085213]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 2d76c3e5c2378039569bc9a388950e6519b0f51bd89732b24a4ef46de356b7c5 -> 2ca0e0a90049e6084c6793a003a981988483134749e87c8e5b877835782c5dcc
2025-09-23 09:48:55,708 [1085831]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 2d76c3e5c2378039569bc9a388950e6519b0f51bd89732b24a4ef46de356b7c5 -> a7ac6e33cb21386b30c1a7cf99dcecec0dfeb944a82fa912b8af95018d93e0cb
2025-09-23 09:48:56,277 [1086400]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 74899664a62dbd6ad64bd33a1818b55aed7003c5e085ebacadb505aeab0563c7 -> a7ac6e33cb21386b30c1a7cf99dcecec0dfeb944a82fa912b8af95018d93e0cb
2025-09-23 09:48:56,775 [1086898]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 74899664a62dbd6ad64bd33a1818b55aed7003c5e085ebacadb505aeab0563c7 -> a7ac6e33cb21386b30c1a7cf99dcecec0dfeb944a82fa912b8af95018d93e0cb
2025-09-23 09:48:57,443 [1087566]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 74899664a62dbd6ad64bd33a1818b55aed7003c5e085ebacadb505aeab0563c7 -> a7ac6e33cb21386b30c1a7cf99dcecec0dfeb944a82fa912b8af95018d93e0cb
2025-09-23 09:48:57,724 [1087847]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: IntersectionDisplay context not found.  Call setIntersectionContext first. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:48:58,278 [1088401]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 87ff3a9535123beb2708ba9f7b9f48b4c51e82c94ea47c9f462d5c00c40fa3b2 -> a7ac6e33cb21386b30c1a7cf99dcecec0dfeb944a82fa912b8af95018d93e0cb
2025-09-23 09:48:58,823 [1088946]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: Loading changes  for a7f24186-d3ed-477a-b9f4-d26b5c9ad785 retry 0:  Error: No changes found [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:48:58,828 [1088951]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 87ff3a9535123beb2708ba9f7b9f48b4c51e82c94ea47c9f462d5c00c40fa3b2 -> a7ac6e33cb21386b30c1a7cf99dcecec0dfeb944a82fa912b8af95018d93e0cb
2025-09-23 09:48:58,836 [1088959]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: Loading changes  for a7f24186-d3ed-477a-b9f4-d26b5c9ad785 retry 1:  Error: No changes found [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:48:58,853 [1088976]   WARN - #com.augmentcode.intellij.chat.ChatWebviewMessageClient$Companion - Workspace guidelines are empty, not including in chat request. 
2025-09-23 09:48:59,327 [1089450]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: Loading changes  for a7f24186-d3ed-477a-b9f4-d26b5c9ad785 retry 2:  Error: No changes found [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:48:59,333 [1089456]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: Loading changes  for a7f24186-d3ed-477a-b9f4-d26b5c9ad785 retry 3:  Error: No changes found [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:48:59,333 [1089456]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: Loading changes failed for a7f24186-d3ed-477a-b9f4-d26b5c9ad785:  Error: No changes found [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:48:59,377 [1089500]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 87ff3a9535123beb2708ba9f7b9f48b4c51e82c94ea47c9f462d5c00c40fa3b2 -> a7ac6e33cb21386b30c1a7cf99dcecec0dfeb944a82fa912b8af95018d93e0cb
2025-09-23 09:48:59,829 [1089952]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: Loading changes  for a7f24186-d3ed-477a-b9f4-d26b5c9ad785 retry 4:  Error: No changes found [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:48:59,829 [1089952]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: Loading changes failed for a7f24186-d3ed-477a-b9f4-d26b5c9ad785:  Error: No changes found [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:48:59,865 [1089988]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 2d76c3e5c2378039569bc9a388950e6519b0f51bd89732b24a4ef46de356b7c5 -> a7ac6e33cb21386b30c1a7cf99dcecec0dfeb944a82fa912b8af95018d93e0cb
2025-09-23 09:49:00,493 [1090616]   WARN - #com.augmentcode.intellij.sentry.SentryMetadataCollector - Git ls-files command failed with exit code: 128
2025-09-23 09:49:00,615 [1090738]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 74899664a62dbd6ad64bd33a1818b55aed7003c5e085ebacadb505aeab0563c7 -> a7ac6e33cb21386b30c1a7cf99dcecec0dfeb944a82fa912b8af95018d93e0cb
2025-09-23 09:49:01,118 [1091241]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 74899664a62dbd6ad64bd33a1818b55aed7003c5e085ebacadb505aeab0563c7 -> a7ac6e33cb21386b30c1a7cf99dcecec0dfeb944a82fa912b8af95018d93e0cb
2025-09-23 09:49:01,835 [1091958]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 87ff3a9535123beb2708ba9f7b9f48b4c51e82c94ea47c9f462d5c00c40fa3b2 -> a7ac6e33cb21386b30c1a7cf99dcecec0dfeb944a82fa912b8af95018d93e0cb
2025-09-23 09:49:02,571 [1092694]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: No exchange with this request ID found. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:49:02,644 [1092767]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 2d76c3e5c2378039569bc9a388950e6519b0f51bd89732b24a4ef46de356b7c5 -> a7ac6e33cb21386b30c1a7cf99dcecec0dfeb944a82fa912b8af95018d93e0cb
2025-09-23 09:49:03,922 [1094045]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 87ff3a9535123beb2708ba9f7b9f48b4c51e82c94ea47c9f462d5c00c40fa3b2 -> a7ac6e33cb21386b30c1a7cf99dcecec0dfeb944a82fa912b8af95018d93e0cb
2025-09-23 09:49:03,948 [1094071]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: IntersectionDisplay context not found.  Call setIntersectionContext first. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:49:04,231 [1094354]   WARN - #com.augmentcode.intellij.webviews.AbstractWebviewMessagingService - Failed to parse webview message as protobuf.
Exception: Missing type url when parsing: {"type":"can-show-terminal","data":{"command":"npm start"}}
Message: {"type":"async-wrapper","requestId":"6b2e6315-d7f6-4f60-b90c-dabe395575b5","error":null,"baseMsg":{"type":"can-show-terminal","data":{"command":"npm start"}},"destination":"host"}
2025-09-23 09:49:04,233 [1094356]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_ERROR]: Failed to check if terminal can be shown: Error: Unable to parse webview message: {"type":"async-wrapper","requestId":"6b2e6315-d7f6-4f60-b90c-dabe395575b5","error":null,"baseMsg":{"type":"can-show-terminal","data":{"command":"npm start"}},"destination":"host"} [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:49:04,668 [1094791]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 2d76c3e5c2378039569bc9a388950e6519b0f51bd89732b24a4ef46de356b7c5 -> a7ac6e33cb21386b30c1a7cf99dcecec0dfeb944a82fa912b8af95018d93e0cb
2025-09-23 09:49:05,251 [1095374]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 87ff3a9535123beb2708ba9f7b9f48b4c51e82c94ea47c9f462d5c00c40fa3b2 -> a7ac6e33cb21386b30c1a7cf99dcecec0dfeb944a82fa912b8af95018d93e0cb
2025-09-23 09:49:08,645 [1098768]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (2d76c3e5c2378039569bc9a388950e6519b0f51bd89732b24a4ef46de356b7c5) is unknown but expected to be uploaded
2025-09-23 09:49:08,645 [1098768]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (2d76c3e5c2378039569bc9a388950e6519b0f51bd89732b24a4ef46de356b7c5) is unknown but expected to be uploaded
2025-09-23 09:49:08,645 [1098768]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (2d76c3e5c2378039569bc9a388950e6519b0f51bd89732b24a4ef46de356b7c5) is unknown but expected to be uploaded
2025-09-23 09:49:08,645 [1098768]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (2d76c3e5c2378039569bc9a388950e6519b0f51bd89732b24a4ef46de356b7c5) is unknown but expected to be uploaded
2025-09-23 09:49:08,645 [1098768]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (2d76c3e5c2378039569bc9a388950e6519b0f51bd89732b24a4ef46de356b7c5) is unknown but expected to be uploaded
2025-09-23 09:49:08,645 [1098768]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (2d76c3e5c2378039569bc9a388950e6519b0f51bd89732b24a4ef46de356b7c5) is unknown but expected to be uploaded
2025-09-23 09:49:08,645 [1098768]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (2d76c3e5c2378039569bc9a388950e6519b0f51bd89732b24a4ef46de356b7c5) is unknown but expected to be uploaded
2025-09-23 09:49:08,645 [1098768]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (2d76c3e5c2378039569bc9a388950e6519b0f51bd89732b24a4ef46de356b7c5) is unknown but expected to be uploaded
2025-09-23 09:49:08,645 [1098768]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (2d76c3e5c2378039569bc9a388950e6519b0f51bd89732b24a4ef46de356b7c5) is unknown but expected to be uploaded
2025-09-23 09:49:08,645 [1098768]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (2d76c3e5c2378039569bc9a388950e6519b0f51bd89732b24a4ef46de356b7c5) is unknown but expected to be uploaded
2025-09-23 09:49:08,645 [1098768]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (a7ac6e33cb21386b30c1a7cf99dcecec0dfeb944a82fa912b8af95018d93e0cb) is unknown but expected to be uploaded
2025-09-23 09:49:08,645 [1098768]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (a7ac6e33cb21386b30c1a7cf99dcecec0dfeb944a82fa912b8af95018d93e0cb) is unknown but expected to be uploaded
2025-09-23 09:49:08,645 [1098768]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (a7ac6e33cb21386b30c1a7cf99dcecec0dfeb944a82fa912b8af95018d93e0cb) is unknown but expected to be uploaded
2025-09-23 09:49:08,645 [1098768]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (a7ac6e33cb21386b30c1a7cf99dcecec0dfeb944a82fa912b8af95018d93e0cb) is unknown but expected to be uploaded
2025-09-23 09:49:08,645 [1098768]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (a7ac6e33cb21386b30c1a7cf99dcecec0dfeb944a82fa912b8af95018d93e0cb) is unknown but expected to be uploaded
2025-09-23 09:49:08,645 [1098768]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (a7ac6e33cb21386b30c1a7cf99dcecec0dfeb944a82fa912b8af95018d93e0cb) is unknown but expected to be uploaded
2025-09-23 09:49:08,645 [1098768]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (a7ac6e33cb21386b30c1a7cf99dcecec0dfeb944a82fa912b8af95018d93e0cb) is unknown but expected to be uploaded
2025-09-23 09:49:08,645 [1098768]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (a7ac6e33cb21386b30c1a7cf99dcecec0dfeb944a82fa912b8af95018d93e0cb) is unknown but expected to be uploaded
2025-09-23 09:49:08,645 [1098768]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (a7ac6e33cb21386b30c1a7cf99dcecec0dfeb944a82fa912b8af95018d93e0cb) is unknown but expected to be uploaded
2025-09-23 09:49:08,645 [1098768]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (a7ac6e33cb21386b30c1a7cf99dcecec0dfeb944a82fa912b8af95018d93e0cb) is unknown but expected to be uploaded
2025-09-23 09:49:08,645 [1098768]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (a7ac6e33cb21386b30c1a7cf99dcecec0dfeb944a82fa912b8af95018d93e0cb) is unknown but expected to be uploaded
2025-09-23 09:49:08,645 [1098768]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (a7ac6e33cb21386b30c1a7cf99dcecec0dfeb944a82fa912b8af95018d93e0cb) is unknown but expected to be uploaded
2025-09-23 09:49:08,645 [1098768]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (a7ac6e33cb21386b30c1a7cf99dcecec0dfeb944a82fa912b8af95018d93e0cb) is unknown but expected to be uploaded
2025-09-23 09:49:08,645 [1098768]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (a7ac6e33cb21386b30c1a7cf99dcecec0dfeb944a82fa912b8af95018d93e0cb) is unknown but expected to be uploaded
2025-09-23 09:49:08,645 [1098768]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (a7ac6e33cb21386b30c1a7cf99dcecec0dfeb944a82fa912b8af95018d93e0cb) is unknown but expected to be uploaded
2025-09-23 09:49:08,645 [1098768]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (a7ac6e33cb21386b30c1a7cf99dcecec0dfeb944a82fa912b8af95018d93e0cb) is unknown but expected to be uploaded
2025-09-23 09:49:09,479 [1099602]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 74899664a62dbd6ad64bd33a1818b55aed7003c5e085ebacadb505aeab0563c7 -> a7ac6e33cb21386b30c1a7cf99dcecec0dfeb944a82fa912b8af95018d93e0cb
2025-09-23 09:49:09,969 [1100092]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 2d76c3e5c2378039569bc9a388950e6519b0f51bd89732b24a4ef46de356b7c5 -> a7ac6e33cb21386b30c1a7cf99dcecec0dfeb944a82fa912b8af95018d93e0cb
2025-09-23 09:49:10,587 [1100710]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 2d76c3e5c2378039569bc9a388950e6519b0f51bd89732b24a4ef46de356b7c5 -> 88e947bf6d10beb62028555ed4c0bce9f2c55a1d77654daf206625d994724b0c
2025-09-23 09:49:11,325 [1101448]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 2d76c3e5c2378039569bc9a388950e6519b0f51bd89732b24a4ef46de356b7c5 -> 88e947bf6d10beb62028555ed4c0bce9f2c55a1d77654daf206625d994724b0c
2025-09-23 09:49:11,769 [1101892]   WARN - #com.augmentcode.intellij.webviews.AbstractWebviewMessagingService - Failed to parse webview message as protobuf.
Exception: Missing type url when parsing: {"type":"can-show-terminal","data":{"command":"npm start"}}
Message: {"type":"async-wrapper","requestId":"135f1fa4-d480-4e1e-a2a2-876234b61ef8","error":null,"baseMsg":{"type":"can-show-terminal","data":{"command":"npm start"}},"destination":"host"}
2025-09-23 09:49:11,837 [1101960]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_ERROR]: Failed to check if terminal can be shown: Error: Unable to parse webview message: {"type":"async-wrapper","requestId":"135f1fa4-d480-4e1e-a2a2-876234b61ef8","error":null,"baseMsg":{"type":"can-show-terminal","data":{"command":"npm start"}},"destination":"host"} [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:49:11,942 [1102065]   WARN - #com.augmentcode.intellij.chat.ChatWebviewMessageClient$Companion - Workspace guidelines are empty, not including in chat request. 
2025-09-23 09:49:12,033 [1102156]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 2d76c3e5c2378039569bc9a388950e6519b0f51bd89732b24a4ef46de356b7c5 -> 88e947bf6d10beb62028555ed4c0bce9f2c55a1d77654daf206625d994724b0c
2025-09-23 09:49:12,322 [1102445]   WARN - #c.i.e.wsl - Failed to get WSL mount root for Ubuntu: DOS working directory is expected, but got \\wsl.localhost\Ubuntu\home\oni\audio-transcriber
2025-09-23 09:49:12,723 [1102846]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 2d76c3e5c2378039569bc9a388950e6519b0f51bd89732b24a4ef46de356b7c5 -> d4e2ebd2d3396372dfb825784b1f28971cb173c084b31604dba3f82c20e3e5f3
2025-09-23 09:49:13,276 [1103399]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: a7ac6e33cb21386b30c1a7cf99dcecec0dfeb944a82fa912b8af95018d93e0cb -> d4e2ebd2d3396372dfb825784b1f28971cb173c084b31604dba3f82c20e3e5f3
2025-09-23 09:49:14,037 [1104160]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: a7ac6e33cb21386b30c1a7cf99dcecec0dfeb944a82fa912b8af95018d93e0cb -> d4e2ebd2d3396372dfb825784b1f28971cb173c084b31604dba3f82c20e3e5f3
2025-09-23 09:49:15,036 [1105159]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: a7ac6e33cb21386b30c1a7cf99dcecec0dfeb944a82fa912b8af95018d93e0cb -> d4e2ebd2d3396372dfb825784b1f28971cb173c084b31604dba3f82c20e3e5f3
2025-09-23 09:49:15,178 [1105301]   WARN - #c.i.e.wsl - Failed to get WSL mount root for Ubuntu: DOS working directory is expected, but got \\wsl.localhost\Ubuntu\home\oni\audio-transcriber
2025-09-23 09:49:15,537 [1105660]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: a7ac6e33cb21386b30c1a7cf99dcecec0dfeb944a82fa912b8af95018d93e0cb -> d4e2ebd2d3396372dfb825784b1f28971cb173c084b31604dba3f82c20e3e5f3
2025-09-23 09:49:16,090 [1106213]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: a7ac6e33cb21386b30c1a7cf99dcecec0dfeb944a82fa912b8af95018d93e0cb -> d4e2ebd2d3396372dfb825784b1f28971cb173c084b31604dba3f82c20e3e5f3
2025-09-23 09:49:16,329 [1106452]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: No exchange with this request ID found. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:49:16,841 [1106964]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: a7ac6e33cb21386b30c1a7cf99dcecec0dfeb944a82fa912b8af95018d93e0cb -> d4e2ebd2d3396372dfb825784b1f28971cb173c084b31604dba3f82c20e3e5f3
2025-09-23 09:49:16,871 [1106994]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: IntersectionDisplay context not found.  Call setIntersectionContext first. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:49:17,008 [1107131]   WARN - #com.augmentcode.intellij.chat.ChatWebviewMessageClient$Companion - Workspace guidelines are empty, not including in chat request. 
2025-09-23 09:49:17,193 [1107316]   WARN - #com.augmentcode.intellij.webviews.AbstractWebviewMessagingService - Failed to parse webview message as protobuf.
Exception: Missing type url when parsing: {"type":"can-show-terminal","data":{"terminalId":23}}
Message: {"type":"async-wrapper","requestId":"c34a0b6b-597c-4702-8812-77e4d9c76de9","error":null,"baseMsg":{"type":"can-show-terminal","data":{"terminalId":23}},"destination":"host"}
2025-09-23 09:49:17,195 [1107318]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_ERROR]: Failed to check if terminal can be shown: Error: Unable to parse webview message: {"type":"async-wrapper","requestId":"c34a0b6b-597c-4702-8812-77e4d9c76de9","error":null,"baseMsg":{"type":"can-show-terminal","data":{"terminalId":23}},"destination":"host"} [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:49:17,315 [1107438]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: a7ac6e33cb21386b30c1a7cf99dcecec0dfeb944a82fa912b8af95018d93e0cb -> d4e2ebd2d3396372dfb825784b1f28971cb173c084b31604dba3f82c20e3e5f3
2025-09-23 09:49:17,820 [1107943]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: a7ac6e33cb21386b30c1a7cf99dcecec0dfeb944a82fa912b8af95018d93e0cb -> d4e2ebd2d3396372dfb825784b1f28971cb173c084b31604dba3f82c20e3e5f3
2025-09-23 09:49:18,406 [1108529]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 2d76c3e5c2378039569bc9a388950e6519b0f51bd89732b24a4ef46de356b7c5 -> d4e2ebd2d3396372dfb825784b1f28971cb173c084b31604dba3f82c20e3e5f3
2025-09-23 09:49:19,139 [1109262]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 2d76c3e5c2378039569bc9a388950e6519b0f51bd89732b24a4ef46de356b7c5 -> d4e2ebd2d3396372dfb825784b1f28971cb173c084b31604dba3f82c20e3e5f3
2025-09-23 09:49:19,967 [1110090]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 2d76c3e5c2378039569bc9a388950e6519b0f51bd89732b24a4ef46de356b7c5 -> d4e2ebd2d3396372dfb825784b1f28971cb173c084b31604dba3f82c20e3e5f3
2025-09-23 09:49:20,475 [1110598]   WARN - #c.i.e.wsl - Failed to get WSL mount root for Ubuntu: DOS working directory is expected, but got \\wsl.localhost\Ubuntu\home\oni\audio-transcriber
2025-09-23 09:49:20,938 [1111061]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: a7ac6e33cb21386b30c1a7cf99dcecec0dfeb944a82fa912b8af95018d93e0cb -> d4e2ebd2d3396372dfb825784b1f28971cb173c084b31604dba3f82c20e3e5f3
2025-09-23 09:49:21,572 [1111695]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: a7ac6e33cb21386b30c1a7cf99dcecec0dfeb944a82fa912b8af95018d93e0cb -> 5fd1374d5295939ee8e530a0d7f0f1ada66c9e255f4e11f0335a0781b02ffa4f
2025-09-23 09:49:21,654 [1111777]   WARN - #c.i.e.wsl - Failed to get WSL mount root for Ubuntu: DOS working directory is expected, but got \\wsl.localhost\Ubuntu\home\oni\audio-transcriber
2025-09-23 09:49:21,679 [1111802]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: No exchange with this request ID found. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:49:22,214 [1112337]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: a7ac6e33cb21386b30c1a7cf99dcecec0dfeb944a82fa912b8af95018d93e0cb -> 75d01cd54c2bbe70352cffb588c9d6d5ff3dbd9d0f5bbe578a5fb31452b77e48
2025-09-23 09:49:22,835 [1112958]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: a7ac6e33cb21386b30c1a7cf99dcecec0dfeb944a82fa912b8af95018d93e0cb -> 75d01cd54c2bbe70352cffb588c9d6d5ff3dbd9d0f5bbe578a5fb31452b77e48
2025-09-23 09:49:23,064 [1113187]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: IntersectionDisplay context not found.  Call setIntersectionContext first. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:49:23,249 [1113372]   WARN - #com.augmentcode.intellij.chat.ChatWebviewMessageClient$Companion - Workspace guidelines are empty, not including in chat request. 
2025-09-23 09:49:23,376 [1113499]   WARN - #com.augmentcode.intellij.webviews.AbstractWebviewMessagingService - Failed to parse webview message as protobuf.
Exception: Missing type url when parsing: {"type":"can-show-terminal","data":{"terminalId":23}}
Message: {"type":"async-wrapper","requestId":"af8ae4b3-7f84-4b0c-a671-feffc40833ac","error":null,"baseMsg":{"type":"can-show-terminal","data":{"terminalId":23}},"destination":"host"}
2025-09-23 09:49:23,378 [1113501]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_ERROR]: Failed to check if terminal can be shown: Error: Unable to parse webview message: {"type":"async-wrapper","requestId":"af8ae4b3-7f84-4b0c-a671-feffc40833ac","error":null,"baseMsg":{"type":"can-show-terminal","data":{"terminalId":23}},"destination":"host"} [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:49:23,647 [1113770]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 2d76c3e5c2378039569bc9a388950e6519b0f51bd89732b24a4ef46de356b7c5 -> 75d01cd54c2bbe70352cffb588c9d6d5ff3dbd9d0f5bbe578a5fb31452b77e48
2025-09-23 09:49:24,583 [1114706]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: a7ac6e33cb21386b30c1a7cf99dcecec0dfeb944a82fa912b8af95018d93e0cb -> 75d01cd54c2bbe70352cffb588c9d6d5ff3dbd9d0f5bbe578a5fb31452b77e48
2025-09-23 09:49:25,464 [1115587]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: a7ac6e33cb21386b30c1a7cf99dcecec0dfeb944a82fa912b8af95018d93e0cb -> 75d01cd54c2bbe70352cffb588c9d6d5ff3dbd9d0f5bbe578a5fb31452b77e48
2025-09-23 09:49:25,534 [1115657]   WARN - #c.i.e.wsl - Failed to get WSL mount root for Ubuntu: DOS working directory is expected, but got \\wsl.localhost\Ubuntu\home\oni\audio-transcriber
2025-09-23 09:49:25,974 [1116097]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 2d76c3e5c2378039569bc9a388950e6519b0f51bd89732b24a4ef46de356b7c5 -> 75d01cd54c2bbe70352cffb588c9d6d5ff3dbd9d0f5bbe578a5fb31452b77e48
2025-09-23 09:49:26,231 [1116354]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: No exchange with this request ID found. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:49:26,588 [1116711]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 5fd1374d5295939ee8e530a0d7f0f1ada66c9e255f4e11f0335a0781b02ffa4f -> df8c97a94e9b07d9592c84487815f664a89f9d3d3dff50b684192c233c4e1c88
2025-09-23 09:49:27,497 [1117620]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: a7ac6e33cb21386b30c1a7cf99dcecec0dfeb944a82fa912b8af95018d93e0cb -> df8c97a94e9b07d9592c84487815f664a89f9d3d3dff50b684192c233c4e1c88
2025-09-23 09:49:28,969 [1119092]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: IntersectionDisplay context not found.  Call setIntersectionContext first. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:49:30,312 [1120435]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: Loading changes  for 3b0a0349-faaf-4772-88e5-90bbdf1e0bdc retry 0:  Error: No changes found [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:49:30,312 [1120435]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: Loading changes  for 3b0a0349-faaf-4772-88e5-90bbdf1e0bdc retry 1:  Error: No changes found [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:49:30,357 [1120480]   WARN - #com.augmentcode.intellij.chat.ChatWebviewMessageClient$Companion - Workspace guidelines are empty, not including in chat request. 
2025-09-23 09:49:30,818 [1120941]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: Loading changes  for 3b0a0349-faaf-4772-88e5-90bbdf1e0bdc retry 2:  Error: No changes found [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:49:30,818 [1120941]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: Loading changes  for 3b0a0349-faaf-4772-88e5-90bbdf1e0bdc retry 3:  Error: No changes found [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:49:30,819 [1120942]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: Loading changes failed for 3b0a0349-faaf-4772-88e5-90bbdf1e0bdc:  Error: No changes found [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:49:30,902 [1121025]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (a7ac6e33cb21386b30c1a7cf99dcecec0dfeb944a82fa912b8af95018d93e0cb) is unknown but expected to be uploaded
2025-09-23 09:49:30,902 [1121025]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (a7ac6e33cb21386b30c1a7cf99dcecec0dfeb944a82fa912b8af95018d93e0cb) is unknown but expected to be uploaded
2025-09-23 09:49:30,902 [1121025]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (88e947bf6d10beb62028555ed4c0bce9f2c55a1d77654daf206625d994724b0c) is unknown but expected to be uploaded
2025-09-23 09:49:30,902 [1121025]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (88e947bf6d10beb62028555ed4c0bce9f2c55a1d77654daf206625d994724b0c) is unknown but expected to be uploaded
2025-09-23 09:49:30,902 [1121025]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (88e947bf6d10beb62028555ed4c0bce9f2c55a1d77654daf206625d994724b0c) is unknown but expected to be uploaded
2025-09-23 09:49:30,902 [1121025]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (88e947bf6d10beb62028555ed4c0bce9f2c55a1d77654daf206625d994724b0c) is unknown but expected to be uploaded
2025-09-23 09:49:30,902 [1121025]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (d4e2ebd2d3396372dfb825784b1f28971cb173c084b31604dba3f82c20e3e5f3) is unknown but expected to be uploaded
2025-09-23 09:49:30,902 [1121025]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (88e947bf6d10beb62028555ed4c0bce9f2c55a1d77654daf206625d994724b0c) is unknown but expected to be uploaded
2025-09-23 09:49:30,902 [1121025]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (d4e2ebd2d3396372dfb825784b1f28971cb173c084b31604dba3f82c20e3e5f3) is unknown but expected to be uploaded
2025-09-23 09:49:30,902 [1121025]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (d4e2ebd2d3396372dfb825784b1f28971cb173c084b31604dba3f82c20e3e5f3) is unknown but expected to be uploaded
2025-09-23 09:49:30,902 [1121025]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (d4e2ebd2d3396372dfb825784b1f28971cb173c084b31604dba3f82c20e3e5f3) is unknown but expected to be uploaded
2025-09-23 09:49:30,902 [1121025]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (d4e2ebd2d3396372dfb825784b1f28971cb173c084b31604dba3f82c20e3e5f3) is unknown but expected to be uploaded
2025-09-23 09:49:30,903 [1121026]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (d4e2ebd2d3396372dfb825784b1f28971cb173c084b31604dba3f82c20e3e5f3) is unknown but expected to be uploaded
2025-09-23 09:49:30,903 [1121026]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (d4e2ebd2d3396372dfb825784b1f28971cb173c084b31604dba3f82c20e3e5f3) is unknown but expected to be uploaded
2025-09-23 09:49:30,903 [1121026]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (d4e2ebd2d3396372dfb825784b1f28971cb173c084b31604dba3f82c20e3e5f3) is unknown but expected to be uploaded
2025-09-23 09:49:30,903 [1121026]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (d4e2ebd2d3396372dfb825784b1f28971cb173c084b31604dba3f82c20e3e5f3) is unknown but expected to be uploaded
2025-09-23 09:49:30,903 [1121026]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (d4e2ebd2d3396372dfb825784b1f28971cb173c084b31604dba3f82c20e3e5f3) is unknown but expected to be uploaded
2025-09-23 09:49:30,903 [1121026]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (d4e2ebd2d3396372dfb825784b1f28971cb173c084b31604dba3f82c20e3e5f3) is unknown but expected to be uploaded
2025-09-23 09:49:30,903 [1121026]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (d4e2ebd2d3396372dfb825784b1f28971cb173c084b31604dba3f82c20e3e5f3) is unknown but expected to be uploaded
2025-09-23 09:49:30,903 [1121026]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (d4e2ebd2d3396372dfb825784b1f28971cb173c084b31604dba3f82c20e3e5f3) is unknown but expected to be uploaded
2025-09-23 09:49:30,903 [1121026]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (5fd1374d5295939ee8e530a0d7f0f1ada66c9e255f4e11f0335a0781b02ffa4f) is unknown but expected to be uploaded
2025-09-23 09:49:30,903 [1121026]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (75d01cd54c2bbe70352cffb588c9d6d5ff3dbd9d0f5bbe578a5fb31452b77e48) is unknown but expected to be uploaded
2025-09-23 09:49:30,903 [1121026]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (5fd1374d5295939ee8e530a0d7f0f1ada66c9e255f4e11f0335a0781b02ffa4f) is unknown but expected to be uploaded
2025-09-23 09:49:30,903 [1121026]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (75d01cd54c2bbe70352cffb588c9d6d5ff3dbd9d0f5bbe578a5fb31452b77e48) is unknown but expected to be uploaded
2025-09-23 09:49:30,903 [1121026]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (75d01cd54c2bbe70352cffb588c9d6d5ff3dbd9d0f5bbe578a5fb31452b77e48) is unknown but expected to be uploaded
2025-09-23 09:49:30,903 [1121026]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (75d01cd54c2bbe70352cffb588c9d6d5ff3dbd9d0f5bbe578a5fb31452b77e48) is unknown but expected to be uploaded
2025-09-23 09:49:30,903 [1121026]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (75d01cd54c2bbe70352cffb588c9d6d5ff3dbd9d0f5bbe578a5fb31452b77e48) is unknown but expected to be uploaded
2025-09-23 09:49:30,903 [1121026]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (75d01cd54c2bbe70352cffb588c9d6d5ff3dbd9d0f5bbe578a5fb31452b77e48) is unknown but expected to be uploaded
2025-09-23 09:49:31,323 [1121446]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: Loading changes  for 3b0a0349-faaf-4772-88e5-90bbdf1e0bdc retry 4:  Error: No changes found [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:49:31,324 [1121447]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: Loading changes failed for 3b0a0349-faaf-4772-88e5-90bbdf1e0bdc:  Error: No changes found [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:49:31,418 [1121541]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: a7ac6e33cb21386b30c1a7cf99dcecec0dfeb944a82fa912b8af95018d93e0cb -> df8c97a94e9b07d9592c84487815f664a89f9d3d3dff50b684192c233c4e1c88
2025-09-23 09:49:32,683 [1122806]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: a7ac6e33cb21386b30c1a7cf99dcecec0dfeb944a82fa912b8af95018d93e0cb -> df8c97a94e9b07d9592c84487815f664a89f9d3d3dff50b684192c233c4e1c88
2025-09-23 09:49:33,339 [1123462]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 88e947bf6d10beb62028555ed4c0bce9f2c55a1d77654daf206625d994724b0c -> df8c97a94e9b07d9592c84487815f664a89f9d3d3dff50b684192c233c4e1c88
2025-09-23 09:49:34,031 [1124154]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 88e947bf6d10beb62028555ed4c0bce9f2c55a1d77654daf206625d994724b0c -> df8c97a94e9b07d9592c84487815f664a89f9d3d3dff50b684192c233c4e1c88
2025-09-23 09:49:34,306 [1124429]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: No exchange with this request ID found. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:49:34,792 [1124915]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 88e947bf6d10beb62028555ed4c0bce9f2c55a1d77654daf206625d994724b0c -> df8c97a94e9b07d9592c84487815f664a89f9d3d3dff50b684192c233c4e1c88
2025-09-23 09:49:35,408 [1125531]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: IntersectionDisplay context not found.  Call setIntersectionContext first. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:49:35,466 [1125589]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: d4e2ebd2d3396372dfb825784b1f28971cb173c084b31604dba3f82c20e3e5f3 -> df8c97a94e9b07d9592c84487815f664a89f9d3d3dff50b684192c233c4e1c88
2025-09-23 09:49:35,707 [1125830]   WARN - #com.augmentcode.intellij.webviews.AbstractWebviewMessagingService - Failed to parse webview message as protobuf.
Exception: Missing type url when parsing: {"type":"can-show-terminal","data":{"terminalId":23}}
Message: {"type":"async-wrapper","requestId":"3639c01d-1b1b-4da1-b607-2991a649be2a","error":null,"baseMsg":{"type":"can-show-terminal","data":{"terminalId":23}},"destination":"host"}
2025-09-23 09:49:35,708 [1125831]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_ERROR]: Failed to check if terminal can be shown: Error: Unable to parse webview message: {"type":"async-wrapper","requestId":"3639c01d-1b1b-4da1-b607-2991a649be2a","error":null,"baseMsg":{"type":"can-show-terminal","data":{"terminalId":23}},"destination":"host"} [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:49:35,993 [1126116]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: d4e2ebd2d3396372dfb825784b1f28971cb173c084b31604dba3f82c20e3e5f3 -> df8c97a94e9b07d9592c84487815f664a89f9d3d3dff50b684192c233c4e1c88
2025-09-23 09:49:35,995 [1126118]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: IntersectionDisplay context not found.  Call setIntersectionContext first. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:49:36,318 [1126441]   WARN - #c.i.e.wsl - Failed to get WSL mount root for Ubuntu: DOS working directory is expected, but got \\wsl.localhost\Ubuntu\home\oni\audio-transcriber
2025-09-23 09:49:36,324 [1126447]   WARN - #com.augmentcode.intellij.webviews.AbstractWebviewMessagingService - Failed to parse webview message as protobuf.
Exception: Missing type url when parsing: {"type":"can-show-terminal","data":{"command":"npm start"}}
Message: {"type":"async-wrapper","requestId":"b1bcaaf1-dfe3-4f74-baec-2f10a014c1ea","error":null,"baseMsg":{"type":"can-show-terminal","data":{"command":"npm start"}},"destination":"host"}
2025-09-23 09:49:36,328 [1126451]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_ERROR]: Failed to check if terminal can be shown: Error: Unable to parse webview message: {"type":"async-wrapper","requestId":"b1bcaaf1-dfe3-4f74-baec-2f10a014c1ea","error":null,"baseMsg":{"type":"can-show-terminal","data":{"command":"npm start"}},"destination":"host"} [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:49:36,991 [1127114]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: d4e2ebd2d3396372dfb825784b1f28971cb173c084b31604dba3f82c20e3e5f3 -> 1ac4a866eea59c7e6125cbf0cd6d3058abfb933aadc9bc92dbcef47c746cfc1c
2025-09-23 09:49:37,929 [1128052]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: d4e2ebd2d3396372dfb825784b1f28971cb173c084b31604dba3f82c20e3e5f3 -> a4e14b967e136a0044c8fe28125c98f4fb1ef79c973c6a3cb0c9ae30d5c5f3f2
2025-09-23 09:49:38,755 [1128878]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: d4e2ebd2d3396372dfb825784b1f28971cb173c084b31604dba3f82c20e3e5f3 -> 250090169e0a4c485a179fdb071701160b7c619320427e74b90dda3aa135a3ef
2025-09-23 09:49:39,471 [1129594]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: d4e2ebd2d3396372dfb825784b1f28971cb173c084b31604dba3f82c20e3e5f3 -> a4449a5754a8bec3d97bf7980fb9e76605e794425fdbbdd6265b380ecdee06a6
2025-09-23 09:49:40,192 [1130315]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 75d01cd54c2bbe70352cffb588c9d6d5ff3dbd9d0f5bbe578a5fb31452b77e48 -> 5432e2b39bd8b5efc5a0a3fdebb3a03c6f4318022d77e5c36935c737815442f0
2025-09-23 09:49:40,734 [1130857]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 75d01cd54c2bbe70352cffb588c9d6d5ff3dbd9d0f5bbe578a5fb31452b77e48 -> 5432e2b39bd8b5efc5a0a3fdebb3a03c6f4318022d77e5c36935c737815442f0
2025-09-23 09:49:41,266 [1131389]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 75d01cd54c2bbe70352cffb588c9d6d5ff3dbd9d0f5bbe578a5fb31452b77e48 -> 5432e2b39bd8b5efc5a0a3fdebb3a03c6f4318022d77e5c36935c737815442f0
2025-09-23 09:49:42,047 [1132170]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 75d01cd54c2bbe70352cffb588c9d6d5ff3dbd9d0f5bbe578a5fb31452b77e48 -> 5432e2b39bd8b5efc5a0a3fdebb3a03c6f4318022d77e5c36935c737815442f0
2025-09-23 09:49:42,561 [1132684]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 88e947bf6d10beb62028555ed4c0bce9f2c55a1d77654daf206625d994724b0c -> 5432e2b39bd8b5efc5a0a3fdebb3a03c6f4318022d77e5c36935c737815442f0
2025-09-23 09:49:42,564 [1132687]   WARN - #com.augmentcode.intellij.webviews.AbstractWebviewMessagingService - Failed to parse webview message as protobuf.
Exception: Missing type url when parsing: {"type":"can-show-terminal","data":{"command":"npm start"}}
Message: {"type":"async-wrapper","requestId":"0885e11f-c9a6-4866-b12e-381d3314c5ba","error":null,"baseMsg":{"type":"can-show-terminal","data":{"command":"npm start"}},"destination":"host"}
2025-09-23 09:49:42,646 [1132769]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_ERROR]: Failed to check if terminal can be shown: Error: Unable to parse webview message: {"type":"async-wrapper","requestId":"0885e11f-c9a6-4866-b12e-381d3314c5ba","error":null,"baseMsg":{"type":"can-show-terminal","data":{"command":"npm start"}},"destination":"host"} [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:49:42,739 [1132862]   WARN - #com.augmentcode.intellij.chat.ChatWebviewMessageClient$Companion - Workspace guidelines are empty, not including in chat request. 
2025-09-23 09:49:43,077 [1133200]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: d4e2ebd2d3396372dfb825784b1f28971cb173c084b31604dba3f82c20e3e5f3 -> 5432e2b39bd8b5efc5a0a3fdebb3a03c6f4318022d77e5c36935c737815442f0
2025-09-23 09:49:43,135 [1133258]   WARN - #c.i.e.wsl - Failed to get WSL mount root for Ubuntu: DOS working directory is expected, but got \\wsl.localhost\Ubuntu\home\oni\audio-transcriber
2025-09-23 09:49:43,926 [1134049]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: d4e2ebd2d3396372dfb825784b1f28971cb173c084b31604dba3f82c20e3e5f3 -> 60a068cb9ad7b1d8322020a2191d0d57d87244a463ef9eb94d6d8c476912940b
2025-09-23 09:49:45,352 [1135475]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: d4e2ebd2d3396372dfb825784b1f28971cb173c084b31604dba3f82c20e3e5f3 -> 60a068cb9ad7b1d8322020a2191d0d57d87244a463ef9eb94d6d8c476912940b
2025-09-23 09:49:45,911 [1136034]   WARN - #c.i.e.wsl - Failed to get WSL mount root for Ubuntu: DOS working directory is expected, but got \\wsl.localhost\Ubuntu\home\oni\audio-transcriber
2025-09-23 09:49:45,942 [1136065]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: d4e2ebd2d3396372dfb825784b1f28971cb173c084b31604dba3f82c20e3e5f3 -> 60a068cb9ad7b1d8322020a2191d0d57d87244a463ef9eb94d6d8c476912940b
2025-09-23 09:49:46,472 [1136595]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 5fd1374d5295939ee8e530a0d7f0f1ada66c9e255f4e11f0335a0781b02ffa4f -> 60a068cb9ad7b1d8322020a2191d0d57d87244a463ef9eb94d6d8c476912940b
2025-09-23 09:49:46,515 [1136638]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: No exchange with this request ID found. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:49:47,044 [1137167]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 75d01cd54c2bbe70352cffb588c9d6d5ff3dbd9d0f5bbe578a5fb31452b77e48 -> 60a068cb9ad7b1d8322020a2191d0d57d87244a463ef9eb94d6d8c476912940b
2025-09-23 09:49:47,323 [1137446]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: IntersectionDisplay context not found.  Call setIntersectionContext first. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:49:47,421 [1137544]   WARN - #com.augmentcode.intellij.chat.ChatWebviewMessageClient$Companion - Workspace guidelines are empty, not including in chat request. 
2025-09-23 09:49:47,601 [1137724]   WARN - #com.augmentcode.intellij.webviews.AbstractWebviewMessagingService - Failed to parse webview message as protobuf.
Exception: Missing type url when parsing: {"type":"can-show-terminal","data":{"terminalId":24}}
Message: {"type":"async-wrapper","requestId":"81cd5ab3-3fe2-4651-8297-41d93c3443a0","error":null,"baseMsg":{"type":"can-show-terminal","data":{"terminalId":24}},"destination":"host"}
2025-09-23 09:49:47,603 [1137726]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_ERROR]: Failed to check if terminal can be shown: Error: Unable to parse webview message: {"type":"async-wrapper","requestId":"81cd5ab3-3fe2-4651-8297-41d93c3443a0","error":null,"baseMsg":{"type":"can-show-terminal","data":{"terminalId":24}},"destination":"host"} [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:49:47,947 [1138070]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 88e947bf6d10beb62028555ed4c0bce9f2c55a1d77654daf206625d994724b0c -> 60a068cb9ad7b1d8322020a2191d0d57d87244a463ef9eb94d6d8c476912940b
2025-09-23 09:49:48,707 [1138830]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: d4e2ebd2d3396372dfb825784b1f28971cb173c084b31604dba3f82c20e3e5f3 -> 60a068cb9ad7b1d8322020a2191d0d57d87244a463ef9eb94d6d8c476912940b
2025-09-23 09:49:49,358 [1139481]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: d4e2ebd2d3396372dfb825784b1f28971cb173c084b31604dba3f82c20e3e5f3 -> 60a068cb9ad7b1d8322020a2191d0d57d87244a463ef9eb94d6d8c476912940b
2025-09-23 09:49:50,293 [1140416]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 75d01cd54c2bbe70352cffb588c9d6d5ff3dbd9d0f5bbe578a5fb31452b77e48 -> 60a068cb9ad7b1d8322020a2191d0d57d87244a463ef9eb94d6d8c476912940b
2025-09-23 09:49:50,689 [1140812]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: No exchange with this request ID found. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:49:51,005 [1141128]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: d4e2ebd2d3396372dfb825784b1f28971cb173c084b31604dba3f82c20e3e5f3 -> 60a068cb9ad7b1d8322020a2191d0d57d87244a463ef9eb94d6d8c476912940b
2025-09-23 09:49:51,836 [1141959]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 5fd1374d5295939ee8e530a0d7f0f1ada66c9e255f4e11f0335a0781b02ffa4f -> 60a068cb9ad7b1d8322020a2191d0d57d87244a463ef9eb94d6d8c476912940b
2025-09-23 09:49:52,503 [1142626]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: IntersectionDisplay context not found.  Call setIntersectionContext first. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:49:52,798 [1142921]   WARN - #com.augmentcode.intellij.webviews.AbstractWebviewMessagingService - Failed to parse webview message as protobuf.
Exception: Missing type url when parsing: {"type":"can-show-terminal","data":{"terminalId":24}}
Message: {"type":"async-wrapper","requestId":"d8360b6b-1809-4511-a7b7-63414be978b8","error":null,"baseMsg":{"type":"can-show-terminal","data":{"terminalId":24}},"destination":"host"}
2025-09-23 09:49:52,799 [1142922]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_ERROR]: Failed to check if terminal can be shown: Error: Unable to parse webview message: {"type":"async-wrapper","requestId":"d8360b6b-1809-4511-a7b7-63414be978b8","error":null,"baseMsg":{"type":"can-show-terminal","data":{"terminalId":24}},"destination":"host"} [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:49:52,941 [1143064]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: IntersectionDisplay context not found.  Call setIntersectionContext first. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:49:53,236 [1143359]   WARN - #com.augmentcode.intellij.webviews.AbstractWebviewMessagingService - Failed to parse webview message as protobuf.
Exception: Missing type url when parsing: {"type":"can-show-terminal","data":{"command":"npm list tailwindcss"}}
Message: {"type":"async-wrapper","requestId":"2cebc51d-576b-463a-aea3-effb915880f2","error":null,"baseMsg":{"type":"can-show-terminal","data":{"command":"npm list tailwindcss"}},"destination":"host"}
2025-09-23 09:49:53,238 [1143361]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_ERROR]: Failed to check if terminal can be shown: Error: Unable to parse webview message: {"type":"async-wrapper","requestId":"2cebc51d-576b-463a-aea3-effb915880f2","error":null,"baseMsg":{"type":"can-show-terminal","data":{"command":"npm list tailwindcss"}},"destination":"host"} [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:49:53,364 [1143487]   WARN - #c.i.e.wsl - Failed to get WSL mount root for Ubuntu: DOS working directory is expected, but got \\wsl.localhost\Ubuntu\home\oni\audio-transcriber
2025-09-23 09:49:54,863 [1144986]   WARN - #com.augmentcode.intellij.chat.ChatWebviewMessageClient$Companion - Workspace guidelines are empty, not including in chat request. 
2025-09-23 09:49:55,449 [1145572]   WARN - #c.i.e.wsl - Failed to get WSL mount root for Ubuntu: DOS working directory is expected, but got \\wsl.localhost\Ubuntu\home\oni\audio-transcriber
2025-09-23 09:49:55,844 [1145967]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (df8c97a94e9b07d9592c84487815f664a89f9d3d3dff50b684192c233c4e1c88) is unknown but expected to be uploaded
2025-09-23 09:49:55,844 [1145967]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (df8c97a94e9b07d9592c84487815f664a89f9d3d3dff50b684192c233c4e1c88) is unknown but expected to be uploaded
2025-09-23 09:49:55,844 [1145967]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (df8c97a94e9b07d9592c84487815f664a89f9d3d3dff50b684192c233c4e1c88) is unknown but expected to be uploaded
2025-09-23 09:49:55,844 [1145967]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (df8c97a94e9b07d9592c84487815f664a89f9d3d3dff50b684192c233c4e1c88) is unknown but expected to be uploaded
2025-09-23 09:49:55,844 [1145967]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (df8c97a94e9b07d9592c84487815f664a89f9d3d3dff50b684192c233c4e1c88) is unknown but expected to be uploaded
2025-09-23 09:49:55,844 [1145967]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (df8c97a94e9b07d9592c84487815f664a89f9d3d3dff50b684192c233c4e1c88) is unknown but expected to be uploaded
2025-09-23 09:49:55,844 [1145967]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (df8c97a94e9b07d9592c84487815f664a89f9d3d3dff50b684192c233c4e1c88) is unknown but expected to be uploaded
2025-09-23 09:49:55,844 [1145967]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (df8c97a94e9b07d9592c84487815f664a89f9d3d3dff50b684192c233c4e1c88) is unknown but expected to be uploaded
2025-09-23 09:49:55,844 [1145967]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (df8c97a94e9b07d9592c84487815f664a89f9d3d3dff50b684192c233c4e1c88) is unknown but expected to be uploaded
2025-09-23 09:49:55,844 [1145967]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (df8c97a94e9b07d9592c84487815f664a89f9d3d3dff50b684192c233c4e1c88) is unknown but expected to be uploaded
2025-09-23 09:49:55,844 [1145967]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (5432e2b39bd8b5efc5a0a3fdebb3a03c6f4318022d77e5c36935c737815442f0) is unknown but expected to be uploaded
2025-09-23 09:49:55,844 [1145967]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (5432e2b39bd8b5efc5a0a3fdebb3a03c6f4318022d77e5c36935c737815442f0) is unknown but expected to be uploaded
2025-09-23 09:49:55,844 [1145967]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (5432e2b39bd8b5efc5a0a3fdebb3a03c6f4318022d77e5c36935c737815442f0) is unknown but expected to be uploaded
2025-09-23 09:49:55,844 [1145967]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (5432e2b39bd8b5efc5a0a3fdebb3a03c6f4318022d77e5c36935c737815442f0) is unknown but expected to be uploaded
2025-09-23 09:49:55,844 [1145967]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (5432e2b39bd8b5efc5a0a3fdebb3a03c6f4318022d77e5c36935c737815442f0) is unknown but expected to be uploaded
2025-09-23 09:49:55,844 [1145967]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (5432e2b39bd8b5efc5a0a3fdebb3a03c6f4318022d77e5c36935c737815442f0) is unknown but expected to be uploaded
2025-09-23 09:49:55,844 [1145967]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (5432e2b39bd8b5efc5a0a3fdebb3a03c6f4318022d77e5c36935c737815442f0) is unknown but expected to be uploaded
2025-09-23 09:49:56,218 [1146341]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: a7ac6e33cb21386b30c1a7cf99dcecec0dfeb944a82fa912b8af95018d93e0cb -> 2999974b4b159530be88cc593eeec34c61e0a5f55a4aa4771a8cd983eb676bfc
2025-09-23 09:49:57,348 [1147471]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: b66d096176ed1926a064daa9176676521235feab43a80a734f948314f3823a57 -> 2999974b4b159530be88cc593eeec34c61e0a5f55a4aa4771a8cd983eb676bfc
2025-09-23 09:49:57,896 [1148019]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: df8c97a94e9b07d9592c84487815f664a89f9d3d3dff50b684192c233c4e1c88 -> 2999974b4b159530be88cc593eeec34c61e0a5f55a4aa4771a8cd983eb676bfc
2025-09-23 09:49:58,571 [1148694]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: No exchange with this request ID found. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:49:58,655 [1148778]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: df8c97a94e9b07d9592c84487815f664a89f9d3d3dff50b684192c233c4e1c88 -> 2999974b4b159530be88cc593eeec34c61e0a5f55a4aa4771a8cd983eb676bfc
2025-09-23 09:49:59,222 [1149345]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: df8c97a94e9b07d9592c84487815f664a89f9d3d3dff50b684192c233c4e1c88 -> 2999974b4b159530be88cc593eeec34c61e0a5f55a4aa4771a8cd983eb676bfc
2025-09-23 09:49:59,735 [1149858]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: df8c97a94e9b07d9592c84487815f664a89f9d3d3dff50b684192c233c4e1c88 -> 2999974b4b159530be88cc593eeec34c61e0a5f55a4aa4771a8cd983eb676bfc
2025-09-23 09:50:00,494 [1150617]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: df8c97a94e9b07d9592c84487815f664a89f9d3d3dff50b684192c233c4e1c88 -> 2999974b4b159530be88cc593eeec34c61e0a5f55a4aa4771a8cd983eb676bfc
2025-09-23 09:50:00,711 [1150834]   WARN - #com.augmentcode.intellij.sentry.SentryMetadataCollector - Git ls-files command failed with exit code: 128
2025-09-23 09:50:01,197 [1151320]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 5432e2b39bd8b5efc5a0a3fdebb3a03c6f4318022d77e5c36935c737815442f0 -> b838d7dccd4e311b8fe51a5fe1364c22410b9f955a2185b97e33969b9334d444
2025-09-23 09:50:01,799 [1151922]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 5432e2b39bd8b5efc5a0a3fdebb3a03c6f4318022d77e5c36935c737815442f0 -> b838d7dccd4e311b8fe51a5fe1364c22410b9f955a2185b97e33969b9334d444
2025-09-23 09:50:01,846 [1151969]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: IntersectionDisplay context not found.  Call setIntersectionContext first. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:50:02,128 [1152251]   WARN - #com.augmentcode.intellij.webviews.AbstractWebviewMessagingService - Failed to parse webview message as protobuf.
Exception: Missing type url when parsing: {"type":"can-show-terminal","data":{"command":"npm uninstall tailwindcss @tailwindcss/postcss"}}
Message: {"type":"async-wrapper","requestId":"26da6fdc-3088-48cf-94d3-40c249163654","error":null,"baseMsg":{"type":"can-show-terminal","data":{"command":"npm uninstall tailwindcss @tailwindcss/postcss"}},"destination":"host"}
2025-09-23 09:50:02,135 [1152258]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_ERROR]: Failed to check if terminal can be shown: Error: Unable to parse webview message: {"type":"async-wrapper","requestId":"26da6fdc-3088-48cf-94d3-40c249163654","error":null,"baseMsg":{"type":"can-show-terminal","data":{"command":"npm uninstall tailwindcss @tailwindcss/postcss"}},"destination":"host"} [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:50:02,535 [1152658]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 5432e2b39bd8b5efc5a0a3fdebb3a03c6f4318022d77e5c36935c737815442f0 -> b838d7dccd4e311b8fe51a5fe1364c22410b9f955a2185b97e33969b9334d444
2025-09-23 09:50:03,287 [1153410]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 2999974b4b159530be88cc593eeec34c61e0a5f55a4aa4771a8cd983eb676bfc -> b838d7dccd4e311b8fe51a5fe1364c22410b9f955a2185b97e33969b9334d444
2025-09-23 09:50:03,965 [1154088]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: df8c97a94e9b07d9592c84487815f664a89f9d3d3dff50b684192c233c4e1c88 -> b838d7dccd4e311b8fe51a5fe1364c22410b9f955a2185b97e33969b9334d444
2025-09-23 09:50:04,915 [1155038]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: df8c97a94e9b07d9592c84487815f664a89f9d3d3dff50b684192c233c4e1c88 -> b838d7dccd4e311b8fe51a5fe1364c22410b9f955a2185b97e33969b9334d444
2025-09-23 09:50:05,419 [1155542]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 5432e2b39bd8b5efc5a0a3fdebb3a03c6f4318022d77e5c36935c737815442f0 -> b838d7dccd4e311b8fe51a5fe1364c22410b9f955a2185b97e33969b9334d444
2025-09-23 09:50:06,111 [1156234]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 5432e2b39bd8b5efc5a0a3fdebb3a03c6f4318022d77e5c36935c737815442f0 -> b838d7dccd4e311b8fe51a5fe1364c22410b9f955a2185b97e33969b9334d444
2025-09-23 09:50:06,976 [1157099]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: df8c97a94e9b07d9592c84487815f664a89f9d3d3dff50b684192c233c4e1c88 -> b838d7dccd4e311b8fe51a5fe1364c22410b9f955a2185b97e33969b9334d444
2025-09-23 09:50:07,806 [1157929]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: df8c97a94e9b07d9592c84487815f664a89f9d3d3dff50b684192c233c4e1c88 -> b838d7dccd4e311b8fe51a5fe1364c22410b9f955a2185b97e33969b9334d444
2025-09-23 09:50:08,360 [1158483]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 5432e2b39bd8b5efc5a0a3fdebb3a03c6f4318022d77e5c36935c737815442f0 -> b838d7dccd4e311b8fe51a5fe1364c22410b9f955a2185b97e33969b9334d444
2025-09-23 09:50:09,138 [1159261]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 5432e2b39bd8b5efc5a0a3fdebb3a03c6f4318022d77e5c36935c737815442f0 -> b838d7dccd4e311b8fe51a5fe1364c22410b9f955a2185b97e33969b9334d444
2025-09-23 09:50:12,660 [1162783]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (60a068cb9ad7b1d8322020a2191d0d57d87244a463ef9eb94d6d8c476912940b) is unknown but expected to be uploaded
2025-09-23 09:50:12,660 [1162783]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (60a068cb9ad7b1d8322020a2191d0d57d87244a463ef9eb94d6d8c476912940b) is unknown but expected to be uploaded
2025-09-23 09:50:12,660 [1162783]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (60a068cb9ad7b1d8322020a2191d0d57d87244a463ef9eb94d6d8c476912940b) is unknown but expected to be uploaded
2025-09-23 09:50:12,660 [1162783]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (60a068cb9ad7b1d8322020a2191d0d57d87244a463ef9eb94d6d8c476912940b) is unknown but expected to be uploaded
2025-09-23 09:50:12,660 [1162783]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (60a068cb9ad7b1d8322020a2191d0d57d87244a463ef9eb94d6d8c476912940b) is unknown but expected to be uploaded
2025-09-23 09:50:12,660 [1162783]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (60a068cb9ad7b1d8322020a2191d0d57d87244a463ef9eb94d6d8c476912940b) is unknown but expected to be uploaded
2025-09-23 09:50:12,660 [1162783]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (60a068cb9ad7b1d8322020a2191d0d57d87244a463ef9eb94d6d8c476912940b) is unknown but expected to be uploaded
2025-09-23 09:50:12,660 [1162783]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (60a068cb9ad7b1d8322020a2191d0d57d87244a463ef9eb94d6d8c476912940b) is unknown but expected to be uploaded
2025-09-23 09:50:12,660 [1162783]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (60a068cb9ad7b1d8322020a2191d0d57d87244a463ef9eb94d6d8c476912940b) is unknown but expected to be uploaded
2025-09-23 09:50:12,660 [1162783]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (60a068cb9ad7b1d8322020a2191d0d57d87244a463ef9eb94d6d8c476912940b) is unknown but expected to be uploaded
2025-09-23 09:50:12,660 [1162783]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (60a068cb9ad7b1d8322020a2191d0d57d87244a463ef9eb94d6d8c476912940b) is unknown but expected to be uploaded
2025-09-23 09:50:12,660 [1162783]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (60a068cb9ad7b1d8322020a2191d0d57d87244a463ef9eb94d6d8c476912940b) is unknown but expected to be uploaded
2025-09-23 09:50:12,660 [1162783]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (2999974b4b159530be88cc593eeec34c61e0a5f55a4aa4771a8cd983eb676bfc) is unknown but expected to be uploaded
2025-09-23 09:50:12,660 [1162783]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (2999974b4b159530be88cc593eeec34c61e0a5f55a4aa4771a8cd983eb676bfc) is unknown but expected to be uploaded
2025-09-23 09:50:12,660 [1162783]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (2999974b4b159530be88cc593eeec34c61e0a5f55a4aa4771a8cd983eb676bfc) is unknown but expected to be uploaded
2025-09-23 09:50:12,660 [1162783]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (2999974b4b159530be88cc593eeec34c61e0a5f55a4aa4771a8cd983eb676bfc) is unknown but expected to be uploaded
2025-09-23 09:50:12,660 [1162783]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (2999974b4b159530be88cc593eeec34c61e0a5f55a4aa4771a8cd983eb676bfc) is unknown but expected to be uploaded
2025-09-23 09:50:12,660 [1162783]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (2999974b4b159530be88cc593eeec34c61e0a5f55a4aa4771a8cd983eb676bfc) is unknown but expected to be uploaded
2025-09-23 09:50:12,660 [1162783]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (2999974b4b159530be88cc593eeec34c61e0a5f55a4aa4771a8cd983eb676bfc) is unknown but expected to be uploaded
2025-09-23 09:50:13,087 [1163210]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: df8c97a94e9b07d9592c84487815f664a89f9d3d3dff50b684192c233c4e1c88 -> b838d7dccd4e311b8fe51a5fe1364c22410b9f955a2185b97e33969b9334d444
2025-09-23 09:50:13,807 [1163930]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 60a068cb9ad7b1d8322020a2191d0d57d87244a463ef9eb94d6d8c476912940b -> b838d7dccd4e311b8fe51a5fe1364c22410b9f955a2185b97e33969b9334d444
2025-09-23 09:50:14,477 [1164600]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 60a068cb9ad7b1d8322020a2191d0d57d87244a463ef9eb94d6d8c476912940b -> b838d7dccd4e311b8fe51a5fe1364c22410b9f955a2185b97e33969b9334d444
2025-09-23 09:50:15,071 [1165194]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 60a068cb9ad7b1d8322020a2191d0d57d87244a463ef9eb94d6d8c476912940b -> b838d7dccd4e311b8fe51a5fe1364c22410b9f955a2185b97e33969b9334d444
2025-09-23 09:50:15,628 [1165751]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 60a068cb9ad7b1d8322020a2191d0d57d87244a463ef9eb94d6d8c476912940b -> b838d7dccd4e311b8fe51a5fe1364c22410b9f955a2185b97e33969b9334d444
2025-09-23 09:50:16,497 [1166620]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 60a068cb9ad7b1d8322020a2191d0d57d87244a463ef9eb94d6d8c476912940b -> 06bdfa42292c6761f12519c30ad7295691be3853015bd6c34d1212936fb5c59a
2025-09-23 09:50:16,983 [1167106]   WARN - #com.augmentcode.intellij.webviews.AbstractWebviewMessagingService - Failed to parse webview message as protobuf.
Exception: Missing type url when parsing: {"type":"can-show-terminal","data":{"command":"npm uninstall tailwindcss @tailwindcss/postcss"}}
Message: {"type":"async-wrapper","requestId":"79bef228-f892-4bb3-9594-cfaf4bbc7961","error":null,"baseMsg":{"type":"can-show-terminal","data":{"command":"npm uninstall tailwindcss @tailwindcss/postcss"}},"destination":"host"}
2025-09-23 09:50:17,038 [1167161]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_ERROR]: Failed to check if terminal can be shown: Error: Unable to parse webview message: {"type":"async-wrapper","requestId":"79bef228-f892-4bb3-9594-cfaf4bbc7961","error":null,"baseMsg":{"type":"can-show-terminal","data":{"command":"npm uninstall tailwindcss @tailwindcss/postcss"}},"destination":"host"} [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:50:17,473 [1167596]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 60a068cb9ad7b1d8322020a2191d0d57d87244a463ef9eb94d6d8c476912940b -> 06bdfa42292c6761f12519c30ad7295691be3853015bd6c34d1212936fb5c59a
2025-09-23 09:50:17,539 [1167662]   WARN - #c.i.e.wsl - Failed to get WSL mount root for Ubuntu: DOS working directory is expected, but got \\wsl.localhost\Ubuntu\home\oni\audio-transcriber
2025-09-23 09:50:18,510 [1168633]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 2999974b4b159530be88cc593eeec34c61e0a5f55a4aa4771a8cd983eb676bfc -> b00ad1111f25c918407cccf97835e31d17b0252fc2451a909fe936722794d7df
2025-09-23 09:50:19,282 [1169405]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 2999974b4b159530be88cc593eeec34c61e0a5f55a4aa4771a8cd983eb676bfc -> b00ad1111f25c918407cccf97835e31d17b0252fc2451a909fe936722794d7df
2025-09-23 09:50:19,887 [1170010]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 2999974b4b159530be88cc593eeec34c61e0a5f55a4aa4771a8cd983eb676bfc -> b00ad1111f25c918407cccf97835e31d17b0252fc2451a909fe936722794d7df
2025-09-23 09:50:20,344 [1170467]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_ERROR]: _validateAndCheckTool called while another tool is active [object Object] [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:50:20,394 [1170517]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 2999974b4b159530be88cc593eeec34c61e0a5f55a4aa4771a8cd983eb676bfc -> b00ad1111f25c918407cccf97835e31d17b0252fc2451a909fe936722794d7df
2025-09-23 09:50:20,413 [1170536]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: IntersectionDisplay context not found.  Call setIntersectionContext first. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:50:20,729 [1170852]   WARN - #com.augmentcode.intellij.webviews.AbstractWebviewMessagingService - Failed to parse webview message as protobuf.
Exception: Missing type url when parsing: {"type":"can-show-terminal","data":{"command":"npm install -D tailwindcss@^3.4.0"}}
Message: {"type":"async-wrapper","requestId":"a96933c8-5e63-4c29-8dc0-cdb6122029db","error":null,"baseMsg":{"type":"can-show-terminal","data":{"command":"npm install -D tailwindcss@^3.4.0"}},"destination":"host"}
2025-09-23 09:50:20,730 [1170853]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_ERROR]: Failed to check if terminal can be shown: Error: Unable to parse webview message: {"type":"async-wrapper","requestId":"a96933c8-5e63-4c29-8dc0-cdb6122029db","error":null,"baseMsg":{"type":"can-show-terminal","data":{"command":"npm install -D tailwindcss@^3.4.0"}},"destination":"host"} [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:50:20,878 [1171001]   WARN - #c.i.e.wsl - Failed to get WSL mount root for Ubuntu: DOS working directory is expected, but got \\wsl.localhost\Ubuntu\home\oni\audio-transcriber
2025-09-23 09:50:20,958 [1171081]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 60a068cb9ad7b1d8322020a2191d0d57d87244a463ef9eb94d6d8c476912940b -> b00ad1111f25c918407cccf97835e31d17b0252fc2451a909fe936722794d7df
2025-09-23 09:50:21,514 [1171637]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 60a068cb9ad7b1d8322020a2191d0d57d87244a463ef9eb94d6d8c476912940b -> b00ad1111f25c918407cccf97835e31d17b0252fc2451a909fe936722794d7df
2025-09-23 09:50:22,163 [1172286]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 60a068cb9ad7b1d8322020a2191d0d57d87244a463ef9eb94d6d8c476912940b -> 58f9087479a959df7201d43460e279790e624287d96621f2f49853b46a8ce534
2025-09-23 09:50:23,021 [1173144]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: b00ad1111f25c918407cccf97835e31d17b0252fc2451a909fe936722794d7df -> 58f9087479a959df7201d43460e279790e624287d96621f2f49853b46a8ce534
2025-09-23 09:50:23,593 [1173716]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 2999974b4b159530be88cc593eeec34c61e0a5f55a4aa4771a8cd983eb676bfc -> 58f9087479a959df7201d43460e279790e624287d96621f2f49853b46a8ce534
2025-09-23 09:50:24,119 [1174242]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 60a068cb9ad7b1d8322020a2191d0d57d87244a463ef9eb94d6d8c476912940b -> 58f9087479a959df7201d43460e279790e624287d96621f2f49853b46a8ce534
2025-09-23 09:50:24,721 [1174844]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 2999974b4b159530be88cc593eeec34c61e0a5f55a4aa4771a8cd983eb676bfc -> 58f9087479a959df7201d43460e279790e624287d96621f2f49853b46a8ce534
2025-09-23 09:50:26,423 [1176546]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 60a068cb9ad7b1d8322020a2191d0d57d87244a463ef9eb94d6d8c476912940b -> 58f9087479a959df7201d43460e279790e624287d96621f2f49853b46a8ce534
2025-09-23 09:50:27,982 [1178105]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 60a068cb9ad7b1d8322020a2191d0d57d87244a463ef9eb94d6d8c476912940b -> 58f9087479a959df7201d43460e279790e624287d96621f2f49853b46a8ce534
2025-09-23 09:50:30,292 [1180415]   WARN - #com.augmentcode.intellij.webviews.AbstractWebviewMessagingService - Failed to parse webview message as protobuf.
Exception: Missing type url when parsing: {"type":"can-show-terminal","data":{"command":"npm install -D tailwindcss@^3.4.0"}}
Message: {"type":"async-wrapper","requestId":"588bb260-f21d-43dd-8e32-56abaf1b4867","error":null,"baseMsg":{"type":"can-show-terminal","data":{"command":"npm install -D tailwindcss@^3.4.0"}},"destination":"host"}
2025-09-23 09:50:30,377 [1180500]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_ERROR]: Failed to check if terminal can be shown: Error: Unable to parse webview message: {"type":"async-wrapper","requestId":"588bb260-f21d-43dd-8e32-56abaf1b4867","error":null,"baseMsg":{"type":"can-show-terminal","data":{"command":"npm install -D tailwindcss@^3.4.0"}},"destination":"host"} [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:50:30,843 [1180966]   WARN - #c.i.e.wsl - Failed to get WSL mount root for Ubuntu: DOS working directory is expected, but got \\wsl.localhost\Ubuntu\home\oni\audio-transcriber
2025-09-23 09:50:31,409 [1181532]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (b00ad1111f25c918407cccf97835e31d17b0252fc2451a909fe936722794d7df) is unknown but expected to be uploaded
2025-09-23 09:50:31,409 [1181532]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (b00ad1111f25c918407cccf97835e31d17b0252fc2451a909fe936722794d7df) is unknown but expected to be uploaded
2025-09-23 09:50:31,409 [1181532]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (b00ad1111f25c918407cccf97835e31d17b0252fc2451a909fe936722794d7df) is unknown but expected to be uploaded
2025-09-23 09:50:31,409 [1181532]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (b00ad1111f25c918407cccf97835e31d17b0252fc2451a909fe936722794d7df) is unknown but expected to be uploaded
2025-09-23 09:50:31,409 [1181532]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (b00ad1111f25c918407cccf97835e31d17b0252fc2451a909fe936722794d7df) is unknown but expected to be uploaded
2025-09-23 09:50:31,409 [1181532]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (b00ad1111f25c918407cccf97835e31d17b0252fc2451a909fe936722794d7df) is unknown but expected to be uploaded
2025-09-23 09:50:31,409 [1181532]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (58f9087479a959df7201d43460e279790e624287d96621f2f49853b46a8ce534) is unknown but expected to be uploaded
2025-09-23 09:50:31,409 [1181532]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (58f9087479a959df7201d43460e279790e624287d96621f2f49853b46a8ce534) is unknown but expected to be uploaded
2025-09-23 09:50:31,411 [1181534]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (58f9087479a959df7201d43460e279790e624287d96621f2f49853b46a8ce534) is unknown but expected to be uploaded
2025-09-23 09:50:31,411 [1181534]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (58f9087479a959df7201d43460e279790e624287d96621f2f49853b46a8ce534) is unknown but expected to be uploaded
2025-09-23 09:50:31,411 [1181534]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (58f9087479a959df7201d43460e279790e624287d96621f2f49853b46a8ce534) is unknown but expected to be uploaded
2025-09-23 09:50:31,411 [1181534]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (58f9087479a959df7201d43460e279790e624287d96621f2f49853b46a8ce534) is unknown but expected to be uploaded
2025-09-23 09:50:31,411 [1181534]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (58f9087479a959df7201d43460e279790e624287d96621f2f49853b46a8ce534) is unknown but expected to be uploaded
2025-09-23 09:50:31,411 [1181534]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (58f9087479a959df7201d43460e279790e624287d96621f2f49853b46a8ce534) is unknown but expected to be uploaded
2025-09-23 09:50:31,852 [1181975]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 2999974b4b159530be88cc593eeec34c61e0a5f55a4aa4771a8cd983eb676bfc -> edda9769495a979d547cad00788e75fc60e013c050eca4b101760cbed2e40bb3
2025-09-23 09:50:33,391 [1183514]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: b00ad1111f25c918407cccf97835e31d17b0252fc2451a909fe936722794d7df -> edda9769495a979d547cad00788e75fc60e013c050eca4b101760cbed2e40bb3
2025-09-23 09:50:33,967 [1184090]   WARN - #com.augmentcode.intellij.chat.ChatWebviewMessageClient$Companion - Workspace guidelines are empty, not including in chat request. 
2025-09-23 09:50:34,217 [1184340]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: b00ad1111f25c918407cccf97835e31d17b0252fc2451a909fe936722794d7df -> edda9769495a979d547cad00788e75fc60e013c050eca4b101760cbed2e40bb3
2025-09-23 09:50:35,181 [1185304]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: b00ad1111f25c918407cccf97835e31d17b0252fc2451a909fe936722794d7df -> edda9769495a979d547cad00788e75fc60e013c050eca4b101760cbed2e40bb3
2025-09-23 09:50:35,183 [1185306]   WARN - #c.i.e.wsl - Failed to get WSL mount root for Ubuntu: DOS working directory is expected, but got \\wsl.localhost\Ubuntu\home\oni\audio-transcriber
2025-09-23 09:50:36,115 [1186238]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 58f9087479a959df7201d43460e279790e624287d96621f2f49853b46a8ce534 -> 2cb7dc392c3bfae388e1ba2aede7524f9e3a87055fb6322588111b617636285c
2025-09-23 09:50:37,061 [1187184]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 58f9087479a959df7201d43460e279790e624287d96621f2f49853b46a8ce534 -> 2cb7dc392c3bfae388e1ba2aede7524f9e3a87055fb6322588111b617636285c
2025-09-23 09:50:37,197 [1187320]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: No exchange with this request ID found. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:50:37,641 [1187764]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 58f9087479a959df7201d43460e279790e624287d96621f2f49853b46a8ce534 -> 2cb7dc392c3bfae388e1ba2aede7524f9e3a87055fb6322588111b617636285c
2025-09-23 09:50:38,212 [1188335]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 58f9087479a959df7201d43460e279790e624287d96621f2f49853b46a8ce534 -> 2cb7dc392c3bfae388e1ba2aede7524f9e3a87055fb6322588111b617636285c
2025-09-23 09:50:38,998 [1189121]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: b00ad1111f25c918407cccf97835e31d17b0252fc2451a909fe936722794d7df -> 2cb7dc392c3bfae388e1ba2aede7524f9e3a87055fb6322588111b617636285c
2025-09-23 09:50:39,589 [1189712]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 58f9087479a959df7201d43460e279790e624287d96621f2f49853b46a8ce534 -> 2cb7dc392c3bfae388e1ba2aede7524f9e3a87055fb6322588111b617636285c
2025-09-23 09:50:39,842 [1189965]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: IntersectionDisplay context not found.  Call setIntersectionContext first. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:50:40,699 [1190822]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 58f9087479a959df7201d43460e279790e624287d96621f2f49853b46a8ce534 -> 2cb7dc392c3bfae388e1ba2aede7524f9e3a87055fb6322588111b617636285c
2025-09-23 09:50:40,922 [1191045]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: Loading changes  for a204f588-71ba-4a1e-9b47-eddd973ce616 retry 0:  Error: No changes found [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:50:40,924 [1191047]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: Loading changes  for a204f588-71ba-4a1e-9b47-eddd973ce616 retry 1:  Error: No changes found [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:50:40,945 [1191068]   WARN - #com.augmentcode.intellij.chat.ChatWebviewMessageClient$Companion - Workspace guidelines are empty, not including in chat request. 
2025-09-23 09:50:41,426 [1191549]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: Loading changes  for a204f588-71ba-4a1e-9b47-eddd973ce616 retry 2:  Error: No changes found [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:50:41,427 [1191550]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: Loading changes  for a204f588-71ba-4a1e-9b47-eddd973ce616 retry 3:  Error: No changes found [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:50:41,427 [1191550]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: Loading changes failed for a204f588-71ba-4a1e-9b47-eddd973ce616:  Error: No changes found [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:50:41,708 [1191831]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: b00ad1111f25c918407cccf97835e31d17b0252fc2451a909fe936722794d7df -> 2cb7dc392c3bfae388e1ba2aede7524f9e3a87055fb6322588111b617636285c
2025-09-23 09:50:41,932 [1192055]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: Loading changes  for a204f588-71ba-4a1e-9b47-eddd973ce616 retry 4:  Error: No changes found [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:50:41,932 [1192055]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: Loading changes failed for a204f588-71ba-4a1e-9b47-eddd973ce616:  Error: No changes found [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:50:42,513 [1192636]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 58f9087479a959df7201d43460e279790e624287d96621f2f49853b46a8ce534 -> 2cb7dc392c3bfae388e1ba2aede7524f9e3a87055fb6322588111b617636285c
2025-09-23 09:50:43,214 [1193337]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 58f9087479a959df7201d43460e279790e624287d96621f2f49853b46a8ce534 -> 2cb7dc392c3bfae388e1ba2aede7524f9e3a87055fb6322588111b617636285c
2025-09-23 09:50:44,371 [1194494]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: No exchange with this request ID found. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:50:45,854 [1195977]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: IntersectionDisplay context not found.  Call setIntersectionContext first. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:50:46,152 [1196275]   WARN - #com.augmentcode.intellij.webviews.AbstractWebviewMessagingService - Failed to parse webview message as protobuf.
Exception: Missing type url when parsing: {"type":"can-show-terminal","data":{"command":"npm start"}}
Message: {"type":"async-wrapper","requestId":"4d444be8-0ede-436d-a1be-14ecaff663d3","error":null,"baseMsg":{"type":"can-show-terminal","data":{"command":"npm start"}},"destination":"host"}
2025-09-23 09:50:46,154 [1196277]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_ERROR]: Failed to check if terminal can be shown: Error: Unable to parse webview message: {"type":"async-wrapper","requestId":"4d444be8-0ede-436d-a1be-14ecaff663d3","error":null,"baseMsg":{"type":"can-show-terminal","data":{"command":"npm start"}},"destination":"host"} [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:50:46,685 [1196808]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (06bdfa42292c6761f12519c30ad7295691be3853015bd6c34d1212936fb5c59a) is unknown but expected to be uploaded
2025-09-23 09:50:46,685 [1196808]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (06bdfa42292c6761f12519c30ad7295691be3853015bd6c34d1212936fb5c59a) is unknown but expected to be uploaded
2025-09-23 09:50:46,685 [1196808]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (2cb7dc392c3bfae388e1ba2aede7524f9e3a87055fb6322588111b617636285c) is unknown but expected to be uploaded
2025-09-23 09:50:46,685 [1196808]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (2cb7dc392c3bfae388e1ba2aede7524f9e3a87055fb6322588111b617636285c) is unknown but expected to be uploaded
2025-09-23 09:50:46,685 [1196808]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (2cb7dc392c3bfae388e1ba2aede7524f9e3a87055fb6322588111b617636285c) is unknown but expected to be uploaded
2025-09-23 09:50:46,685 [1196808]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (2cb7dc392c3bfae388e1ba2aede7524f9e3a87055fb6322588111b617636285c) is unknown but expected to be uploaded
2025-09-23 09:50:46,685 [1196808]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (2cb7dc392c3bfae388e1ba2aede7524f9e3a87055fb6322588111b617636285c) is unknown but expected to be uploaded
2025-09-23 09:50:46,685 [1196808]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (2cb7dc392c3bfae388e1ba2aede7524f9e3a87055fb6322588111b617636285c) is unknown but expected to be uploaded
2025-09-23 09:50:46,685 [1196808]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (2cb7dc392c3bfae388e1ba2aede7524f9e3a87055fb6322588111b617636285c) is unknown but expected to be uploaded
2025-09-23 09:50:46,685 [1196808]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (2cb7dc392c3bfae388e1ba2aede7524f9e3a87055fb6322588111b617636285c) is unknown but expected to be uploaded
2025-09-23 09:50:46,685 [1196808]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (2cb7dc392c3bfae388e1ba2aede7524f9e3a87055fb6322588111b617636285c) is unknown but expected to be uploaded
2025-09-23 09:50:46,685 [1196808]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (2cb7dc392c3bfae388e1ba2aede7524f9e3a87055fb6322588111b617636285c) is unknown but expected to be uploaded
2025-09-23 09:50:46,685 [1196808]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (2cb7dc392c3bfae388e1ba2aede7524f9e3a87055fb6322588111b617636285c) is unknown but expected to be uploaded
2025-09-23 09:50:46,962 [1197085]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: b00ad1111f25c918407cccf97835e31d17b0252fc2451a909fe936722794d7df -> 2cb7dc392c3bfae388e1ba2aede7524f9e3a87055fb6322588111b617636285c
2025-09-23 09:50:47,844 [1197967]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 06bdfa42292c6761f12519c30ad7295691be3853015bd6c34d1212936fb5c59a -> 2cb7dc392c3bfae388e1ba2aede7524f9e3a87055fb6322588111b617636285c
2025-09-23 09:50:50,484 [1200607]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 2cb7dc392c3bfae388e1ba2aede7524f9e3a87055fb6322588111b617636285c -> a180e030656b80f102664e6024d55a3625dbc167bc8b0d781fbb1767d9b3356c
2025-09-23 09:50:51,494 [1201617]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 2cb7dc392c3bfae388e1ba2aede7524f9e3a87055fb6322588111b617636285c -> a180e030656b80f102664e6024d55a3625dbc167bc8b0d781fbb1767d9b3356c
2025-09-23 09:50:52,079 [1202202]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 2cb7dc392c3bfae388e1ba2aede7524f9e3a87055fb6322588111b617636285c -> a180e030656b80f102664e6024d55a3625dbc167bc8b0d781fbb1767d9b3356c
2025-09-23 09:50:52,927 [1203050]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 2cb7dc392c3bfae388e1ba2aede7524f9e3a87055fb6322588111b617636285c -> a180e030656b80f102664e6024d55a3625dbc167bc8b0d781fbb1767d9b3356c
2025-09-23 09:50:53,848 [1203971]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 2cb7dc392c3bfae388e1ba2aede7524f9e3a87055fb6322588111b617636285c -> a180e030656b80f102664e6024d55a3625dbc167bc8b0d781fbb1767d9b3356c
2025-09-23 09:50:54,381 [1204504]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 2cb7dc392c3bfae388e1ba2aede7524f9e3a87055fb6322588111b617636285c -> a180e030656b80f102664e6024d55a3625dbc167bc8b0d781fbb1767d9b3356c
2025-09-23 09:50:54,972 [1205095]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 2cb7dc392c3bfae388e1ba2aede7524f9e3a87055fb6322588111b617636285c -> a180e030656b80f102664e6024d55a3625dbc167bc8b0d781fbb1767d9b3356c
2025-09-23 09:50:55,664 [1205787]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 2cb7dc392c3bfae388e1ba2aede7524f9e3a87055fb6322588111b617636285c -> a180e030656b80f102664e6024d55a3625dbc167bc8b0d781fbb1767d9b3356c
2025-09-23 09:50:55,832 [1205955]   WARN - #com.augmentcode.intellij.webviews.AbstractWebviewMessagingService - Failed to parse webview message as protobuf.
Exception: Missing type url when parsing: {"type":"can-show-terminal","data":{"command":"npm start"}}
Message: {"type":"async-wrapper","requestId":"4dddf357-af15-402a-847b-a084858480bb","error":null,"baseMsg":{"type":"can-show-terminal","data":{"command":"npm start"}},"destination":"host"}
2025-09-23 09:50:55,909 [1206032]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_ERROR]: Failed to check if terminal can be shown: Error: Unable to parse webview message: {"type":"async-wrapper","requestId":"4dddf357-af15-402a-847b-a084858480bb","error":null,"baseMsg":{"type":"can-show-terminal","data":{"command":"npm start"}},"destination":"host"} [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:50:56,101 [1206224]   WARN - #com.augmentcode.intellij.chat.ChatWebviewMessageClient$Companion - Workspace guidelines are empty, not including in chat request. 
2025-09-23 09:50:56,319 [1206442]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 06bdfa42292c6761f12519c30ad7295691be3853015bd6c34d1212936fb5c59a -> a180e030656b80f102664e6024d55a3625dbc167bc8b0d781fbb1767d9b3356c
2025-09-23 09:50:56,382 [1206505]   WARN - #c.i.e.wsl - Failed to get WSL mount root for Ubuntu: DOS working directory is expected, but got \\wsl.localhost\Ubuntu\home\oni\audio-transcriber
2025-09-23 09:50:57,680 [1207803]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 2cb7dc392c3bfae388e1ba2aede7524f9e3a87055fb6322588111b617636285c -> f2f527e344f3b5c50aa068c5a78a40a3b9772f7b683d44d759451c2c6978da90
2025-09-23 09:50:59,862 [1209985]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: No exchange with this request ID found. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:51:00,271 [1210394]   WARN - #c.i.e.wsl - Failed to get WSL mount root for Ubuntu: DOS working directory is expected, but got \\wsl.localhost\Ubuntu\home\oni\audio-transcriber
2025-09-23 09:51:00,398 [1210521]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: IntersectionDisplay context not found.  Call setIntersectionContext first. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:51:00,571 [1210694]   WARN - #com.augmentcode.intellij.chat.ChatWebviewMessageClient$Companion - Workspace guidelines are empty, not including in chat request. 
2025-09-23 09:51:00,706 [1210829]   WARN - #com.augmentcode.intellij.webviews.AbstractWebviewMessagingService - Failed to parse webview message as protobuf.
Exception: Missing type url when parsing: {"type":"can-show-terminal","data":{"terminalId":28}}
Message: {"type":"async-wrapper","requestId":"70341e31-bedf-40be-929e-8732535b071b","error":null,"baseMsg":{"type":"can-show-terminal","data":{"terminalId":28}},"destination":"host"}
2025-09-23 09:51:00,707 [1210830]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_ERROR]: Failed to check if terminal can be shown: Error: Unable to parse webview message: {"type":"async-wrapper","requestId":"70341e31-bedf-40be-929e-8732535b071b","error":null,"baseMsg":{"type":"can-show-terminal","data":{"terminalId":28}},"destination":"host"} [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:51:00,943 [1211066]   WARN - #com.augmentcode.intellij.sentry.SentryMetadataCollector - Git ls-files command failed with exit code: 128
2025-09-23 09:51:01,284 [1211407]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (edda9769495a979d547cad00788e75fc60e013c050eca4b101760cbed2e40bb3) is unknown but expected to be uploaded
2025-09-23 09:51:01,284 [1211407]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (edda9769495a979d547cad00788e75fc60e013c050eca4b101760cbed2e40bb3) is unknown but expected to be uploaded
2025-09-23 09:51:01,284 [1211407]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (edda9769495a979d547cad00788e75fc60e013c050eca4b101760cbed2e40bb3) is unknown but expected to be uploaded
2025-09-23 09:51:01,284 [1211407]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (edda9769495a979d547cad00788e75fc60e013c050eca4b101760cbed2e40bb3) is unknown but expected to be uploaded
2025-09-23 09:51:01,284 [1211407]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (edda9769495a979d547cad00788e75fc60e013c050eca4b101760cbed2e40bb3) is unknown but expected to be uploaded
2025-09-23 09:51:01,284 [1211407]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (edda9769495a979d547cad00788e75fc60e013c050eca4b101760cbed2e40bb3) is unknown but expected to be uploaded
2025-09-23 09:51:01,284 [1211407]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (a180e030656b80f102664e6024d55a3625dbc167bc8b0d781fbb1767d9b3356c) is unknown but expected to be uploaded
2025-09-23 09:51:01,284 [1211407]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (a180e030656b80f102664e6024d55a3625dbc167bc8b0d781fbb1767d9b3356c) is unknown but expected to be uploaded
2025-09-23 09:51:01,284 [1211407]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (a180e030656b80f102664e6024d55a3625dbc167bc8b0d781fbb1767d9b3356c) is unknown but expected to be uploaded
2025-09-23 09:51:01,284 [1211407]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (a180e030656b80f102664e6024d55a3625dbc167bc8b0d781fbb1767d9b3356c) is unknown but expected to be uploaded
2025-09-23 09:51:01,284 [1211407]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (a180e030656b80f102664e6024d55a3625dbc167bc8b0d781fbb1767d9b3356c) is unknown but expected to be uploaded
2025-09-23 09:51:01,284 [1211407]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (a180e030656b80f102664e6024d55a3625dbc167bc8b0d781fbb1767d9b3356c) is unknown but expected to be uploaded
2025-09-23 09:51:01,284 [1211407]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (a180e030656b80f102664e6024d55a3625dbc167bc8b0d781fbb1767d9b3356c) is unknown but expected to be uploaded
2025-09-23 09:51:01,284 [1211407]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (a180e030656b80f102664e6024d55a3625dbc167bc8b0d781fbb1767d9b3356c) is unknown but expected to be uploaded
2025-09-23 09:51:01,284 [1211407]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (a180e030656b80f102664e6024d55a3625dbc167bc8b0d781fbb1767d9b3356c) is unknown but expected to be uploaded
2025-09-23 09:51:01,284 [1211407]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (a180e030656b80f102664e6024d55a3625dbc167bc8b0d781fbb1767d9b3356c) is unknown but expected to be uploaded
2025-09-23 09:51:01,958 [1212081]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: edda9769495a979d547cad00788e75fc60e013c050eca4b101760cbed2e40bb3 -> f2f527e344f3b5c50aa068c5a78a40a3b9772f7b683d44d759451c2c6978da90
2025-09-23 09:51:02,819 [1212942]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: edda9769495a979d547cad00788e75fc60e013c050eca4b101760cbed2e40bb3 -> f2f527e344f3b5c50aa068c5a78a40a3b9772f7b683d44d759451c2c6978da90
2025-09-23 09:51:03,465 [1213588]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: edda9769495a979d547cad00788e75fc60e013c050eca4b101760cbed2e40bb3 -> f2f527e344f3b5c50aa068c5a78a40a3b9772f7b683d44d759451c2c6978da90
2025-09-23 09:51:03,992 [1214115]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: a180e030656b80f102664e6024d55a3625dbc167bc8b0d781fbb1767d9b3356c -> f2f527e344f3b5c50aa068c5a78a40a3b9772f7b683d44d759451c2c6978da90
2025-09-23 09:51:04,542 [1214665]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: a180e030656b80f102664e6024d55a3625dbc167bc8b0d781fbb1767d9b3356c -> f2f527e344f3b5c50aa068c5a78a40a3b9772f7b683d44d759451c2c6978da90
2025-09-23 09:51:04,603 [1214726]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: No exchange with this request ID found. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:51:05,066 [1215189]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: a180e030656b80f102664e6024d55a3625dbc167bc8b0d781fbb1767d9b3356c -> f2f527e344f3b5c50aa068c5a78a40a3b9772f7b683d44d759451c2c6978da90
2025-09-23 09:51:05,662 [1215785]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: IntersectionDisplay context not found.  Call setIntersectionContext first. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:51:05,833 [1215956]   WARN - #com.augmentcode.intellij.chat.ChatWebviewMessageClient$Companion - Workspace guidelines are empty, not including in chat request. 
2025-09-23 09:51:05,954 [1216077]   WARN - #com.augmentcode.intellij.webviews.AbstractWebviewMessagingService - Failed to parse webview message as protobuf.
Exception: Missing type url when parsing: {"type":"can-show-terminal","data":{"terminalId":28}}
Message: {"type":"async-wrapper","requestId":"2b9b8cd0-ead0-4a34-be58-e8e9a41f8fde","error":null,"baseMsg":{"type":"can-show-terminal","data":{"terminalId":28}},"destination":"host"}
2025-09-23 09:51:05,955 [1216078]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_ERROR]: Failed to check if terminal can be shown: Error: Unable to parse webview message: {"type":"async-wrapper","requestId":"2b9b8cd0-ead0-4a34-be58-e8e9a41f8fde","error":null,"baseMsg":{"type":"can-show-terminal","data":{"terminalId":28}},"destination":"host"} [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:51:06,167 [1216290]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: a180e030656b80f102664e6024d55a3625dbc167bc8b0d781fbb1767d9b3356c -> f2f527e344f3b5c50aa068c5a78a40a3b9772f7b683d44d759451c2c6978da90
2025-09-23 09:51:06,471 [1216594]   WARN - #c.i.u.i.p.ProjectIndexableFilesFilterHealthCheck - Following files are indexable but they were NOT found in filter. Errors count: 2. Examples:
file id=199049 path=//wsl.localhost/Ubuntu/home/<USER>/audio-transcriber/node_modules/.cache/default-development/0.pack
file id=202153 path=//wsl.localhost/Ubuntu/home/<USER>/audio-transcriber/node_modules/tailwindcss/peers/index.js
2025-09-23 09:51:06,941 [1217064]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: a180e030656b80f102664e6024d55a3625dbc167bc8b0d781fbb1767d9b3356c -> f2f527e344f3b5c50aa068c5a78a40a3b9772f7b683d44d759451c2c6978da90
2025-09-23 09:51:07,575 [1217698]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: edda9769495a979d547cad00788e75fc60e013c050eca4b101760cbed2e40bb3 -> f2f527e344f3b5c50aa068c5a78a40a3b9772f7b683d44d759451c2c6978da90
2025-09-23 09:51:08,118 [1218241]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: edda9769495a979d547cad00788e75fc60e013c050eca4b101760cbed2e40bb3 -> f2f527e344f3b5c50aa068c5a78a40a3b9772f7b683d44d759451c2c6978da90
2025-09-23 09:51:08,871 [1218994]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: a180e030656b80f102664e6024d55a3625dbc167bc8b0d781fbb1767d9b3356c -> f2f527e344f3b5c50aa068c5a78a40a3b9772f7b683d44d759451c2c6978da90
2025-09-23 09:51:09,561 [1219684]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: No exchange with this request ID found. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:51:09,598 [1219721]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: a180e030656b80f102664e6024d55a3625dbc167bc8b0d781fbb1767d9b3356c -> f2f527e344f3b5c50aa068c5a78a40a3b9772f7b683d44d759451c2c6978da90
2025-09-23 09:51:10,661 [1220784]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: edda9769495a979d547cad00788e75fc60e013c050eca4b101760cbed2e40bb3 -> f2f527e344f3b5c50aa068c5a78a40a3b9772f7b683d44d759451c2c6978da90
2025-09-23 09:51:11,225 [1221348]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: IntersectionDisplay context not found.  Call setIntersectionContext first. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:51:11,391 [1221514]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: a180e030656b80f102664e6024d55a3625dbc167bc8b0d781fbb1767d9b3356c -> f2f527e344f3b5c50aa068c5a78a40a3b9772f7b683d44d759451c2c6978da90
2025-09-23 09:51:11,408 [1221531]   WARN - #com.augmentcode.intellij.chat.ChatWebviewMessageClient$Companion - Workspace guidelines are empty, not including in chat request. 
2025-09-23 09:51:11,534 [1221657]   WARN - #com.augmentcode.intellij.webviews.AbstractWebviewMessagingService - Failed to parse webview message as protobuf.
Exception: Missing type url when parsing: {"type":"can-show-terminal","data":{"terminalId":28}}
Message: {"type":"async-wrapper","requestId":"d16c5f01-e558-4b5c-bf24-2945b57f3a72","error":null,"baseMsg":{"type":"can-show-terminal","data":{"terminalId":28}},"destination":"host"}
2025-09-23 09:51:11,537 [1221660]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_ERROR]: Failed to check if terminal can be shown: Error: Unable to parse webview message: {"type":"async-wrapper","requestId":"d16c5f01-e558-4b5c-bf24-2945b57f3a72","error":null,"baseMsg":{"type":"can-show-terminal","data":{"terminalId":28}},"destination":"host"} [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:51:11,997 [1222120]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: a180e030656b80f102664e6024d55a3625dbc167bc8b0d781fbb1767d9b3356c -> f2f527e344f3b5c50aa068c5a78a40a3b9772f7b683d44d759451c2c6978da90
2025-09-23 09:51:12,178 [1222301]   WARN - #c.i.e.wsl - Failed to get WSL mount root for Ubuntu: DOS working directory is expected, but got \\wsl.localhost\Ubuntu\home\oni\audio-transcriber
2025-09-23 09:51:15,662 [1225785]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: a180e030656b80f102664e6024d55a3625dbc167bc8b0d781fbb1767d9b3356c -> bd68cd0d398968d036957dc3999512d6420f462870650e46f2f9d5285a709bc7
2025-09-23 09:51:15,680 [1225803]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: No exchange with this request ID found. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:51:18,192 [1228315]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: IntersectionDisplay context not found.  Call setIntersectionContext first. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:51:19,068 [1229191]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Non of the blobs were indexed, backing off and retrying...
2025-09-23 09:51:19,281 [1229404]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: Loading changes  for af41568e-3d7a-4c8e-b9d5-d02166a7891e retry 0:  Error: No changes found [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:51:19,281 [1229404]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: Loading changes  for af41568e-3d7a-4c8e-b9d5-d02166a7891e retry 1:  Error: No changes found [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:51:19,328 [1229451]   WARN - #com.augmentcode.intellij.chat.ChatWebviewMessageClient$Companion - Workspace guidelines are empty, not including in chat request. 
2025-09-23 09:51:19,782 [1229905]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: Loading changes  for af41568e-3d7a-4c8e-b9d5-d02166a7891e retry 2:  Error: No changes found [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:51:19,783 [1229906]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: Loading changes  for af41568e-3d7a-4c8e-b9d5-d02166a7891e retry 3:  Error: No changes found [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:51:19,783 [1229906]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: Loading changes failed for af41568e-3d7a-4c8e-b9d5-d02166a7891e:  Error: No changes found [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:51:20,291 [1230414]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: Loading changes  for af41568e-3d7a-4c8e-b9d5-d02166a7891e retry 4:  Error: No changes found [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:51:20,291 [1230414]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: Loading changes failed for af41568e-3d7a-4c8e-b9d5-d02166a7891e:  Error: No changes found [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:51:23,507 [1233630]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: No exchange with this request ID found. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:51:24,864 [1234987]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: IntersectionDisplay context not found.  Call setIntersectionContext first. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:51:25,169 [1235292]   WARN - #com.augmentcode.intellij.webviews.AbstractWebviewMessagingService - Failed to parse webview message as protobuf.
Exception: Missing type url when parsing: {"type":"can-show-terminal","data":{"command":"npm start"}}
Message: {"type":"async-wrapper","requestId":"fb19761f-1e25-49a7-8be7-a712b008ad9e","error":null,"baseMsg":{"type":"can-show-terminal","data":{"command":"npm start"}},"destination":"host"}
2025-09-23 09:51:25,171 [1235294]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_ERROR]: Failed to check if terminal can be shown: Error: Unable to parse webview message: {"type":"async-wrapper","requestId":"fb19761f-1e25-49a7-8be7-a712b008ad9e","error":null,"baseMsg":{"type":"can-show-terminal","data":{"command":"npm start"}},"destination":"host"} [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:51:32,481 [1242604]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (bd68cd0d398968d036957dc3999512d6420f462870650e46f2f9d5285a709bc7) is unknown but expected to be uploaded
2025-09-23 09:51:32,481 [1242604]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (bd68cd0d398968d036957dc3999512d6420f462870650e46f2f9d5285a709bc7) is unknown but expected to be uploaded
2025-09-23 09:51:33,257 [1243380]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: bd68cd0d398968d036957dc3999512d6420f462870650e46f2f9d5285a709bc7 -> c6541327b2aaf6747abc99d8b0bb9cd2179e584ab0a07cb8e282b9cdf9cba6c8
2025-09-23 09:51:36,722 [1246845]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Non of the blobs were indexed, backing off and retrying...
2025-09-23 09:51:37,032 [1247155]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: bd68cd0d398968d036957dc3999512d6420f462870650e46f2f9d5285a709bc7 -> c6541327b2aaf6747abc99d8b0bb9cd2179e584ab0a07cb8e282b9cdf9cba6c8
2025-09-23 09:51:40,552 [1250675]   WARN - #com.augmentcode.intellij.webviews.AbstractWebviewMessagingService - Failed to parse webview message as protobuf.
Exception: Missing type url when parsing: {"type":"can-show-terminal","data":{"command":"npm start"}}
Message: {"type":"async-wrapper","requestId":"a7bb18e9-8951-459d-8ad0-54da9975547d","error":null,"baseMsg":{"type":"can-show-terminal","data":{"command":"npm start"}},"destination":"host"}
2025-09-23 09:51:40,609 [1250732]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_ERROR]: Failed to check if terminal can be shown: Error: Unable to parse webview message: {"type":"async-wrapper","requestId":"a7bb18e9-8951-459d-8ad0-54da9975547d","error":null,"baseMsg":{"type":"can-show-terminal","data":{"command":"npm start"}},"destination":"host"} [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:51:40,744 [1250867]   WARN - #com.augmentcode.intellij.chat.ChatWebviewMessageClient$Companion - Workspace guidelines are empty, not including in chat request. 
2025-09-23 09:51:41,118 [1251241]   WARN - #c.i.e.wsl - Failed to get WSL mount root for Ubuntu: DOS working directory is expected, but got \\wsl.localhost\Ubuntu\home\oni\audio-transcriber
2025-09-23 09:51:44,333 [1254456]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: No exchange with this request ID found. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:51:44,532 [1254655]   WARN - #c.i.e.wsl - Failed to get WSL mount root for Ubuntu: DOS working directory is expected, but got \\wsl.localhost\Ubuntu\home\oni\audio-transcriber
2025-09-23 09:51:44,920 [1255043]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: IntersectionDisplay context not found.  Call setIntersectionContext first. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:51:45,100 [1255223]   WARN - #com.augmentcode.intellij.chat.ChatWebviewMessageClient$Companion - Workspace guidelines are empty, not including in chat request. 
2025-09-23 09:51:45,232 [1255355]   WARN - #com.augmentcode.intellij.webviews.AbstractWebviewMessagingService - Failed to parse webview message as protobuf.
Exception: Missing type url when parsing: {"type":"can-show-terminal","data":{"terminalId":29}}
Message: {"type":"async-wrapper","requestId":"bcd1e7cc-feab-4ede-b431-d1162af0ba38","error":null,"baseMsg":{"type":"can-show-terminal","data":{"terminalId":29}},"destination":"host"}
2025-09-23 09:51:45,234 [1255357]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_ERROR]: Failed to check if terminal can be shown: Error: Unable to parse webview message: {"type":"async-wrapper","requestId":"bcd1e7cc-feab-4ede-b431-d1162af0ba38","error":null,"baseMsg":{"type":"can-show-terminal","data":{"terminalId":29}},"destination":"host"} [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:51:48,490 [1258613]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: No exchange with this request ID found. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:51:49,362 [1259485]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: IntersectionDisplay context not found.  Call setIntersectionContext first. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:51:49,564 [1259687]   WARN - #com.augmentcode.intellij.chat.ChatWebviewMessageClient$Companion - Workspace guidelines are empty, not including in chat request. 
2025-09-23 09:51:49,677 [1259800]   WARN - #com.augmentcode.intellij.webviews.AbstractWebviewMessagingService - Failed to parse webview message as protobuf.
Exception: Missing type url when parsing: {"type":"can-show-terminal","data":{"terminalId":29}}
Message: {"type":"async-wrapper","requestId":"b127cd12-c57c-4e67-8bfc-9aa9a2b08d5a","error":null,"baseMsg":{"type":"can-show-terminal","data":{"terminalId":29}},"destination":"host"}
2025-09-23 09:51:49,679 [1259802]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_ERROR]: Failed to check if terminal can be shown: Error: Unable to parse webview message: {"type":"async-wrapper","requestId":"b127cd12-c57c-4e67-8bfc-9aa9a2b08d5a","error":null,"baseMsg":{"type":"can-show-terminal","data":{"terminalId":29}},"destination":"host"} [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:51:53,578 [1263701]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (c6541327b2aaf6747abc99d8b0bb9cd2179e584ab0a07cb8e282b9cdf9cba6c8) is unknown but expected to be uploaded
2025-09-23 09:51:53,578 [1263701]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (c6541327b2aaf6747abc99d8b0bb9cd2179e584ab0a07cb8e282b9cdf9cba6c8) is unknown but expected to be uploaded
2025-09-23 09:51:54,295 [1264418]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: c6541327b2aaf6747abc99d8b0bb9cd2179e584ab0a07cb8e282b9cdf9cba6c8 -> d540b8f49b355a663564d1ba447e702a97f2dc529192d7827dbbac3e5e96a618
2025-09-23 09:51:54,462 [1264585]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: No exchange with this request ID found. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:51:57,227 [1267350]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: IntersectionDisplay context not found.  Call setIntersectionContext first. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:51:57,531 [1267654]   WARN - #com.augmentcode.intellij.webviews.AbstractWebviewMessagingService - Failed to parse webview message as protobuf.
Exception: Missing type url when parsing: {"type":"can-show-terminal","data":{"terminalId":29}}
Message: {"type":"async-wrapper","requestId":"23c47cf9-36df-4ce4-b3f5-9c7479d13e3a","error":null,"baseMsg":{"type":"can-show-terminal","data":{"terminalId":29}},"destination":"host"}
2025-09-23 09:51:57,531 [1267654]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_ERROR]: Failed to check if terminal can be shown: Error: Unable to parse webview message: {"type":"async-wrapper","requestId":"23c47cf9-36df-4ce4-b3f5-9c7479d13e3a","error":null,"baseMsg":{"type":"can-show-terminal","data":{"terminalId":29}},"destination":"host"} [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:51:57,975 [1268098]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: c6541327b2aaf6747abc99d8b0bb9cd2179e584ab0a07cb8e282b9cdf9cba6c8 -> d540b8f49b355a663564d1ba447e702a97f2dc529192d7827dbbac3e5e96a618
2025-09-23 09:51:58,124 [1268247]   WARN - #c.i.e.wsl - Failed to get WSL mount root for Ubuntu: DOS working directory is expected, but got \\wsl.localhost\Ubuntu\home\oni\audio-transcriber
2025-09-23 09:52:01,088 [1271211]   WARN - #com.augmentcode.intellij.sentry.SentryMetadataCollector - Git ls-files command failed with exit code: 128
2025-09-23 09:52:37,569 [1307692]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: IntersectionDisplay context not found.  Call setIntersectionContext first. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:52:37,820 [1307943]   WARN - #com.augmentcode.intellij.chat.ChatWebviewMessageClient$Companion - Workspace guidelines are empty, not including in chat request. 
2025-09-23 09:52:37,915 [1308038]   WARN - #c.i.e.wsl - Failed to get WSL mount root for Ubuntu: DOS working directory is expected, but got \\wsl.localhost\Ubuntu\home\oni\audio-transcriber
2025-09-23 09:52:42,073 [1312196]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: No exchange with this request ID found. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:52:45,285 [1315408]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: IntersectionDisplay context not found.  Call setIntersectionContext first. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:52:45,595 [1315718]   WARN - #com.augmentcode.intellij.webviews.AbstractWebviewMessagingService - Failed to parse webview message as protobuf.
Exception: Missing type url when parsing: {"type":"can-show-terminal","data":{"command":"mv src/App.js src/App-tailwind.js && mv src/App-simple.js src/App.js"}}
Message: {"type":"async-wrapper","requestId":"61882826-0be0-413f-8207-0374ba90470a","error":null,"baseMsg":{"type":"can-show-terminal","data":{"command":"mv src/App.js src/App-tailwind.js && mv src/App-simple.js src/App.js"}},"destination":"host"}
2025-09-23 09:52:45,598 [1315721]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_ERROR]: Failed to check if terminal can be shown: Error: Unable to parse webview message: {"type":"async-wrapper","requestId":"61882826-0be0-413f-8207-0374ba90470a","error":null,"baseMsg":{"type":"can-show-terminal","data":{"command":"mv src/App.js src/App-tailwind.js && mv src/App-simple.js src/App.js"}},"destination":"host"} [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:52:57,299 [1327422]   WARN - #com.augmentcode.intellij.api.AugmentHttpClient$Companion - Failed to call find-missing (11b92c93-e562-4b8f-af2d-9359bcc1743b)
io.ktor.client.plugins.HttpRequestTimeoutException: Request timeout has expired [url=https://i1.api.augmentcode.com/find-missing, request_timeout=60000 ms]
	at io.ktor.client.plugins.HttpTimeout$Plugin$install$1$1$killer$1.invokeSuspend(HttpTimeout.kt:165)
	at kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)
	at kotlinx.coroutines.DispatchedTask.run(DispatchedTask.kt:100)
	at kotlinx.coroutines.internal.SoftLimitedDispatcher$Worker.run(SoftLimitedDispatcher.kt:130)
	at kotlinx.coroutines.scheduling.TaskImpl.run(Tasks.kt:89)
	at kotlinx.coroutines.scheduling.CoroutineScheduler.runSafely(CoroutineScheduler.kt:613)
	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.executeTask(CoroutineScheduler.kt:1183)
	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.runWorker(CoroutineScheduler.kt:778)
	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.run(CoroutineScheduler.kt:765)
2025-09-23 09:52:57,300 [1327423]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Failed to check indexing status (Requeue-ing blobs): Failed to make network call to find-missing with request ID 11b92c93-e562-4b8f-af2d-9359bcc1743b: Request timeout has expired [url=https://i1.api.augmentcode.com/find-missing, request_timeout=60000 ms]
2025-09-23 09:53:00,387 [1330510]   WARN - #c.i.e.wsl - Failed to get WSL mount root for Ubuntu: DOS working directory is expected, but got \\wsl.localhost\Ubuntu\home\oni\audio-transcriber
2025-09-23 09:53:01,342 [1331465]   WARN - #com.augmentcode.intellij.sentry.SentryMetadataCollector - Git ls-files command failed with exit code: 128
2025-09-23 09:53:30,738 [1360861]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (d540b8f49b355a663564d1ba447e702a97f2dc529192d7827dbbac3e5e96a618) is unknown but expected to be uploaded
2025-09-23 09:53:30,738 [1360861]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (d540b8f49b355a663564d1ba447e702a97f2dc529192d7827dbbac3e5e96a618) is unknown but expected to be uploaded
2025-09-23 09:53:31,394 [1361517]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: d540b8f49b355a663564d1ba447e702a97f2dc529192d7827dbbac3e5e96a618 -> 3767a8c9bb7b130dded284d4993033afb2bd879acaa4cb08fa17a11e93550d83
2025-09-23 09:53:34,897 [1365020]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Non of the blobs were indexed, backing off and retrying...
2025-09-23 09:53:34,914 [1365037]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: d540b8f49b355a663564d1ba447e702a97f2dc529192d7827dbbac3e5e96a618 -> 3767a8c9bb7b130dded284d4993033afb2bd879acaa4cb08fa17a11e93550d83
2025-09-23 09:53:37,576 [1367699]   WARN - #com.augmentcode.intellij.webviews.AbstractWebviewMessagingService - Failed to parse webview message as protobuf.
Exception: Missing type url when parsing: {"type":"can-show-terminal","data":{"command":"mv src/App.js src/App-tailwind.js && mv src/App-simple.js src/App.js"}}
Message: {"type":"async-wrapper","requestId":"6d1b3485-a1cd-4766-aede-e966a3b0424c","error":null,"baseMsg":{"type":"can-show-terminal","data":{"command":"mv src/App.js src/App-tailwind.js && mv src/App-simple.js src/App.js"}},"destination":"host"}
2025-09-23 09:53:37,640 [1367763]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_ERROR]: Failed to check if terminal can be shown: Error: Unable to parse webview message: {"type":"async-wrapper","requestId":"6d1b3485-a1cd-4766-aede-e966a3b0424c","error":null,"baseMsg":{"type":"can-show-terminal","data":{"command":"mv src/App.js src/App-tailwind.js && mv src/App-simple.js src/App.js"}},"destination":"host"} [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:53:37,735 [1367858]   WARN - #com.augmentcode.intellij.chat.ChatWebviewMessageClient$Companion - Workspace guidelines are empty, not including in chat request. 
