2025-09-23 09:30:50,374 [    497]   WARN - #c.i.o.v.n.p.FSRecords - Records storage [C:\Users\<USER>\AppData\Local\JetBrains\IdeaIC2025.2\caches\records.dat] was in use by process [OwnershipInfo{owner pid: 41708, acquired at: 1757584871708}] which is not exist now (wasn't closed properly/crashed?) -> re-acquiring forcibly
2025-09-23 09:30:50,394 [    517]   WARN - #c.i.o.v.n.p.PersistentFSLoader - [VFS load problem]: VFS wasn't safely shut down: records.wasClosedProperly is false
2025-09-23 09:30:51,490 [   1613]   WARN - #c.i.o.f.i.FileTypeManagerImpl - 
ua.t3hnar.plugins.cmdsupport.CmdFileType$@14b74d56 from 'PluginMainDescriptor(name=CMD Support, id=CMD Support, version=1.0.5, isBundled=false, path=~\AppData\Roaming\JetBrains\IdeaIC2025.2\plugins\cmdsupport)' (class ua.t3hnar.plugins.cmdsupport.CmdFileType$) and
ua.t3hnar.plugins.cmdsupport.BatFileType$@6ce9fca from 'PluginMainDescriptor(name=CMD Support, id=CMD Support, version=1.0.5, isBundled=false, path=~\AppData\Roaming\JetBrains\IdeaIC2025.2\plugins\cmdsupport)' (class ua.t3hnar.plugins.cmdsupport.BatFileType$)
 both have the same .getDisplayName(): 'Cmd'. Please override either one's getDisplayName() to something unique. [Plugin: CMD Support]
com.intellij.diagnostic.PluginException: 
ua.t3hnar.plugins.cmdsupport.CmdFileType$@14b74d56 from 'PluginMainDescriptor(name=CMD Support, id=CMD Support, version=1.0.5, isBundled=false, path=~\AppData\Roaming\JetBrains\IdeaIC2025.2\plugins\cmdsupport)' (class ua.t3hnar.plugins.cmdsupport.CmdFileType$) and
ua.t3hnar.plugins.cmdsupport.BatFileType$@6ce9fca from 'PluginMainDescriptor(name=CMD Support, id=CMD Support, version=1.0.5, isBundled=false, path=~\AppData\Roaming\JetBrains\IdeaIC2025.2\plugins\cmdsupport)' (class ua.t3hnar.plugins.cmdsupport.BatFileType$)
 both have the same .getDisplayName(): 'Cmd'. Please override either one's getDisplayName() to something unique. [Plugin: CMD Support]
	at com.intellij.openapi.fileTypes.impl.FileTypeManagerImpl.checkUnique(FileTypeManagerImpl.java:1625)
	at com.intellij.openapi.fileTypes.impl.FileTypeManagerImpl.checkUnique(FileTypeManagerImpl.java:1598)
	at com.intellij.openapi.fileTypes.impl.FileTypeManagerImpl.checkUnique(FileTypeManagerImpl.java:1588)
	at com.intellij.openapi.fileTypes.impl.AlarmAdapterKt$singleAlarm$1$1.invokeSuspend(AlarmAdapter.kt:24)
	at com.intellij.openapi.fileTypes.impl.AlarmAdapterKt$singleAlarm$1$1.invoke(AlarmAdapter.kt)
	at com.intellij.openapi.fileTypes.impl.AlarmAdapterKt$singleAlarm$1$1.invoke(AlarmAdapter.kt)
	at kotlinx.coroutines.flow.FlowKt__MergeKt$mapLatest$1.invokeSuspend(Merge.kt:213)
	at kotlinx.coroutines.flow.FlowKt__MergeKt$mapLatest$1.invoke(Merge.kt)
	at kotlinx.coroutines.flow.FlowKt__MergeKt$mapLatest$1.invoke(Merge.kt)
	at kotlinx.coroutines.flow.internal.ChannelFlowTransformLatest$flowCollect$3$1$2.invokeSuspend(Merge.kt:30)
	at kotlinx.coroutines.flow.internal.ChannelFlowTransformLatest$flowCollect$3$1$2.invoke(Merge.kt)
	at kotlinx.coroutines.flow.internal.ChannelFlowTransformLatest$flowCollect$3$1$2.invoke(Merge.kt)
	at kotlinx.coroutines.intrinsics.UndispatchedKt.startCoroutineUndispatched(Undispatched.kt:20)
	at kotlinx.coroutines.CoroutineStart.invoke(CoroutineStart.kt:360)
	at kotlinx.coroutines.AbstractCoroutine.start(AbstractCoroutine.kt:134)
	at kotlinx.coroutines.BuildersKt__Builders_commonKt.launch(Builders.common.kt:52)
	at kotlinx.coroutines.BuildersKt.launch(Unknown Source)
	at kotlinx.coroutines.BuildersKt__Builders_commonKt.launch$default(Builders.common.kt:43)
	at kotlinx.coroutines.BuildersKt.launch$default(Unknown Source)
	at kotlinx.coroutines.flow.internal.ChannelFlowTransformLatest$flowCollect$3$1.emit(Merge.kt:29)
	at kotlinx.coroutines.flow.internal.FlowValueWrapperInternalKt.emitInternal(FlowValueWrapperInternal.kt:39)
	at kotlinx.coroutines.flow.FlowKt__DelayKt$debounceInternal$1$3$1.invokeSuspend(Delay.kt:226)
	at kotlinx.coroutines.flow.FlowKt__DelayKt$debounceInternal$1$3$1.invoke(Delay.kt)
	at kotlinx.coroutines.flow.FlowKt__DelayKt$debounceInternal$1$3$1.invoke(Delay.kt)
	at kotlinx.coroutines.selects.SelectImplementation$ClauseData.invokeBlock(Select.kt:847)
	at kotlinx.coroutines.selects.SelectImplementation$complete$3.invokeSuspend(Select.kt:718)
	at kotlinx.coroutines.selects.SelectImplementation$complete$3.invoke(Select.kt)
	at kotlinx.coroutines.selects.SelectImplementation$complete$3.invoke(Select.kt)
	at kotlinx.coroutines.flow.internal.FlowValueWrapperInternalKt.debuggerCapture(FlowValueWrapperInternal.kt:44)
	at kotlinx.coroutines.selects.SelectImplementation.complete(Select.kt:712)
	at kotlinx.coroutines.selects.SelectImplementation.doSelectSuspend(Select.kt:458)
	at kotlinx.coroutines.selects.SelectImplementation.access$doSelectSuspend(Select.kt:253)
	at kotlinx.coroutines.selects.SelectImplementation$doSelectSuspend$1.invokeSuspend(Select.kt)
	at kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)
	at kotlinx.coroutines.DispatchedTask.run(DispatchedTask.kt:100)
	at kotlinx.coroutines.scheduling.CoroutineScheduler.runSafely(CoroutineScheduler.kt:613)
	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.executeTask(CoroutineScheduler.kt:1189)
	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.runWorker(CoroutineScheduler.kt:778)
	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.run(CoroutineScheduler.kt:765)
2025-09-23 09:30:51,549 [   1672]   WARN - #c.i.o.v.n.p.PersistentFSLoader - [VFS load problem]: NOT_CLOSED_PROPERLY recovered, no problems remain
2025-09-23 09:30:51,623 [   1746]   WARN - #c.i.i.s.p.i.BundledSharedIndexProvider - Bundled shared index is not found at: C:\Program Files\JetBrains\IntelliJ IDEA Community Edition 2025.2.2\jdk-shared-indexes
2025-09-23 09:30:52,077 [   2200]   WARN - #c.i.e.wsl - Failed to get WSL mount root for Ubuntu: DOS working directory is expected, but got \\wsl.localhost\Ubuntu\home\oni\audio-transcriber
2025-09-23 09:30:54,036 [   4159]   WARN - #c.i.s.ComponentManagerImpl - com.augmentcode.intellij.syncing.AugmentRemoteSyncingManagerImpl PluginClassLoader(plugin=PluginMainDescriptor(name=Augment: AI coding assistant for professionals, id=com.augmentcode, version=0.288.0, isBundled=false, path=~\AppData\Roaming\JetBrains\IdeaIC2025.2\plugins\intellij-augment), packagePrefix=null, state=active, parents=ContentModuleDescriptor(moduleName=intellij.platform.vcs.impl) <- PluginMainDescriptor(name=IDEA CORE, id=com.intellij, version=252.26199.169, isBundled=true, path=C:\Program Files\JetBrains\IntelliJ IDEA Community Edition 2025.2.2\lib), ContentModuleDescriptor(moduleName=intellij.platform.vcs.log.impl) <- PluginMainDescriptor(name=IDEA CORE, id=com.intellij, version=252.26199.169, isBundled=true, path=C:\Program Files\JetBrains\IntelliJ IDEA Community Edition 2025.2.2\lib), ContentModuleDescriptor(moduleName=intellij.platform.vcs.dvcs.impl) <- PluginMainDescriptor(name=IDEA CORE, id=com.intellij, version=252.26199.169, isBundled=true, path=C:\Program Files\JetBrains\IntelliJ IDEA Community Edition 2025.2.2\lib), ContentModuleDescriptor(moduleName=intellij.platform.collaborationTools) <- PluginMainDescriptor(name=IDEA CORE, id=com.intellij, version=252.26199.169, isBundled=true, path=C:\Program Files\JetBrains\IntelliJ IDEA Community Edition 2025.2.2\lib), PluginMainDescriptor(name=Terminal, id=org.jetbrains.plugins.terminal, version=252.26199.169, package=org.jetbrains.plugins.terminal, isBundled=true, path=C:\Program Files\JetBrains\IntelliJ IDEA Community Edition 2025.2.2\plugins\terminal), PluginMainDescriptor(name=Shell Script, id=com.jetbrains.sh, version=252.26199.169, package=com.intellij.sh, isBundled=true, path=C:\Program Files\JetBrains\IntelliJ IDEA Community Edition 2025.2.2\plugins\sh), )
java.lang.ClassNotFoundException: com.augmentcode.intellij.syncing.AugmentRemoteSyncingManagerImpl PluginClassLoader(plugin=PluginMainDescriptor(name=Augment: AI coding assistant for professionals, id=com.augmentcode, version=0.288.0, isBundled=false, path=~\AppData\Roaming\JetBrains\IdeaIC2025.2\plugins\intellij-augment), packagePrefix=null, state=active, parents=ContentModuleDescriptor(moduleName=intellij.platform.vcs.impl) <- PluginMainDescriptor(name=IDEA CORE, id=com.intellij, version=252.26199.169, isBundled=true, path=C:\Program Files\JetBrains\IntelliJ IDEA Community Edition 2025.2.2\lib), ContentModuleDescriptor(moduleName=intellij.platform.vcs.log.impl) <- PluginMainDescriptor(name=IDEA CORE, id=com.intellij, version=252.26199.169, isBundled=true, path=C:\Program Files\JetBrains\IntelliJ IDEA Community Edition 2025.2.2\lib), ContentModuleDescriptor(moduleName=intellij.platform.vcs.dvcs.impl) <- PluginMainDescriptor(name=IDEA CORE, id=com.intellij, version=252.26199.169, isBundled=true, path=C:\Program Files\JetBrains\IntelliJ IDEA Community Edition 2025.2.2\lib), ContentModuleDescriptor(moduleName=intellij.platform.collaborationTools) <- PluginMainDescriptor(name=IDEA CORE, id=com.intellij, version=252.26199.169, isBundled=true, path=C:\Program Files\JetBrains\IntelliJ IDEA Community Edition 2025.2.2\lib), PluginMainDescriptor(name=Terminal, id=org.jetbrains.plugins.terminal, version=252.26199.169, package=org.jetbrains.plugins.terminal, isBundled=true, path=C:\Program Files\JetBrains\IntelliJ IDEA Community Edition 2025.2.2\plugins\terminal), PluginMainDescriptor(name=Shell Script, id=com.jetbrains.sh, version=252.26199.169, package=com.intellij.sh, isBundled=true, path=C:\Program Files\JetBrains\IntelliJ IDEA Community Edition 2025.2.2\plugins\sh), )
	at com.intellij.serviceContainer.ComponentManagerImplKt.doLoadClass(ComponentManagerImpl.kt:1433)
	at com.intellij.serviceContainer.ComponentManagerImplKt.doLoadClass$default(ComponentManagerImpl.kt:1429)
	at com.intellij.serviceContainer.ServiceDescriptorInstanceInitializer.loadInstanceClass(ServiceInstanceInitializer.kt:97)
	at com.intellij.platform.instanceContainer.internal.LazyInstanceHolder.instanceClass(LazyInstanceHolder.kt:55)
	at com.intellij.serviceContainer.ComponentManagerImpl.processAllHolders$process(ComponentManagerImpl.kt:1245)
	at com.intellij.serviceContainer.ComponentManagerImpl.processAllHolders(ComponentManagerImpl.kt:1264)
	at com.intellij.openapi.project.impl.DefaultProject.processAllHolders(DefaultProject.kt:193)
	at com.intellij.configurationStore.DefaultProjectElementNormalizerKt.moveComponentConfiguration(defaultProjectElementNormalizer.kt:125)
	at com.intellij.configurationStore.DefaultProjectElementNormalizerKt.normalizeDefaultProjectElement(defaultProjectElementNormalizer.kt:60)
	at com.intellij.configurationStore.ProjectStoreImpl.loadProjectFromTemplate(ProjectStoreImpl.kt:190)
	at com.intellij.configurationStore.ProjectStoreImpl.setPath(ProjectStoreImpl.kt:158)
	at com.intellij.openapi.project.impl.ProjectManagerImplKt.initProject(ProjectManagerImpl.kt:1329)
	at com.intellij.openapi.project.impl.ProjectManagerImplKt.access$initProject(ProjectManagerImpl.kt:1)
	at com.intellij.openapi.project.impl.ProjectManagerImplKt$initProject$1.invokeSuspend(ProjectManagerImpl.kt)
	at kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)
	at kotlinx.coroutines.UndispatchedCoroutine.afterResume(CoroutineContext.kt:277)
	at kotlinx.coroutines.AbstractCoroutine.resumeWith(AbstractCoroutine.kt:101)
	at kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:46)
	at kotlinx.coroutines.UndispatchedCoroutine.afterResume(CoroutineContext.kt:277)
	at kotlinx.coroutines.AbstractCoroutine.resumeWith(AbstractCoroutine.kt:101)
	at kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:46)
	at kotlinx.coroutines.DispatchedTask.run(DispatchedTask.kt:100)
	at kotlinx.coroutines.scheduling.CoroutineScheduler.runSafely(CoroutineScheduler.kt:613)
	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.executeTask(CoroutineScheduler.kt:1189)
	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.runWorker(CoroutineScheduler.kt:778)
	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.run(CoroutineScheduler.kt:765)
SLF4J: Failed to load class "org.slf4j.impl.StaticLoggerBinder".
SLF4J: Defaulting to no-operation (NOP) logger implementation
SLF4J: See http://www.slf4j.org/codes.html#StaticLoggerBinder for further details.
2025-09-23 09:30:56,052 [   6175]   WARN - #c.i.s.c.i.StationSocketConnectionLoop - RecentProjects connection failed with AnnotatedConnectException(ConnectException) (Connection refused: connect: C:\Users\<USER>\AppData\Local\Temp\jb.station.Onesmus.sock)
2025-09-23 09:30:56,112 [   6235]   WARN - #c.i.s.c.i.StationSocketConnectionLoop - Discovery connection failed with AnnotatedConnectException(ConnectException) (Connection refused: connect: C:\Users\<USER>\AppData\Local\Temp\jb.station.Onesmus.sock)
JCEF_V(30:56:563): Found cef_server binary 'C:\Program Files\JetBrains\IntelliJ IDEA Community Edition 2025.2.2\jbr\bin\cef_server.exe' via System.getProperty('java.home')=C:\Program Files\JetBrains\IntelliJ IDEA Community Edition 2025.2.2\jbr
JCEF_V(30:56:564): Java is started via native launcher. Found cef_server path C:\Program Files\JetBrains\IntelliJ IDEA Community Edition 2025.2.2\jbr\bin\cef_server.exe (via system propety)
2025-09-23 09:30:56,991 [   7114]   WARN - #c.i.u.i.FileBasedIndexImpl - Suppressed a frequent exception logged for the 2nd time: null
2025-09-23 09:30:57,523 [   7646]   WARN - #com.augmentcode.intellij.sentry.SentryMetadataCollector - Git ls-files command failed with exit code: 128
2025-09-23 09:30:58,147 [   8270]   WARN - #c.i.e.wsl - Failed to get WSL mount root for Ubuntu: DOS working directory is expected, but got \\wsl.localhost\Ubuntu\home\oni\audio-transcriber
JCEF_I(30:59:574): Found free tcp-port 6188 for server.
JCEF_I(30:59:574): Found free tcp-port 6189 for java-handlers.
JCEF_V(30:59:593): Created CefServer instance. Transport port=6188 (backward port=6189). Params:
[--disable-features=SpareRendererForSitePerProcess, --disable-gpu-process-crash-limit, --disable-component-update, --autoplay-policy=no-user-gesture-required]| log_file=C:\Users\<USER>\AppData\Local\JetBrains\IdeaIC2025.2\log\jcef_28448.log, log_severity=LOGSEVERITY_DISABLE
2025-09-23 09:31:03,369 [  13492]   WARN - #c.i.u.j.JBCefBrowserBuilder - Trying to create windowed browser when remote-mode is enabled. Settings isOffScreenRendering=false will be ignored.
2025-09-23 09:31:04,919 [  15042]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_ERROR]: Failed to restore state SyntaxError: Unexpected end of JSON input [http://augment.localhost/main-panel.html:17]
2025-09-23 09:31:05,239 [  15362]   WARN - #org.intellij.plugins.markdown.ui.preview.jcef.impl.JcefBrowserPipeImpl - No subscribers for documentReady!
Attached data: 
2025-09-23 09:31:06,409 [  16532]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: The PerformanceObserver does not support buffered flag with the entryTypes argument. [http://augment.localhost/assets/initialize-DVX7iRDU.js:15]
2025-09-23 09:31:06,560 [  16683]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_ERROR]: Host not initialized [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:31:06,646 [  16769]   WARN - #com.augmentcode.intellij.sidecar.SidecarService - Using first project base directory as workspace root from all candidates: [file:////wsl.localhost/Ubuntu/home/<USER>/audio-transcriber]
2025-09-23 09:31:06,649 [  16772]   WARN - #com.augmentcode.intellij.webviews.AbstractWebviewMessagingService - Failed to parse webview message as protobuf.
Exception: Missing type url when parsing: {"type":"get-remote-agent-notification-enabled-request","data":{"agentIds":[]}}
Message: {"type":"async-wrapper","requestId":"17675b96-d853-4269-95ff-3e55e875f11f","error":null,"baseMsg":{"type":"get-remote-agent-notification-enabled-request","data":{"agentIds":[]}},"destination":"host"}
2025-09-23 09:31:06,649 [  16772]   WARN - #com.augmentcode.intellij.webviews.AbstractWebviewMessagingService - Failed to parse webview message as protobuf.
Exception: Missing type url when parsing: {"type":"get-remote-agent-pinned-status-request","data":{}}
Message: {"type":"async-wrapper","requestId":"48279a48-6ee1-48e7-b8f1-37b7dacdf2cd","error":null,"baseMsg":{"type":"get-remote-agent-pinned-status-request","data":{}},"destination":"host"}
2025-09-23 09:31:06,650 [  16773]   WARN - #com.augmentcode.intellij.webviews.AbstractWebviewMessagingService - Failed to parse webview message as protobuf.
Exception: Missing type url when parsing: {"type":"get-remote-agent-status"}
Message: {"type":"async-wrapper","requestId":"513d7d88-3d8f-469e-b1a1-5848af38f1bf","error":null,"baseMsg":{"type":"get-remote-agent-status"},"destination":"host"}
2025-09-23 09:31:06,663 [  16786]   WARN - #com.augmentcode.intellij.sidecar.SidecarService - Using first project base directory as workspace root from all candidates: [file:////wsl.localhost/Ubuntu/home/<USER>/audio-transcriber]
2025-09-23 09:31:06,722 [  16845]   WARN - #com.augmentcode.intellij.webviews.AbstractWebviewMessagingService - Failed to parse webview message as protobuf.
Exception: Missing type url when parsing: {"type":"get-orientation-status"}
Message: {"type":"get-orientation-status"}
2025-09-23 09:31:06,759 [  16882]   WARN - #com.augmentcode.intellij.webviews.AbstractWebviewMessagingService - Failed to parse webview message as protobuf.
Exception: Missing type url when parsing: {"type":"update-shared-webview-state","data":{"agentOverviews":[],"activeWebviews":[],"pinnedAgents":{}},"id":"remoteAgentStore"}
Message: {"type":"update-shared-webview-state","data":{"agentOverviews":[],"activeWebviews":[],"pinnedAgents":{}},"id":"remoteAgentStore"}
2025-09-23 09:31:06,780 [  16903]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_ERROR]: Failed to get pinned agents from store: Error: Unable to parse webview message: {"type":"async-wrapper","requestId":"48279a48-6ee1-48e7-b8f1-37b7dacdf2cd","error":null,"baseMsg":{"type":"get-remote-agent-pinned-status-request","data":{}},"destination":"host"} [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:31:06,781 [  16904]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_ERROR]: Uncaught (in promise) Error: Unable to parse webview message: {"type":"async-wrapper","requestId":"17675b96-d853-4269-95ff-3e55e875f11f","error":null,"baseMsg":{"type":"get-remote-agent-notification-enabled-request","data":{"agentIds":[]}},"destination":"host"} [http://augment.localhost/assets/GuardedIcon-CE9Pu5ez.js:7]
2025-09-23 09:31:06,781 [  16904]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_ERROR]: Uncaught (in promise) Error: Unable to parse webview message: {"type":"async-wrapper","requestId":"513d7d88-3d8f-469e-b1a1-5848af38f1bf","error":null,"baseMsg":{"type":"get-remote-agent-status"},"destination":"host"} [http://augment.localhost/assets/GuardedIcon-CE9Pu5ez.js:7]
2025-09-23 09:31:07,031 [  17154]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: IntersectionObserverManager: disabled [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:31:13,070 [  23193]   WARN - #c.i.e.wsl - Failed to get WSL mount root for Ubuntu: DOS working directory is expected, but got \\wsl.localhost\Ubuntu\home\oni\audio-transcriber
2025-09-23 09:31:23,121 [  33244]   WARN - #c.i.e.wsl - Failed to get WSL mount root for Ubuntu: DOS working directory is expected, but got \\wsl.localhost\Ubuntu\home\oni\audio-transcriber
2025-09-23 09:31:30,559 [  40682]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_ERROR]: ResizeObserver loop completed with undelivered notifications. [http://augment.localhost/main-panel.html:0]
2025-09-23 09:31:32,571 [  42694]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: IntersectionObserverManager: disabled [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:31:34,631 [  44754]   WARN - #com.augmentcode.intellij.chat.ChatWebviewMessageClient$Companion - Workspace guidelines are empty, not including in chat request. 
2025-09-23 09:31:36,019 [  46142]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: No exchange with this request ID found. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:31:36,083 [  46206]   WARN - #com.augmentcode.intellij.chat.ChatWebviewMessageClient$Companion - Workspace guidelines are empty, not including in chat request. 
2025-09-23 09:31:39,269 [  49392]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: No exchange with this request ID found. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:31:42,599 [  52722]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 3160a1d4e3f0ccc1fa8b2779ffe879b3616b82386c8ed6e958b30b7046cbc3a3 -> d04d206b96ab69c473234440e5cd420d9d9ef834642b88aae161c4862bcf5e6f
2025-09-23 09:31:44,189 [  54312]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: IntersectionDisplay context not found.  Call setIntersectionContext first. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:31:45,881 [  56004]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: IntersectionDisplay context not found.  Call setIntersectionContext first. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:31:48,390 [  58513]   WARN - #com.augmentcode.intellij.chat.ChatWebviewMessageClient$Companion - Workspace guidelines are empty, not including in chat request. 
2025-09-23 09:31:49,076 [  59199]   WARN - #kotlinx.coroutines.CoroutineScope - Sending initial guidelines state on EDT from dumb service runWhenSmart
2025-09-23 09:31:51,441 [  61564]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: No exchange with this request ID found. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:31:52,787 [  62910]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: IntersectionDisplay context not found.  Call setIntersectionContext first. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:31:52,821 [  62944]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: IntersectionDisplay context not found.  Call setIntersectionContext first. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:31:52,863 [  62986]   WARN - #com.augmentcode.intellij.chat.ChatWebviewMessageClient$Companion - Workspace guidelines are empty, not including in chat request. 
2025-09-23 09:31:56,858 [  66981]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: No exchange with this request ID found. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:31:57,719 [  67842]   WARN - #com.augmentcode.intellij.sentry.SentryMetadataCollector - Git ls-files command failed with exit code: 128
2025-09-23 09:32:07,252 [  77375]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: IntersectionDisplay context not found.  Call setIntersectionContext first. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:32:07,483 [  77606]   WARN - #com.augmentcode.intellij.chat.ChatWebviewMessageClient$Companion - Workspace guidelines are empty, not including in chat request. 
2025-09-23 09:32:11,827 [  81950]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: No exchange with this request ID found. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:32:13,486 [  83609]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: IntersectionDisplay context not found.  Call setIntersectionContext first. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:32:13,591 [  83714]   WARN - #com.augmentcode.intellij.chat.ChatWebviewMessageClient$Companion - Workspace guidelines are empty, not including in chat request. 
2025-09-23 09:32:17,312 [  87435]   WARN - #com.augmentcode.intellij.api.AugmentHttpClient$Companion - Failed to call record-session-events (4ebd07bc-a46b-4e21-bb14-82f9dd2e2175): 400 Bad Request
2025-09-23 09:32:17,313 [  87436]   WARN - #com.augmentcode.intellij.api.AugmentAPI$Companion - Failed to record session events: 400 Bad Request
2025-09-23 09:32:18,292 [  88415]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: No exchange with this request ID found. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:32:20,711 [  90834]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: IntersectionDisplay context not found.  Call setIntersectionContext first. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:32:21,004 [  91127]   WARN - #com.augmentcode.intellij.webviews.AbstractWebviewMessagingService - Failed to parse webview message as protobuf.
Exception: Missing type url when parsing: {"type":"can-show-terminal","data":{"command":"npm install express multer cors dotenv"}}
Message: {"type":"async-wrapper","requestId":"8b1e52ea-bf8e-48be-9534-23bad1f9fab0","error":null,"baseMsg":{"type":"can-show-terminal","data":{"command":"npm install express multer cors dotenv"}},"destination":"host"}
2025-09-23 09:32:21,008 [  91131]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_ERROR]: Failed to check if terminal can be shown: Error: Unable to parse webview message: {"type":"async-wrapper","requestId":"8b1e52ea-bf8e-48be-9534-23bad1f9fab0","error":null,"baseMsg":{"type":"can-show-terminal","data":{"command":"npm install express multer cors dotenv"}},"destination":"host"} [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:32:27,745 [  97868]   WARN - #c.i.e.wsl - Failed to get WSL mount root for Ubuntu: DOS working directory is expected, but got \\wsl.localhost\Ubuntu\home\oni\audio-transcriber
2025-09-23 09:32:47,702 [ 117825]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Non of the blobs were indexed, backing off and retrying...
2025-09-23 09:32:57,891 [ 128014]   WARN - #com.augmentcode.intellij.sentry.SentryMetadataCollector - Git ls-files command failed with exit code: 128
2025-09-23 09:32:59,962 [ 130085]   WARN - #com.augmentcode.intellij.webviews.AbstractWebviewMessagingService - Failed to parse webview message as protobuf.
Exception: Missing type url when parsing: {"type":"can-show-terminal","data":{"command":"npm install express multer cors dotenv"}}
Message: {"type":"async-wrapper","requestId":"0e0abbec-bf6f-4363-95d2-1a7b73e876a3","error":null,"baseMsg":{"type":"can-show-terminal","data":{"command":"npm install express multer cors dotenv"}},"destination":"host"}
2025-09-23 09:32:59,982 [ 130105]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_ERROR]: Failed to check if terminal can be shown: Error: Unable to parse webview message: {"type":"async-wrapper","requestId":"0e0abbec-bf6f-4363-95d2-1a7b73e876a3","error":null,"baseMsg":{"type":"can-show-terminal","data":{"command":"npm install express multer cors dotenv"}},"destination":"host"} [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:33:00,058 [ 130181] SEVERE - #c.i.o.progress - This method is forbidden on EDT because it does not pump the event queue. Switch to a BGT, or use com.intellij.openapi.progress.TasksKt.runWithModalProgressBlocking. 
java.lang.IllegalStateException: This method is forbidden on EDT because it does not pump the event queue. Switch to a BGT, or use com.intellij.openapi.progress.TasksKt.runWithModalProgressBlocking. 
	at com.intellij.openapi.progress.CoroutinesKt.assertBackgroundThreadAndNoWriteAction(coroutines.kt:577)
	at com.intellij.openapi.progress.CoroutinesKt.runBlockingCancellable(coroutines.kt:130)
	at com.intellij.openapi.progress.CoroutinesKt.runBlockingMaybeCancellable(coroutines.kt:180)
	at com.intellij.platform.eel.provider.EelProviderUtil.toEelApiBlocking(EelProvider.kt:94)
	at com.intellij.sh.run.ShConfigurationType.trivialDefaultShellDetection(ShConfigurationType.java:66)
	at com.intellij.sh.run.ShConfigurationType.getDefaultShell(ShConfigurationType.java:61)
	at com.intellij.sh.run.ShRunConfigurationProfileState.createCommandLineForScript(ShRunConfigurationProfileState.java:97)
	at com.intellij.sh.run.ShRunConfigurationProfileState.buildExecutionResult(ShRunConfigurationProfileState.java:73)
	at com.intellij.sh.run.ShRunConfigurationProfileState.execute(ShRunConfigurationProfileState.java:59)
	at com.intellij.sh.run.ShRunProgramRunner.lambda$execute$0(ShRunProgramRunner.java:33)
	at com.intellij.execution.ExecutionManager.startRunProfile$lambda$0(ExecutionManager.kt:75)
	at com.intellij.execution.impl.ExecutionManagerImpl.startRunProfile$lambda$6(ExecutionManagerImpl.kt:229)
	at com.intellij.execution.impl.ExecutionManagerImpl.doStartRunProfile$lambda$15(ExecutionManagerImpl.kt:296)
	at com.intellij.openapi.application.TransactionGuardImpl.runWithWritingAllowed(TransactionGuardImpl.java:240)
	at com.intellij.openapi.application.TransactionGuardImpl.access$100(TransactionGuardImpl.java:26)
	at com.intellij.openapi.application.TransactionGuardImpl$1.run(TransactionGuardImpl.java:202)
	at com.intellij.openapi.application.impl.AppImplKt$runnableUnitFunction$1.invoke(appImpl.kt:104)
	at com.intellij.openapi.application.impl.AppImplKt$runnableUnitFunction$1.invoke(appImpl.kt:104)
	at com.intellij.platform.locking.impl.NestedLocksThreadingSupport.doRunWriteIntentReadAction(NestedLocksThreadingSupport.kt:666)
	at com.intellij.platform.locking.impl.NestedLocksThreadingSupport.runPreventiveWriteIntentReadAction(NestedLocksThreadingSupport.kt:640)
	at com.intellij.platform.locking.impl.NestedLocksThreadingSupport.runWriteIntentReadAction(NestedLocksThreadingSupport.kt:633)
	at com.intellij.openapi.application.impl.ApplicationImpl.runIntendedWriteActionOnCurrentThread(ApplicationImpl.java:1022)
	at com.intellij.openapi.application.impl.ApplicationImpl$6.run(ApplicationImpl.java:574)
	at com.intellij.util.concurrency.ChildContext$runInChildContext$1.invoke(propagation.kt:167)
	at com.intellij.util.concurrency.ChildContext$runInChildContext$1.invoke(propagation.kt:167)
	at com.intellij.util.concurrency.ChildContext.runInChildContext(propagation.kt:173)
	at com.intellij.util.concurrency.ChildContext.runInChildContext(propagation.kt:167)
	at com.intellij.util.concurrency.ContextRunnable.run(ContextRunnable.java:27)
	at com.intellij.openapi.application.impl.FlushQueue.runNextEvent(FlushQueue.java:122)
	at com.intellij.openapi.application.impl.FlushQueue.flushNow(FlushQueue.java:43)
	at java.desktop/java.awt.event.InvocationEvent.dispatch(InvocationEvent.java:318)
	at java.desktop/java.awt.EventQueue.dispatchEventImpl(EventQueue.java:781)
	at java.desktop/java.awt.EventQueue$4.run(EventQueue.java:728)
	at java.desktop/java.awt.EventQueue$4.run(EventQueue.java:722)
	at java.base/java.security.AccessController.doPrivileged(AccessController.java:400)
	at java.base/java.security.ProtectionDomain$JavaSecurityAccessImpl.doIntersectionPrivilege(ProtectionDomain.java:87)
	at java.desktop/java.awt.EventQueue.dispatchEvent(EventQueue.java:750)
	at com.intellij.ide.IdeEventQueue.defaultDispatchEvent(IdeEventQueue.kt:595)
	at com.intellij.ide.IdeEventQueue._dispatchEvent(IdeEventQueue.kt:488)
	at com.intellij.ide.IdeEventQueue.dispatchEvent$lambda$12$lambda$11$lambda$10$lambda$9(IdeEventQueue.kt:313)
	at com.intellij.openapi.progress.impl.CoreProgressManager.computePrioritized(CoreProgressManager.java:865)
	at com.intellij.ide.IdeEventQueue.dispatchEvent$lambda$12$lambda$11$lambda$10(IdeEventQueue.kt:312)
	at com.intellij.ide.IdeEventQueueKt.performActivity$lambda$3(IdeEventQueue.kt:974)
	at com.intellij.openapi.application.TransactionGuardImpl.performActivity(TransactionGuardImpl.java:110)
	at com.intellij.ide.IdeEventQueueKt.performActivity(IdeEventQueue.kt:974)
	at com.intellij.ide.IdeEventQueue.dispatchEvent$lambda$12(IdeEventQueue.kt:307)
	at com.intellij.ide.IdeEventQueue.dispatchEvent(IdeEventQueue.kt:347)
	at java.desktop/java.awt.EventDispatchThread.pumpOneEventForFilters(EventDispatchThread.java:207)
	at java.desktop/java.awt.EventDispatchThread.pumpEventsForFilter(EventDispatchThread.java:128)
	at java.desktop/java.awt.EventDispatchThread.pumpEventsForHierarchy(EventDispatchThread.java:117)
	at java.desktop/java.awt.EventDispatchThread.pumpEvents(EventDispatchThread.java:113)
	at java.desktop/java.awt.EventDispatchThread.pumpEvents(EventDispatchThread.java:105)
	at java.desktop/java.awt.EventDispatchThread.run(EventDispatchThread.java:92)
2025-09-23 09:33:00,059 [ 130182] SEVERE - #c.i.o.progress - IntelliJ IDEA 2025.2.2  Build #IC-252.26199.169
2025-09-23 09:33:00,059 [ 130182] SEVERE - #c.i.o.progress - JDK: 21.0.8; VM: OpenJDK 64-Bit Server VM; Vendor: JetBrains s.r.o.
2025-09-23 09:33:00,059 [ 130182] SEVERE - #c.i.o.progress - OS: Windows 11
2025-09-23 09:33:00,059 [ 130182] SEVERE - #c.i.o.progress - Plugin to blame: Shell Script version: 252.26199.169
2025-09-23 09:33:00,061 [ 130184] SEVERE - #c.i.o.progress - This method is forbidden on EDT because it does not pump the event queue. Switch to a BGT, or use com.intellij.openapi.progress.TasksKt.runWithModalProgressBlocking. 
java.lang.IllegalStateException: This method is forbidden on EDT because it does not pump the event queue. Switch to a BGT, or use com.intellij.openapi.progress.TasksKt.runWithModalProgressBlocking. 
	at com.intellij.openapi.progress.CoroutinesKt.assertBackgroundThreadAndNoWriteAction(coroutines.kt:577)
	at com.intellij.openapi.progress.CoroutinesKt.runBlockingCancellable(coroutines.kt:130)
	at com.intellij.openapi.progress.CoroutinesKt.runBlockingMaybeCancellable(coroutines.kt:180)
	at com.intellij.platform.eel.provider.utils.EelUtilsKt.fetchLoginShellEnvVariablesBlocking(eelUtils.kt:20)
	at com.intellij.sh.run.ShConfigurationType.trivialDefaultShellDetection(ShConfigurationType.java:67)
	at com.intellij.sh.run.ShConfigurationType.getDefaultShell(ShConfigurationType.java:61)
	at com.intellij.sh.run.ShRunConfigurationProfileState.createCommandLineForScript(ShRunConfigurationProfileState.java:97)
	at com.intellij.sh.run.ShRunConfigurationProfileState.buildExecutionResult(ShRunConfigurationProfileState.java:73)
	at com.intellij.sh.run.ShRunConfigurationProfileState.execute(ShRunConfigurationProfileState.java:59)
	at com.intellij.sh.run.ShRunProgramRunner.lambda$execute$0(ShRunProgramRunner.java:33)
	at com.intellij.execution.ExecutionManager.startRunProfile$lambda$0(ExecutionManager.kt:75)
	at com.intellij.execution.impl.ExecutionManagerImpl.startRunProfile$lambda$6(ExecutionManagerImpl.kt:229)
	at com.intellij.execution.impl.ExecutionManagerImpl.doStartRunProfile$lambda$15(ExecutionManagerImpl.kt:296)
	at com.intellij.openapi.application.TransactionGuardImpl.runWithWritingAllowed(TransactionGuardImpl.java:240)
	at com.intellij.openapi.application.TransactionGuardImpl.access$100(TransactionGuardImpl.java:26)
	at com.intellij.openapi.application.TransactionGuardImpl$1.run(TransactionGuardImpl.java:202)
	at com.intellij.openapi.application.impl.AppImplKt$runnableUnitFunction$1.invoke(appImpl.kt:104)
	at com.intellij.openapi.application.impl.AppImplKt$runnableUnitFunction$1.invoke(appImpl.kt:104)
	at com.intellij.platform.locking.impl.NestedLocksThreadingSupport.doRunWriteIntentReadAction(NestedLocksThreadingSupport.kt:666)
	at com.intellij.platform.locking.impl.NestedLocksThreadingSupport.runPreventiveWriteIntentReadAction(NestedLocksThreadingSupport.kt:640)
	at com.intellij.platform.locking.impl.NestedLocksThreadingSupport.runWriteIntentReadAction(NestedLocksThreadingSupport.kt:633)
	at com.intellij.openapi.application.impl.ApplicationImpl.runIntendedWriteActionOnCurrentThread(ApplicationImpl.java:1022)
	at com.intellij.openapi.application.impl.ApplicationImpl$6.run(ApplicationImpl.java:574)
	at com.intellij.util.concurrency.ChildContext$runInChildContext$1.invoke(propagation.kt:167)
	at com.intellij.util.concurrency.ChildContext$runInChildContext$1.invoke(propagation.kt:167)
	at com.intellij.util.concurrency.ChildContext.runInChildContext(propagation.kt:173)
	at com.intellij.util.concurrency.ChildContext.runInChildContext(propagation.kt:167)
	at com.intellij.util.concurrency.ContextRunnable.run(ContextRunnable.java:27)
	at com.intellij.openapi.application.impl.FlushQueue.runNextEvent(FlushQueue.java:122)
	at com.intellij.openapi.application.impl.FlushQueue.flushNow(FlushQueue.java:43)
	at java.desktop/java.awt.event.InvocationEvent.dispatch(InvocationEvent.java:318)
	at java.desktop/java.awt.EventQueue.dispatchEventImpl(EventQueue.java:781)
	at java.desktop/java.awt.EventQueue$4.run(EventQueue.java:728)
	at java.desktop/java.awt.EventQueue$4.run(EventQueue.java:722)
	at java.base/java.security.AccessController.doPrivileged(AccessController.java:400)
	at java.base/java.security.ProtectionDomain$JavaSecurityAccessImpl.doIntersectionPrivilege(ProtectionDomain.java:87)
	at java.desktop/java.awt.EventQueue.dispatchEvent(EventQueue.java:750)
	at com.intellij.ide.IdeEventQueue.defaultDispatchEvent(IdeEventQueue.kt:595)
	at com.intellij.ide.IdeEventQueue._dispatchEvent(IdeEventQueue.kt:488)
	at com.intellij.ide.IdeEventQueue.dispatchEvent$lambda$12$lambda$11$lambda$10$lambda$9(IdeEventQueue.kt:313)
	at com.intellij.openapi.progress.impl.CoreProgressManager.computePrioritized(CoreProgressManager.java:865)
	at com.intellij.ide.IdeEventQueue.dispatchEvent$lambda$12$lambda$11$lambda$10(IdeEventQueue.kt:312)
	at com.intellij.ide.IdeEventQueueKt.performActivity$lambda$3(IdeEventQueue.kt:974)
	at com.intellij.openapi.application.TransactionGuardImpl.performActivity(TransactionGuardImpl.java:110)
	at com.intellij.ide.IdeEventQueueKt.performActivity(IdeEventQueue.kt:974)
	at com.intellij.ide.IdeEventQueue.dispatchEvent$lambda$12(IdeEventQueue.kt:307)
	at com.intellij.ide.IdeEventQueue.dispatchEvent(IdeEventQueue.kt:347)
	at java.desktop/java.awt.EventDispatchThread.pumpOneEventForFilters(EventDispatchThread.java:207)
	at java.desktop/java.awt.EventDispatchThread.pumpEventsForFilter(EventDispatchThread.java:128)
	at java.desktop/java.awt.EventDispatchThread.pumpEventsForHierarchy(EventDispatchThread.java:117)
	at java.desktop/java.awt.EventDispatchThread.pumpEvents(EventDispatchThread.java:113)
	at java.desktop/java.awt.EventDispatchThread.pumpEvents(EventDispatchThread.java:105)
	at java.desktop/java.awt.EventDispatchThread.run(EventDispatchThread.java:92)
2025-09-23 09:33:00,062 [ 130185] SEVERE - #c.i.o.progress - IntelliJ IDEA 2025.2.2  Build #IC-252.26199.169
2025-09-23 09:33:00,062 [ 130185] SEVERE - #c.i.o.progress - JDK: 21.0.8; VM: OpenJDK 64-Bit Server VM; Vendor: JetBrains s.r.o.
2025-09-23 09:33:00,064 [ 130187] SEVERE - #c.i.o.progress - OS: Windows 11
2025-09-23 09:33:00,064 [ 130187] SEVERE - #c.i.o.progress - Plugin to blame: Shell Script version: 252.26199.169
2025-09-23 09:33:00,068 [ 130191] SEVERE - #c.i.o.progress - This method is forbidden on EDT because it does not pump the event queue. Switch to a BGT, or use com.intellij.openapi.progress.TasksKt.runWithModalProgressBlocking. 
java.lang.IllegalStateException: This method is forbidden on EDT because it does not pump the event queue. Switch to a BGT, or use com.intellij.openapi.progress.TasksKt.runWithModalProgressBlocking. 
	at com.intellij.openapi.progress.CoroutinesKt.assertBackgroundThreadAndNoWriteAction(coroutines.kt:577)
	at com.intellij.openapi.progress.CoroutinesKt.runBlockingCancellable(coroutines.kt:130)
	at com.intellij.openapi.progress.CoroutinesKt.runBlockingMaybeCancellable(coroutines.kt:180)
	at com.intellij.platform.eel.provider.EelProviderUtil.toEelApiBlocking(EelProvider.kt:94)
	at com.intellij.execution.configurations.GeneralCommandLine.tryGetEel(GeneralCommandLine.java:493)
	at com.intellij.execution.configurations.GeneralCommandLine.startProcess(GeneralCommandLine.java:429)
	at com.intellij.execution.configurations.GeneralCommandLine.createProcess(GeneralCommandLine.java:402)
	at com.intellij.execution.process.OSProcessHandler.startProcess(OSProcessHandler.java:85)
	at com.intellij.execution.process.OSProcessHandler.<init>(OSProcessHandler.java:45)
	at com.intellij.execution.process.KillableProcessHandler.<init>(KillableProcessHandler.java:40)
	at com.intellij.sh.run.ShRunConfigurationProfileState$1.<init>(ShRunConfigurationProfileState.java:83)
	at com.intellij.sh.run.ShRunConfigurationProfileState.createProcessHandler(ShRunConfigurationProfileState.java:83)
	at com.intellij.sh.run.ShRunConfigurationProfileState.buildExecutionResult(ShRunConfigurationProfileState.java:75)
	at com.intellij.sh.run.ShRunConfigurationProfileState.execute(ShRunConfigurationProfileState.java:59)
	at com.intellij.sh.run.ShRunProgramRunner.lambda$execute$0(ShRunProgramRunner.java:33)
	at com.intellij.execution.ExecutionManager.startRunProfile$lambda$0(ExecutionManager.kt:75)
	at com.intellij.execution.impl.ExecutionManagerImpl.startRunProfile$lambda$6(ExecutionManagerImpl.kt:229)
	at com.intellij.execution.impl.ExecutionManagerImpl.doStartRunProfile$lambda$15(ExecutionManagerImpl.kt:296)
	at com.intellij.openapi.application.TransactionGuardImpl.runWithWritingAllowed(TransactionGuardImpl.java:240)
	at com.intellij.openapi.application.TransactionGuardImpl.access$100(TransactionGuardImpl.java:26)
	at com.intellij.openapi.application.TransactionGuardImpl$1.run(TransactionGuardImpl.java:202)
	at com.intellij.openapi.application.impl.AppImplKt$runnableUnitFunction$1.invoke(appImpl.kt:104)
	at com.intellij.openapi.application.impl.AppImplKt$runnableUnitFunction$1.invoke(appImpl.kt:104)
	at com.intellij.platform.locking.impl.NestedLocksThreadingSupport.doRunWriteIntentReadAction(NestedLocksThreadingSupport.kt:666)
	at com.intellij.platform.locking.impl.NestedLocksThreadingSupport.runPreventiveWriteIntentReadAction(NestedLocksThreadingSupport.kt:640)
	at com.intellij.platform.locking.impl.NestedLocksThreadingSupport.runWriteIntentReadAction(NestedLocksThreadingSupport.kt:633)
	at com.intellij.openapi.application.impl.ApplicationImpl.runIntendedWriteActionOnCurrentThread(ApplicationImpl.java:1022)
	at com.intellij.openapi.application.impl.ApplicationImpl$6.run(ApplicationImpl.java:574)
	at com.intellij.util.concurrency.ChildContext$runInChildContext$1.invoke(propagation.kt:167)
	at com.intellij.util.concurrency.ChildContext$runInChildContext$1.invoke(propagation.kt:167)
	at com.intellij.util.concurrency.ChildContext.runInChildContext(propagation.kt:173)
	at com.intellij.util.concurrency.ChildContext.runInChildContext(propagation.kt:167)
	at com.intellij.util.concurrency.ContextRunnable.run(ContextRunnable.java:27)
	at com.intellij.openapi.application.impl.FlushQueue.runNextEvent(FlushQueue.java:122)
	at com.intellij.openapi.application.impl.FlushQueue.flushNow(FlushQueue.java:43)
	at java.desktop/java.awt.event.InvocationEvent.dispatch(InvocationEvent.java:318)
	at java.desktop/java.awt.EventQueue.dispatchEventImpl(EventQueue.java:781)
	at java.desktop/java.awt.EventQueue$4.run(EventQueue.java:728)
	at java.desktop/java.awt.EventQueue$4.run(EventQueue.java:722)
	at java.base/java.security.AccessController.doPrivileged(AccessController.java:400)
	at java.base/java.security.ProtectionDomain$JavaSecurityAccessImpl.doIntersectionPrivilege(ProtectionDomain.java:87)
	at java.desktop/java.awt.EventQueue.dispatchEvent(EventQueue.java:750)
	at com.intellij.ide.IdeEventQueue.defaultDispatchEvent(IdeEventQueue.kt:595)
	at com.intellij.ide.IdeEventQueue._dispatchEvent(IdeEventQueue.kt:488)
	at com.intellij.ide.IdeEventQueue.dispatchEvent$lambda$12$lambda$11$lambda$10$lambda$9(IdeEventQueue.kt:313)
	at com.intellij.openapi.progress.impl.CoreProgressManager.computePrioritized(CoreProgressManager.java:865)
	at com.intellij.ide.IdeEventQueue.dispatchEvent$lambda$12$lambda$11$lambda$10(IdeEventQueue.kt:312)
	at com.intellij.ide.IdeEventQueueKt.performActivity$lambda$3(IdeEventQueue.kt:974)
	at com.intellij.openapi.application.TransactionGuardImpl.performActivity(TransactionGuardImpl.java:110)
	at com.intellij.ide.IdeEventQueueKt.performActivity(IdeEventQueue.kt:974)
	at com.intellij.ide.IdeEventQueue.dispatchEvent$lambda$12(IdeEventQueue.kt:307)
	at com.intellij.ide.IdeEventQueue.dispatchEvent(IdeEventQueue.kt:347)
	at java.desktop/java.awt.EventDispatchThread.pumpOneEventForFilters(EventDispatchThread.java:207)
	at java.desktop/java.awt.EventDispatchThread.pumpEventsForFilter(EventDispatchThread.java:128)
	at java.desktop/java.awt.EventDispatchThread.pumpEventsForHierarchy(EventDispatchThread.java:117)
	at java.desktop/java.awt.EventDispatchThread.pumpEvents(EventDispatchThread.java:113)
	at java.desktop/java.awt.EventDispatchThread.pumpEvents(EventDispatchThread.java:105)
	at java.desktop/java.awt.EventDispatchThread.run(EventDispatchThread.java:92)
2025-09-23 09:33:00,069 [ 130192] SEVERE - #c.i.o.progress - IntelliJ IDEA 2025.2.2  Build #IC-252.26199.169
2025-09-23 09:33:00,069 [ 130192] SEVERE - #c.i.o.progress - JDK: 21.0.8; VM: OpenJDK 64-Bit Server VM; Vendor: JetBrains s.r.o.
2025-09-23 09:33:00,069 [ 130192] SEVERE - #c.i.o.progress - OS: Windows 11
2025-09-23 09:33:00,069 [ 130192] SEVERE - #c.i.o.progress - Plugin to blame: Shell Script version: 252.26199.169
2025-09-23 09:33:00,071 [ 130194] SEVERE - #c.i.o.progress - This method is forbidden on EDT because it does not pump the event queue. Switch to a BGT, or use com.intellij.openapi.progress.TasksKt.runWithModalProgressBlocking. 
java.lang.IllegalStateException: This method is forbidden on EDT because it does not pump the event queue. Switch to a BGT, or use com.intellij.openapi.progress.TasksKt.runWithModalProgressBlocking. 
	at com.intellij.openapi.progress.CoroutinesKt.assertBackgroundThreadAndNoWriteAction(coroutines.kt:577)
	at com.intellij.openapi.progress.CoroutinesKt.runBlockingCancellable(coroutines.kt:130)
	at com.intellij.openapi.progress.CoroutinesKt.runBlockingMaybeCancellable(coroutines.kt:180)
	at com.intellij.execution.util.ExecUtil.startProcessBlockingUsingEel(ExecUtil.kt:217)
	at com.intellij.execution.configurations.GeneralCommandLine.createProcess(GeneralCommandLine.java:584)
	at com.intellij.execution.configurations.PtyCommandLine.createProcess(PtyCommandLine.java:123)
	at com.intellij.execution.configurations.GeneralCommandLine.startProcess(GeneralCommandLine.java:590)
	at com.intellij.execution.configurations.GeneralCommandLine.startProcess(GeneralCommandLine.java:432)
	at com.intellij.execution.configurations.GeneralCommandLine.createProcess(GeneralCommandLine.java:402)
	at com.intellij.execution.process.OSProcessHandler.startProcess(OSProcessHandler.java:85)
	at com.intellij.execution.process.OSProcessHandler.<init>(OSProcessHandler.java:45)
	at com.intellij.execution.process.KillableProcessHandler.<init>(KillableProcessHandler.java:40)
	at com.intellij.sh.run.ShRunConfigurationProfileState$1.<init>(ShRunConfigurationProfileState.java:83)
	at com.intellij.sh.run.ShRunConfigurationProfileState.createProcessHandler(ShRunConfigurationProfileState.java:83)
	at com.intellij.sh.run.ShRunConfigurationProfileState.buildExecutionResult(ShRunConfigurationProfileState.java:75)
	at com.intellij.sh.run.ShRunConfigurationProfileState.execute(ShRunConfigurationProfileState.java:59)
	at com.intellij.sh.run.ShRunProgramRunner.lambda$execute$0(ShRunProgramRunner.java:33)
	at com.intellij.execution.ExecutionManager.startRunProfile$lambda$0(ExecutionManager.kt:75)
	at com.intellij.execution.impl.ExecutionManagerImpl.startRunProfile$lambda$6(ExecutionManagerImpl.kt:229)
	at com.intellij.execution.impl.ExecutionManagerImpl.doStartRunProfile$lambda$15(ExecutionManagerImpl.kt:296)
	at com.intellij.openapi.application.TransactionGuardImpl.runWithWritingAllowed(TransactionGuardImpl.java:240)
	at com.intellij.openapi.application.TransactionGuardImpl.access$100(TransactionGuardImpl.java:26)
	at com.intellij.openapi.application.TransactionGuardImpl$1.run(TransactionGuardImpl.java:202)
	at com.intellij.openapi.application.impl.AppImplKt$runnableUnitFunction$1.invoke(appImpl.kt:104)
	at com.intellij.openapi.application.impl.AppImplKt$runnableUnitFunction$1.invoke(appImpl.kt:104)
	at com.intellij.platform.locking.impl.NestedLocksThreadingSupport.doRunWriteIntentReadAction(NestedLocksThreadingSupport.kt:666)
	at com.intellij.platform.locking.impl.NestedLocksThreadingSupport.runPreventiveWriteIntentReadAction(NestedLocksThreadingSupport.kt:640)
	at com.intellij.platform.locking.impl.NestedLocksThreadingSupport.runWriteIntentReadAction(NestedLocksThreadingSupport.kt:633)
	at com.intellij.openapi.application.impl.ApplicationImpl.runIntendedWriteActionOnCurrentThread(ApplicationImpl.java:1022)
	at com.intellij.openapi.application.impl.ApplicationImpl$6.run(ApplicationImpl.java:574)
	at com.intellij.util.concurrency.ChildContext$runInChildContext$1.invoke(propagation.kt:167)
	at com.intellij.util.concurrency.ChildContext$runInChildContext$1.invoke(propagation.kt:167)
	at com.intellij.util.concurrency.ChildContext.runInChildContext(propagation.kt:173)
	at com.intellij.util.concurrency.ChildContext.runInChildContext(propagation.kt:167)
	at com.intellij.util.concurrency.ContextRunnable.run(ContextRunnable.java:27)
	at com.intellij.openapi.application.impl.FlushQueue.runNextEvent(FlushQueue.java:122)
	at com.intellij.openapi.application.impl.FlushQueue.flushNow(FlushQueue.java:43)
	at java.desktop/java.awt.event.InvocationEvent.dispatch(InvocationEvent.java:318)
	at java.desktop/java.awt.EventQueue.dispatchEventImpl(EventQueue.java:781)
	at java.desktop/java.awt.EventQueue$4.run(EventQueue.java:728)
	at java.desktop/java.awt.EventQueue$4.run(EventQueue.java:722)
	at java.base/java.security.AccessController.doPrivileged(AccessController.java:400)
	at java.base/java.security.ProtectionDomain$JavaSecurityAccessImpl.doIntersectionPrivilege(ProtectionDomain.java:87)
	at java.desktop/java.awt.EventQueue.dispatchEvent(EventQueue.java:750)
	at com.intellij.ide.IdeEventQueue.defaultDispatchEvent(IdeEventQueue.kt:595)
	at com.intellij.ide.IdeEventQueue._dispatchEvent(IdeEventQueue.kt:488)
	at com.intellij.ide.IdeEventQueue.dispatchEvent$lambda$12$lambda$11$lambda$10$lambda$9(IdeEventQueue.kt:313)
	at com.intellij.openapi.progress.impl.CoreProgressManager.computePrioritized(CoreProgressManager.java:865)
	at com.intellij.ide.IdeEventQueue.dispatchEvent$lambda$12$lambda$11$lambda$10(IdeEventQueue.kt:312)
	at com.intellij.ide.IdeEventQueueKt.performActivity$lambda$3(IdeEventQueue.kt:974)
	at com.intellij.openapi.application.TransactionGuardImpl.performActivity(TransactionGuardImpl.java:110)
	at com.intellij.ide.IdeEventQueueKt.performActivity(IdeEventQueue.kt:974)
	at com.intellij.ide.IdeEventQueue.dispatchEvent$lambda$12(IdeEventQueue.kt:307)
	at com.intellij.ide.IdeEventQueue.dispatchEvent(IdeEventQueue.kt:347)
	at java.desktop/java.awt.EventDispatchThread.pumpOneEventForFilters(EventDispatchThread.java:207)
	at java.desktop/java.awt.EventDispatchThread.pumpEventsForFilter(EventDispatchThread.java:128)
	at java.desktop/java.awt.EventDispatchThread.pumpEventsForHierarchy(EventDispatchThread.java:117)
	at java.desktop/java.awt.EventDispatchThread.pumpEvents(EventDispatchThread.java:113)
	at java.desktop/java.awt.EventDispatchThread.pumpEvents(EventDispatchThread.java:105)
	at java.desktop/java.awt.EventDispatchThread.run(EventDispatchThread.java:92)
2025-09-23 09:33:00,072 [ 130195] SEVERE - #c.i.o.progress - IntelliJ IDEA 2025.2.2  Build #IC-252.26199.169
2025-09-23 09:33:00,072 [ 130195] SEVERE - #c.i.o.progress - JDK: 21.0.8; VM: OpenJDK 64-Bit Server VM; Vendor: JetBrains s.r.o.
2025-09-23 09:33:00,072 [ 130195] SEVERE - #c.i.o.progress - OS: Windows 11
2025-09-23 09:33:00,072 [ 130195] SEVERE - #c.i.o.progress - Plugin to blame: Shell Script version: 252.26199.169
2025-09-23 09:33:00,077 [ 130200] SEVERE - #c.i.o.progress - This method is forbidden on EDT because it does not pump the event queue. Switch to a BGT, or use com.intellij.openapi.progress.TasksKt.runWithModalProgressBlocking. 
java.lang.IllegalStateException: This method is forbidden on EDT because it does not pump the event queue. Switch to a BGT, or use com.intellij.openapi.progress.TasksKt.runWithModalProgressBlocking. 
	at com.intellij.openapi.progress.CoroutinesKt.assertBackgroundThreadAndNoWriteAction(coroutines.kt:577)
	at com.intellij.openapi.progress.CoroutinesKt.runBlockingCancellable(coroutines.kt:130)
	at com.intellij.openapi.progress.CoroutinesKt.runBlockingMaybeCancellable(coroutines.kt:180)
	at com.intellij.execution.util.ExecUtil.startProcessBlockingUsingEel(ExecUtil.kt:226)
	at com.intellij.execution.configurations.GeneralCommandLine.createProcess(GeneralCommandLine.java:584)
	at com.intellij.execution.configurations.PtyCommandLine.createProcess(PtyCommandLine.java:123)
	at com.intellij.execution.configurations.GeneralCommandLine.startProcess(GeneralCommandLine.java:590)
	at com.intellij.execution.configurations.GeneralCommandLine.startProcess(GeneralCommandLine.java:432)
	at com.intellij.execution.configurations.GeneralCommandLine.createProcess(GeneralCommandLine.java:402)
	at com.intellij.execution.process.OSProcessHandler.startProcess(OSProcessHandler.java:85)
	at com.intellij.execution.process.OSProcessHandler.<init>(OSProcessHandler.java:45)
	at com.intellij.execution.process.KillableProcessHandler.<init>(KillableProcessHandler.java:40)
	at com.intellij.sh.run.ShRunConfigurationProfileState$1.<init>(ShRunConfigurationProfileState.java:83)
	at com.intellij.sh.run.ShRunConfigurationProfileState.createProcessHandler(ShRunConfigurationProfileState.java:83)
	at com.intellij.sh.run.ShRunConfigurationProfileState.buildExecutionResult(ShRunConfigurationProfileState.java:75)
	at com.intellij.sh.run.ShRunConfigurationProfileState.execute(ShRunConfigurationProfileState.java:59)
	at com.intellij.sh.run.ShRunProgramRunner.lambda$execute$0(ShRunProgramRunner.java:33)
	at com.intellij.execution.ExecutionManager.startRunProfile$lambda$0(ExecutionManager.kt:75)
	at com.intellij.execution.impl.ExecutionManagerImpl.startRunProfile$lambda$6(ExecutionManagerImpl.kt:229)
	at com.intellij.execution.impl.ExecutionManagerImpl.doStartRunProfile$lambda$15(ExecutionManagerImpl.kt:296)
	at com.intellij.openapi.application.TransactionGuardImpl.runWithWritingAllowed(TransactionGuardImpl.java:240)
	at com.intellij.openapi.application.TransactionGuardImpl.access$100(TransactionGuardImpl.java:26)
	at com.intellij.openapi.application.TransactionGuardImpl$1.run(TransactionGuardImpl.java:202)
	at com.intellij.openapi.application.impl.AppImplKt$runnableUnitFunction$1.invoke(appImpl.kt:104)
	at com.intellij.openapi.application.impl.AppImplKt$runnableUnitFunction$1.invoke(appImpl.kt:104)
	at com.intellij.platform.locking.impl.NestedLocksThreadingSupport.doRunWriteIntentReadAction(NestedLocksThreadingSupport.kt:666)
	at com.intellij.platform.locking.impl.NestedLocksThreadingSupport.runPreventiveWriteIntentReadAction(NestedLocksThreadingSupport.kt:640)
	at com.intellij.platform.locking.impl.NestedLocksThreadingSupport.runWriteIntentReadAction(NestedLocksThreadingSupport.kt:633)
	at com.intellij.openapi.application.impl.ApplicationImpl.runIntendedWriteActionOnCurrentThread(ApplicationImpl.java:1022)
	at com.intellij.openapi.application.impl.ApplicationImpl$6.run(ApplicationImpl.java:574)
	at com.intellij.util.concurrency.ChildContext$runInChildContext$1.invoke(propagation.kt:167)
	at com.intellij.util.concurrency.ChildContext$runInChildContext$1.invoke(propagation.kt:167)
	at com.intellij.util.concurrency.ChildContext.runInChildContext(propagation.kt:173)
	at com.intellij.util.concurrency.ChildContext.runInChildContext(propagation.kt:167)
	at com.intellij.util.concurrency.ContextRunnable.run(ContextRunnable.java:27)
	at com.intellij.openapi.application.impl.FlushQueue.runNextEvent(FlushQueue.java:122)
	at com.intellij.openapi.application.impl.FlushQueue.flushNow(FlushQueue.java:43)
	at java.desktop/java.awt.event.InvocationEvent.dispatch(InvocationEvent.java:318)
	at java.desktop/java.awt.EventQueue.dispatchEventImpl(EventQueue.java:781)
	at java.desktop/java.awt.EventQueue$4.run(EventQueue.java:728)
	at java.desktop/java.awt.EventQueue$4.run(EventQueue.java:722)
	at java.base/java.security.AccessController.doPrivileged(AccessController.java:400)
	at java.base/java.security.ProtectionDomain$JavaSecurityAccessImpl.doIntersectionPrivilege(ProtectionDomain.java:87)
	at java.desktop/java.awt.EventQueue.dispatchEvent(EventQueue.java:750)
	at com.intellij.ide.IdeEventQueue.defaultDispatchEvent(IdeEventQueue.kt:595)
	at com.intellij.ide.IdeEventQueue._dispatchEvent(IdeEventQueue.kt:488)
	at com.intellij.ide.IdeEventQueue.dispatchEvent$lambda$12$lambda$11$lambda$10$lambda$9(IdeEventQueue.kt:313)
	at com.intellij.openapi.progress.impl.CoreProgressManager.computePrioritized(CoreProgressManager.java:865)
	at com.intellij.ide.IdeEventQueue.dispatchEvent$lambda$12$lambda$11$lambda$10(IdeEventQueue.kt:312)
	at com.intellij.ide.IdeEventQueueKt.performActivity$lambda$3(IdeEventQueue.kt:974)
	at com.intellij.openapi.application.TransactionGuardImpl.performActivity(TransactionGuardImpl.java:110)
	at com.intellij.ide.IdeEventQueueKt.performActivity(IdeEventQueue.kt:974)
	at com.intellij.ide.IdeEventQueue.dispatchEvent$lambda$12(IdeEventQueue.kt:307)
	at com.intellij.ide.IdeEventQueue.dispatchEvent(IdeEventQueue.kt:347)
	at java.desktop/java.awt.EventDispatchThread.pumpOneEventForFilters(EventDispatchThread.java:207)
	at java.desktop/java.awt.EventDispatchThread.pumpEventsForFilter(EventDispatchThread.java:128)
	at java.desktop/java.awt.EventDispatchThread.pumpEventsForHierarchy(EventDispatchThread.java:117)
	at java.desktop/java.awt.EventDispatchThread.pumpEvents(EventDispatchThread.java:113)
	at java.desktop/java.awt.EventDispatchThread.pumpEvents(EventDispatchThread.java:105)
	at java.desktop/java.awt.EventDispatchThread.run(EventDispatchThread.java:92)
2025-09-23 09:33:00,078 [ 130201] SEVERE - #c.i.o.progress - IntelliJ IDEA 2025.2.2  Build #IC-252.26199.169
2025-09-23 09:33:00,078 [ 130201] SEVERE - #c.i.o.progress - JDK: 21.0.8; VM: OpenJDK 64-Bit Server VM; Vendor: JetBrains s.r.o.
2025-09-23 09:33:00,078 [ 130201] SEVERE - #c.i.o.progress - OS: Windows 11
2025-09-23 09:33:00,078 [ 130201] SEVERE - #c.i.o.progress - Plugin to blame: Shell Script version: 252.26199.169
2025-09-23 09:33:00,657 [ 130780]   WARN - #c.i.e.wsl - Failed to get WSL mount root for Ubuntu: DOS working directory is expected, but got \\wsl.localhost\Ubuntu\home\oni\audio-transcriber
2025-09-23 09:33:04,445 [ 134568]   WARN - #c.i.o.v.i.w.W.Ubuntu - table error: collision at 9056 (new /home/<USER>/audio-transcriber/node_modules/.cookie-signature-ZYp1MFv7, existing /home/<USER>/audio-transcriber/node_modules/cookie-signature)
2025-09-23 09:33:04,459 [ 134582]   WARN - #c.i.o.v.i.w.W.Ubuntu - Watcher terminated with exit code 3
2025-09-23 09:33:06,036 [ 136159]   WARN - #com.augmentcode.intellij.chat.ChatWebviewMessageClient$Companion - Workspace guidelines are empty, not including in chat request. 
2025-09-23 09:33:07,290 [ 137413]   WARN - #c.i.e.wsl - Failed to get WSL mount root for Ubuntu: DOS working directory is expected, but got \\wsl.localhost\Ubuntu\home\oni\audio-transcriber
2025-09-23 09:33:09,337 [ 139460]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: No exchange with this request ID found. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:33:10,843 [ 140966]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: IntersectionDisplay context not found.  Call setIntersectionContext first. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:33:11,126 [ 141249]   WARN - #com.augmentcode.intellij.webviews.AbstractWebviewMessagingService - Failed to parse webview message as protobuf.
Exception: Missing type url when parsing: {"type":"can-show-terminal","data":{"command":"npm install -D tailwindcss postcss autoprefixer"}}
Message: {"type":"async-wrapper","requestId":"6be71d03-eff8-4240-81fb-b7d6af51c944","error":null,"baseMsg":{"type":"can-show-terminal","data":{"command":"npm install -D tailwindcss postcss autoprefixer"}},"destination":"host"}
2025-09-23 09:33:11,127 [ 141250]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_ERROR]: Failed to check if terminal can be shown: Error: Unable to parse webview message: {"type":"async-wrapper","requestId":"6be71d03-eff8-4240-81fb-b7d6af51c944","error":null,"baseMsg":{"type":"can-show-terminal","data":{"command":"npm install -D tailwindcss postcss autoprefixer"}},"destination":"host"} [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:33:11,910 [ 142033]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Non of the blobs were indexed, backing off and retrying...
2025-09-23 09:33:12,172 [ 142295]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: f9db5b6c8b6dc41584c8fdb7b7cf0d999425b4ca8ee2bf1db765313ebb1a6da9 -> 69ccdfce759b02458f42eadea0dbc7976ac875c0b1ef5e22c5ca6c300aafa7d9
2025-09-23 09:33:58,033 [ 188156]   WARN - #com.augmentcode.intellij.sentry.SentryMetadataCollector - Git ls-files command failed with exit code: 128
2025-09-23 09:33:58,888 [ 189011]   WARN - #com.augmentcode.intellij.webviews.AbstractWebviewMessagingService - Failed to parse webview message as protobuf.
Exception: Missing type url when parsing: {"type":"can-show-terminal","data":{"command":"npm install -D tailwindcss postcss autoprefixer"}}
Message: {"type":"async-wrapper","requestId":"652a0ad4-569d-4b87-82e7-d2843c6fd99f","error":null,"baseMsg":{"type":"can-show-terminal","data":{"command":"npm install -D tailwindcss postcss autoprefixer"}},"destination":"host"}
2025-09-23 09:33:58,890 [ 189013]   WARN - #c.i.o.progress - Suppressed a frequent exception logged for the 2nd time: This method is forbidden on EDT because it does not pump the event queue. Switch to a BGT, or use com.intellij.openapi.progress.TasksKt.runWithModalProgressBlocking. 
2025-09-23 09:33:58,890 [ 189013]   WARN - #c.i.o.progress - Suppressed a frequent exception logged for the 2nd time: This method is forbidden on EDT because it does not pump the event queue. Switch to a BGT, or use com.intellij.openapi.progress.TasksKt.runWithModalProgressBlocking. 
2025-09-23 09:33:58,892 [ 189015]   WARN - #c.i.o.progress - Suppressed a frequent exception logged for the 2nd time: This method is forbidden on EDT because it does not pump the event queue. Switch to a BGT, or use com.intellij.openapi.progress.TasksKt.runWithModalProgressBlocking. 
2025-09-23 09:33:58,892 [ 189015]   WARN - #c.i.o.progress - Suppressed a frequent exception logged for the 2nd time: This method is forbidden on EDT because it does not pump the event queue. Switch to a BGT, or use com.intellij.openapi.progress.TasksKt.runWithModalProgressBlocking. 
2025-09-23 09:33:58,893 [ 189016]   WARN - #c.i.o.progress - Suppressed a frequent exception logged for the 2nd time: This method is forbidden on EDT because it does not pump the event queue. Switch to a BGT, or use com.intellij.openapi.progress.TasksKt.runWithModalProgressBlocking. 
2025-09-23 09:33:58,898 [ 189021]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_ERROR]: Failed to check if terminal can be shown: Error: Unable to parse webview message: {"type":"async-wrapper","requestId":"652a0ad4-569d-4b87-82e7-d2843c6fd99f","error":null,"baseMsg":{"type":"can-show-terminal","data":{"command":"npm install -D tailwindcss postcss autoprefixer"}},"destination":"host"} [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:33:59,455 [ 189578]   WARN - #c.i.e.wsl - Failed to get WSL mount root for Ubuntu: DOS working directory is expected, but got \\wsl.localhost\Ubuntu\home\oni\audio-transcriber
2025-09-23 09:34:04,498 [ 194621]   WARN - #com.augmentcode.intellij.chat.ChatWebviewMessageClient$Companion - Workspace guidelines are empty, not including in chat request. 
2025-09-23 09:34:05,461 [ 195584]   WARN - #c.i.e.wsl - Failed to get WSL mount root for Ubuntu: DOS working directory is expected, but got \\wsl.localhost\Ubuntu\home\oni\audio-transcriber
2025-09-23 09:34:07,721 [ 197844]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: No exchange with this request ID found. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:34:09,349 [ 199472]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: IntersectionDisplay context not found.  Call setIntersectionContext first. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:34:09,580 [ 199703]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 70ebac7484969feb28f4841fe9e3340bf759d81e9eeab0bf40d8a5901fbfc921 -> 26307c9cafb85f37dfb7b8a661544ff67c5f286944de5867ec4819689fe4070a
2025-09-23 09:34:09,624 [ 199747]   WARN - #com.augmentcode.intellij.webviews.AbstractWebviewMessagingService - Failed to parse webview message as protobuf.
Exception: Missing type url when parsing: {"type":"can-show-terminal","data":{"command":"npx tailwindcss init -p"}}
Message: {"type":"async-wrapper","requestId":"f526ca18-bd90-4871-8ecc-bb122aa3fbc8","error":null,"baseMsg":{"type":"can-show-terminal","data":{"command":"npx tailwindcss init -p"}},"destination":"host"}
2025-09-23 09:34:09,625 [ 199748]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_ERROR]: Failed to check if terminal can be shown: Error: Unable to parse webview message: {"type":"async-wrapper","requestId":"f526ca18-bd90-4871-8ecc-bb122aa3fbc8","error":null,"baseMsg":{"type":"can-show-terminal","data":{"command":"npx tailwindcss init -p"}},"destination":"host"} [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:34:11,610 [ 201733]   WARN - #com.augmentcode.intellij.webviews.AbstractWebviewMessagingService - Failed to parse webview message as protobuf.
Exception: Missing type url when parsing: {"type":"can-show-terminal","data":{"command":"npx tailwindcss init -p"}}
Message: {"type":"async-wrapper","requestId":"438cebca-64c1-49c5-b21b-f6f1c2eeaa19","error":null,"baseMsg":{"type":"can-show-terminal","data":{"command":"npx tailwindcss init -p"}},"destination":"host"}
2025-09-23 09:34:11,619 [ 201742]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_ERROR]: Failed to check if terminal can be shown: Error: Unable to parse webview message: {"type":"async-wrapper","requestId":"438cebca-64c1-49c5-b21b-f6f1c2eeaa19","error":null,"baseMsg":{"type":"can-show-terminal","data":{"command":"npx tailwindcss init -p"}},"destination":"host"} [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:34:12,571 [ 202694]   WARN - #com.augmentcode.intellij.chat.ChatWebviewMessageClient$Companion - Workspace guidelines are empty, not including in chat request. 
2025-09-23 09:34:13,352 [ 203475]   WARN - #c.i.e.wsl - Failed to get WSL mount root for Ubuntu: DOS working directory is expected, but got \\wsl.localhost\Ubuntu\home\oni\audio-transcriber
2025-09-23 09:34:15,646 [ 205769]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: No exchange with this request ID found. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:34:16,470 [ 206593]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Non of the blobs were indexed, backing off and retrying...
2025-09-23 09:34:16,873 [ 206996]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: IntersectionDisplay context not found.  Call setIntersectionContext first. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:34:17,160 [ 207283]   WARN - #com.augmentcode.intellij.webviews.AbstractWebviewMessagingService - Failed to parse webview message as protobuf.
Exception: Missing type url when parsing: {"type":"can-show-terminal","data":{"command":"./node_modules/.bin/tailwindcss init -p"}}
Message: {"type":"async-wrapper","requestId":"7851f1c5-1c91-4381-9724-90b94ab6b6d7","error":null,"baseMsg":{"type":"can-show-terminal","data":{"command":"./node_modules/.bin/tailwindcss init -p"}},"destination":"host"}
2025-09-23 09:34:17,162 [ 207285]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_ERROR]: Failed to check if terminal can be shown: Error: Unable to parse webview message: {"type":"async-wrapper","requestId":"7851f1c5-1c91-4381-9724-90b94ab6b6d7","error":null,"baseMsg":{"type":"can-show-terminal","data":{"command":"./node_modules/.bin/tailwindcss init -p"}},"destination":"host"} [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:34:18,650 [ 208773]   WARN - #com.augmentcode.intellij.webviews.AbstractWebviewMessagingService - Failed to parse webview message as protobuf.
Exception: Missing type url when parsing: {"type":"can-show-terminal","data":{"command":"./node_modules/.bin/tailwindcss init -p"}}
Message: {"type":"async-wrapper","requestId":"56bbc764-163d-452c-96e7-65f04fa8cf71","error":null,"baseMsg":{"type":"can-show-terminal","data":{"command":"./node_modules/.bin/tailwindcss init -p"}},"destination":"host"}
2025-09-23 09:34:18,661 [ 208784]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_ERROR]: Failed to check if terminal can be shown: Error: Unable to parse webview message: {"type":"async-wrapper","requestId":"56bbc764-163d-452c-96e7-65f04fa8cf71","error":null,"baseMsg":{"type":"can-show-terminal","data":{"command":"./node_modules/.bin/tailwindcss init -p"}},"destination":"host"} [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:34:18,728 [ 208851]   WARN - #com.augmentcode.intellij.chat.ChatWebviewMessageClient$Companion - Workspace guidelines are empty, not including in chat request. 
2025-09-23 09:34:19,334 [ 209457]   WARN - #c.i.e.wsl - Failed to get WSL mount root for Ubuntu: DOS working directory is expected, but got \\wsl.localhost\Ubuntu\home\oni\audio-transcriber
2025-09-23 09:34:22,408 [ 212531]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: No exchange with this request ID found. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:34:23,567 [ 213690]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: IntersectionDisplay context not found.  Call setIntersectionContext first. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:34:23,616 [ 213739]   WARN - #com.augmentcode.intellij.chat.ChatWebviewMessageClient$Companion - Workspace guidelines are empty, not including in chat request. 
2025-09-23 09:34:26,339 [ 216462]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: No exchange with this request ID found. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:34:30,630 [ 220753]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: IntersectionDisplay context not found.  Call setIntersectionContext first. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:34:31,840 [ 221963]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: IntersectionDisplay context not found.  Call setIntersectionContext first. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:34:33,310 [ 223433]   WARN - #com.augmentcode.intellij.chat.ChatWebviewMessageClient$Companion - Workspace guidelines are empty, not including in chat request. 
2025-09-23 09:34:36,525 [ 226648]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: No exchange with this request ID found. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:34:39,372 [ 229495]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: IntersectionDisplay context not found.  Call setIntersectionContext first. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:34:39,658 [ 229781]   WARN - #com.augmentcode.intellij.webviews.AbstractWebviewMessagingService - Failed to parse webview message as protobuf.
Exception: Missing type url when parsing: {"type":"can-show-terminal","data":{"command":"mkdir -p server"}}
Message: {"type":"async-wrapper","requestId":"eb7ac5fb-a33a-4706-b096-65172e86e3b6","error":null,"baseMsg":{"type":"can-show-terminal","data":{"command":"mkdir -p server"}},"destination":"host"}
2025-09-23 09:34:39,660 [ 229783]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_ERROR]: Failed to check if terminal can be shown: Error: Unable to parse webview message: {"type":"async-wrapper","requestId":"eb7ac5fb-a33a-4706-b096-65172e86e3b6","error":null,"baseMsg":{"type":"can-show-terminal","data":{"command":"mkdir -p server"}},"destination":"host"} [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:34:58,174 [ 248297]   WARN - #com.augmentcode.intellij.sentry.SentryMetadataCollector - Git ls-files command failed with exit code: 128
2025-09-23 09:35:07,294 [ 257417]   WARN - #com.augmentcode.intellij.api.AugmentHttpClient$Companion - Failed to call client-metrics (eabf5b36-fc8b-4b56-a546-3920912a0b4f)
io.ktor.client.plugins.HttpRequestTimeoutException: Request timeout has expired [url=https://i1.api.augmentcode.com/client-metrics, request_timeout=60000 ms]
	at io.ktor.client.plugins.HttpTimeout$Plugin$install$1$1$killer$1.invokeSuspend(HttpTimeout.kt:165)
	at kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)
	at kotlinx.coroutines.DispatchedTask.run(DispatchedTask.kt:100)
	at kotlinx.coroutines.internal.SoftLimitedDispatcher$Worker.run(SoftLimitedDispatcher.kt:130)
	at kotlinx.coroutines.scheduling.TaskImpl.run(Tasks.kt:89)
	at kotlinx.coroutines.scheduling.CoroutineScheduler.runSafely(CoroutineScheduler.kt:613)
	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.executeTask(CoroutineScheduler.kt:1183)
	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.runWorker(CoroutineScheduler.kt:778)
	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.run(CoroutineScheduler.kt:765)
2025-09-23 09:35:07,294 [ 257417]   WARN - #com.augmentcode.intellij.metrics.MetricsReporter - Failed to upload 1 metrics
java.lang.IllegalStateException: Failed to make network call to client-metrics with request ID eabf5b36-fc8b-4b56-a546-3920912a0b4f: Request timeout has expired [url=https://i1.api.augmentcode.com/client-metrics, request_timeout=60000 ms]
	at com.augmentcode.intellij.api.AugmentHttpClient.wrapNetworkError(AugmentHttpClient.kt:223)
	at com.augmentcode.intellij.api.AugmentHttpClient.access$wrapNetworkError(AugmentHttpClient.kt:31)
	at com.augmentcode.intellij.api.AugmentHttpClient$post$2.invokeSuspend(AugmentHttpClient.kt:180)
	at kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)
	at kotlinx.coroutines.DispatchedTask.run(DispatchedTask.kt:98)
	at kotlinx.coroutines.scheduling.CoroutineScheduler.runSafely(CoroutineScheduler.kt:613)
	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.executeTask(CoroutineScheduler.kt:1189)
	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.runWorker(CoroutineScheduler.kt:778)
	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.run(CoroutineScheduler.kt:765)
Caused by: io.ktor.client.plugins.HttpRequestTimeoutException: Request timeout has expired [url=https://i1.api.augmentcode.com/client-metrics, request_timeout=60000 ms]
	at io.ktor.client.plugins.HttpTimeout$Plugin$install$1$1$killer$1.invokeSuspend(HttpTimeout.kt:165)
	at kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)
	at kotlinx.coroutines.DispatchedTask.run(DispatchedTask.kt:100)
	at kotlinx.coroutines.internal.SoftLimitedDispatcher$Worker.run(SoftLimitedDispatcher.kt:130)
	at kotlinx.coroutines.scheduling.TaskImpl.run(Tasks.kt:89)
	at kotlinx.coroutines.scheduling.CoroutineScheduler.runSafely(CoroutineScheduler.kt:613)
	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.executeTask(CoroutineScheduler.kt:1183)
	... 2 more
2025-09-23 09:35:28,285 [ 278408]   WARN - #com.augmentcode.intellij.webviews.AbstractWebviewMessagingService - Failed to parse webview message as protobuf.
Exception: Missing type url when parsing: {"type":"can-show-terminal","data":{"command":"mkdir -p server"}}
Message: {"type":"async-wrapper","requestId":"1cff6e48-675e-486f-8f2f-e1082f23e71a","error":null,"baseMsg":{"type":"can-show-terminal","data":{"command":"mkdir -p server"}},"destination":"host"}
2025-09-23 09:35:28,288 [ 278411]   WARN - #c.i.o.progress - Suppressed a frequent exception logged for the 5th time: This method is forbidden on EDT because it does not pump the event queue. Switch to a BGT, or use com.intellij.openapi.progress.TasksKt.runWithModalProgressBlocking. 
2025-09-23 09:35:28,288 [ 278411]   WARN - #c.i.o.progress - Suppressed a frequent exception logged for the 5th time: This method is forbidden on EDT because it does not pump the event queue. Switch to a BGT, or use com.intellij.openapi.progress.TasksKt.runWithModalProgressBlocking. 
2025-09-23 09:35:28,289 [ 278412]   WARN - #c.i.o.progress - Suppressed a frequent exception logged for the 5th time: This method is forbidden on EDT because it does not pump the event queue. Switch to a BGT, or use com.intellij.openapi.progress.TasksKt.runWithModalProgressBlocking. 
2025-09-23 09:35:28,290 [ 278413]   WARN - #c.i.o.progress - Suppressed a frequent exception logged for the 5th time: This method is forbidden on EDT because it does not pump the event queue. Switch to a BGT, or use com.intellij.openapi.progress.TasksKt.runWithModalProgressBlocking. 
2025-09-23 09:35:28,290 [ 278413]   WARN - #c.i.o.progress - Suppressed a frequent exception logged for the 5th time: This method is forbidden on EDT because it does not pump the event queue. Switch to a BGT, or use com.intellij.openapi.progress.TasksKt.runWithModalProgressBlocking. 
2025-09-23 09:35:28,305 [ 278428]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_ERROR]: Failed to check if terminal can be shown: Error: Unable to parse webview message: {"type":"async-wrapper","requestId":"1cff6e48-675e-486f-8f2f-e1082f23e71a","error":null,"baseMsg":{"type":"can-show-terminal","data":{"command":"mkdir -p server"}},"destination":"host"} [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:35:28,327 [ 278450]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_ERROR]: _validateAndCheckTool called while another tool is active [object Object] [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:35:28,353 [ 278476]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: IntersectionDisplay context not found.  Call setIntersectionContext first. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:35:28,390 [ 278513]   WARN - #c.i.e.wsl - Failed to get WSL mount root for Ubuntu: DOS working directory is expected, but got \\wsl.localhost\Ubuntu\home\oni\audio-transcriber
2025-09-23 09:35:28,515 [ 278638]   WARN - #com.augmentcode.intellij.chat.ChatWebviewMessageClient$Companion - Workspace guidelines are empty, not including in chat request. 
2025-09-23 09:35:33,249 [ 283372]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: No exchange with this request ID found. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:35:46,503 [ 296626]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: IntersectionDisplay context not found.  Call setIntersectionContext first. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:35:46,633 [ 296756]   WARN - #com.augmentcode.intellij.chat.ChatWebviewMessageClient$Companion - Workspace guidelines are empty, not including in chat request. 
2025-09-23 09:35:50,611 [ 300734]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: No exchange with this request ID found. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:35:54,173 [ 304296]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Non of the blobs were indexed, backing off and retrying...
2025-09-23 09:35:56,116 [ 306239]   WARN - #c.i.u.i.p.ProjectIndexableFilesFilterHealthCheck - Following files are indexable but they were NOT found in filter. Errors count: 1. Examples:
file id=198693 path=//wsl.localhost/Ubuntu/home/<USER>/audio-transcriber/node_modules/react-scripts/node_modules/tailwindcss/peers/index.js
2025-09-23 09:35:58,325 [ 308448]   WARN - #com.augmentcode.intellij.sentry.SentryMetadataCollector - Git ls-files command failed with exit code: 128
2025-09-23 09:36:00,825 [ 310948]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: IntersectionDisplay context not found.  Call setIntersectionContext first. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:36:00,950 [ 311073]   WARN - #com.augmentcode.intellij.chat.ChatWebviewMessageClient$Companion - Workspace guidelines are empty, not including in chat request. 
2025-09-23 09:36:04,842 [ 314965]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: No exchange with this request ID found. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:36:06,472 [ 316595]   WARN - #com.augmentcode.intellij.api.AugmentHttpClient$Companion - Failed to call record-session-events (bf79660d-9598-4dc4-bb04-1e5e5f18c9f8): 400 Bad Request
2025-09-23 09:36:06,472 [ 316595]   WARN - #com.augmentcode.intellij.api.AugmentAPI$Companion - Failed to record session events: 400 Bad Request
2025-09-23 09:36:10,917 [ 321040]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: IntersectionDisplay context not found.  Call setIntersectionContext first. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:36:19,173 [ 329296]   WARN - #c.i.e.wsl - Failed to get WSL mount root for Ubuntu: DOS working directory is expected, but got \\wsl.localhost\Ubuntu\home\oni\audio-transcriber
2025-09-23 09:36:20,196 [ 330319]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: Loading changes  for 77f85e56-27c4-49cf-bd22-07899593832e retry 0:  Error: No changes found [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:36:20,198 [ 330321]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: Loading changes  for 77f85e56-27c4-49cf-bd22-07899593832e retry 1:  Error: No changes found [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:36:20,208 [ 330331]   WARN - #com.augmentcode.intellij.chat.ChatWebviewMessageClient$Companion - Workspace guidelines are empty, not including in chat request. 
2025-09-23 09:36:20,700 [ 330823]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: Loading changes  for 77f85e56-27c4-49cf-bd22-07899593832e retry 2:  Error: No changes found [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:36:20,702 [ 330825]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: Loading changes  for 77f85e56-27c4-49cf-bd22-07899593832e retry 3:  Error: No changes found [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:36:20,702 [ 330825]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: Loading changes failed for 77f85e56-27c4-49cf-bd22-07899593832e:  Error: No changes found [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:36:21,206 [ 331329]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: Loading changes  for 77f85e56-27c4-49cf-bd22-07899593832e retry 4:  Error: No changes found [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:36:21,206 [ 331329]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: Loading changes failed for 77f85e56-27c4-49cf-bd22-07899593832e:  Error: No changes found [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:36:23,358 [ 333481]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: No exchange with this request ID found. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:36:26,979 [ 337102]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Non of the blobs were indexed, backing off and retrying...
2025-09-23 09:36:35,082 [ 345205]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: IntersectionDisplay context not found.  Call setIntersectionContext first. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:36:36,130 [ 346253]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: Loading changes  for cc4fc4c0-65ec-4b0b-a94a-6a9f0b8f6458 retry 0:  Error: No changes found [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:36:36,138 [ 346261]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: Loading changes  for cc4fc4c0-65ec-4b0b-a94a-6a9f0b8f6458 retry 1:  Error: No changes found [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:36:36,149 [ 346272]   WARN - #com.augmentcode.intellij.chat.ChatWebviewMessageClient$Companion - Workspace guidelines are empty, not including in chat request. 
2025-09-23 09:36:36,634 [ 346757]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: Loading changes  for cc4fc4c0-65ec-4b0b-a94a-6a9f0b8f6458 retry 2:  Error: No changes found [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:36:36,641 [ 346764]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: Loading changes  for cc4fc4c0-65ec-4b0b-a94a-6a9f0b8f6458 retry 3:  Error: No changes found [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:36:36,641 [ 346764]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: Loading changes failed for cc4fc4c0-65ec-4b0b-a94a-6a9f0b8f6458:  Error: No changes found [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:36:37,138 [ 347261]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: Loading changes  for cc4fc4c0-65ec-4b0b-a94a-6a9f0b8f6458 retry 4:  Error: No changes found [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:36:37,138 [ 347261]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: Loading changes failed for cc4fc4c0-65ec-4b0b-a94a-6a9f0b8f6458:  Error: No changes found [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:36:39,191 [ 349314]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: No exchange with this request ID found. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:36:46,347 [ 356470]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Non of the blobs were indexed, backing off and retrying...
2025-09-23 09:36:50,348 [ 360471]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: IntersectionDisplay context not found.  Call setIntersectionContext first. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:36:50,441 [ 360564]   WARN - #c.i.e.wsl - Failed to get WSL mount root for Ubuntu: DOS working directory is expected, but got \\wsl.localhost\Ubuntu\home\oni\audio-transcriber
2025-09-23 09:36:51,398 [ 361521]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: Loading changes  for c5af8158-e25d-4b08-bfc3-feb5703d90b5 retry 0:  Error: No changes found [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:36:51,405 [ 361528]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: Loading changes  for c5af8158-e25d-4b08-bfc3-feb5703d90b5 retry 1:  Error: No changes found [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:36:51,414 [ 361537]   WARN - #com.augmentcode.intellij.chat.ChatWebviewMessageClient$Companion - Workspace guidelines are empty, not including in chat request. 
2025-09-23 09:36:51,902 [ 362025]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: Loading changes  for c5af8158-e25d-4b08-bfc3-feb5703d90b5 retry 2:  Error: No changes found [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:36:51,907 [ 362030]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: Loading changes  for c5af8158-e25d-4b08-bfc3-feb5703d90b5 retry 3:  Error: No changes found [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:36:51,907 [ 362030]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: Loading changes failed for c5af8158-e25d-4b08-bfc3-feb5703d90b5:  Error: No changes found [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:36:52,406 [ 362529]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: Loading changes  for c5af8158-e25d-4b08-bfc3-feb5703d90b5 retry 4:  Error: No changes found [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:36:52,406 [ 362529]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: Loading changes failed for c5af8158-e25d-4b08-bfc3-feb5703d90b5:  Error: No changes found [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:36:54,545 [ 364668]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: No exchange with this request ID found. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:36:56,792 [ 366915]   WARN - #c.i.e.wsl - Failed to get WSL mount root for Ubuntu: DOS working directory is expected, but got \\wsl.localhost\Ubuntu\home\oni\audio-transcriber
2025-09-23 09:36:57,766 [ 367889]   WARN - #c.i.e.wsl - Failed to get WSL mount root for Ubuntu: DOS working directory is expected, but got \\wsl.localhost\Ubuntu\home\oni\audio-transcriber
2025-09-23 09:36:58,501 [ 368624]   WARN - #com.augmentcode.intellij.sentry.SentryMetadataCollector - Git ls-files command failed with exit code: 128
2025-09-23 09:37:04,213 [ 374336]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Non of the blobs were indexed, backing off and retrying...
2025-09-23 09:37:04,574 [ 374697]   WARN - #c.i.e.wsl - Failed to get WSL mount root for Ubuntu: DOS working directory is expected, but got \\wsl.localhost\Ubuntu\home\oni\audio-transcriber
2025-09-23 09:37:07,115 [ 377238]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: IntersectionDisplay context not found.  Call setIntersectionContext first. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:37:08,175 [ 378298]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: Loading changes  for b5c02dcb-37b0-4b0a-98d7-d2bbc889b707 retry 0:  Error: No changes found [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:37:08,180 [ 378303]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: Loading changes  for b5c02dcb-37b0-4b0a-98d7-d2bbc889b707 retry 1:  Error: No changes found [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:37:08,184 [ 378307]   WARN - #com.augmentcode.intellij.chat.ChatWebviewMessageClient$Companion - Workspace guidelines are empty, not including in chat request. 
2025-09-23 09:37:08,680 [ 378803]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: Loading changes  for b5c02dcb-37b0-4b0a-98d7-d2bbc889b707 retry 2:  Error: No changes found [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:37:08,683 [ 378806]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: Loading changes  for b5c02dcb-37b0-4b0a-98d7-d2bbc889b707 retry 3:  Error: No changes found [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:37:08,683 [ 378806]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: Loading changes failed for b5c02dcb-37b0-4b0a-98d7-d2bbc889b707:  Error: No changes found [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:37:09,148 [ 379271]   WARN - #c.i.e.wsl - Failed to get WSL mount root for Ubuntu: DOS working directory is expected, but got \\wsl.localhost\Ubuntu\home\oni\audio-transcriber
2025-09-23 09:37:09,185 [ 379308]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: Loading changes  for b5c02dcb-37b0-4b0a-98d7-d2bbc889b707 retry 4:  Error: No changes found [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:37:09,185 [ 379308]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: Loading changes failed for b5c02dcb-37b0-4b0a-98d7-d2bbc889b707:  Error: No changes found [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:37:12,432 [ 382555]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: No exchange with this request ID found. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:37:22,461 [ 392584]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: IntersectionDisplay context not found.  Call setIntersectionContext first. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:37:23,519 [ 393642]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: Loading changes  for 6d3d8589-c9aa-44af-a6b9-82e6f0069194 retry 0:  Error: No changes found [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:37:23,520 [ 393643]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: Loading changes  for 6d3d8589-c9aa-44af-a6b9-82e6f0069194 retry 1:  Error: No changes found [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:37:23,533 [ 393656]   WARN - #com.augmentcode.intellij.chat.ChatWebviewMessageClient$Companion - Workspace guidelines are empty, not including in chat request. 
2025-09-23 09:37:24,023 [ 394146]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: Loading changes  for 6d3d8589-c9aa-44af-a6b9-82e6f0069194 retry 2:  Error: No changes found [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:37:24,024 [ 394147]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: Loading changes  for 6d3d8589-c9aa-44af-a6b9-82e6f0069194 retry 3:  Error: No changes found [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:37:24,024 [ 394147]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: Loading changes failed for 6d3d8589-c9aa-44af-a6b9-82e6f0069194:  Error: No changes found [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:37:24,528 [ 394651]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: Loading changes  for 6d3d8589-c9aa-44af-a6b9-82e6f0069194 retry 4:  Error: No changes found [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:37:24,528 [ 394651]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: Loading changes failed for 6d3d8589-c9aa-44af-a6b9-82e6f0069194:  Error: No changes found [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:37:27,765 [ 397888]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: No exchange with this request ID found. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:37:30,486 [ 400609]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: IntersectionDisplay context not found.  Call setIntersectionContext first. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:37:31,549 [ 401672]   WARN - #com.augmentcode.intellij.chat.ChatWebviewMessageClient$Companion - Workspace guidelines are empty, not including in chat request. 
2025-09-23 09:37:34,625 [ 404748]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: No exchange with this request ID found. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:37:38,519 [ 408642]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: IntersectionDisplay context not found.  Call setIntersectionContext first. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:37:39,597 [ 409720]   WARN - #com.augmentcode.intellij.chat.ChatWebviewMessageClient$Companion - Workspace guidelines are empty, not including in chat request. 
2025-09-23 09:37:42,607 [ 412730]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: No exchange with this request ID found. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:37:44,782 [ 414905]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: IntersectionDisplay context not found.  Call setIntersectionContext first. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:37:58,658 [ 428781]   WARN - #com.augmentcode.intellij.sentry.SentryMetadataCollector - Git ls-files command failed with exit code: 128
2025-09-23 09:38:21,144 [ 451267]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Non of the blobs were indexed, backing off and retrying...
2025-09-23 09:38:28,086 [ 458209]   WARN - #com.augmentcode.intellij.webviews.AbstractWebviewMessagingService - Failed to parse webview message as protobuf.
Exception: Missing type url when parsing: {"type":"can-show-terminal","data":{"command":"npm install -D concurrently"}}
Message: {"type":"async-wrapper","requestId":"d117fd1c-78c3-4407-8c02-75bfd4476dc8","error":null,"baseMsg":{"type":"can-show-terminal","data":{"command":"npm install -D concurrently"}},"destination":"host"}
2025-09-23 09:38:28,088 [ 458211]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_ERROR]: Failed to check if terminal can be shown: Error: Unable to parse webview message: {"type":"async-wrapper","requestId":"d117fd1c-78c3-4407-8c02-75bfd4476dc8","error":null,"baseMsg":{"type":"can-show-terminal","data":{"command":"npm install -D concurrently"}},"destination":"host"} [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:38:30,256 [ 460379]   WARN - #com.augmentcode.intellij.webviews.AbstractWebviewMessagingService - Failed to parse webview message as protobuf.
Exception: Missing type url when parsing: {"type":"can-show-terminal","data":{"command":"npm install -D concurrently"}}
Message: {"type":"async-wrapper","requestId":"7e8b16af-641a-4aef-9ae4-65048161a1bb","error":null,"baseMsg":{"type":"can-show-terminal","data":{"command":"npm install -D concurrently"}},"destination":"host"}
2025-09-23 09:38:30,277 [ 460400]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_ERROR]: Failed to check if terminal can be shown: Error: Unable to parse webview message: {"type":"async-wrapper","requestId":"7e8b16af-641a-4aef-9ae4-65048161a1bb","error":null,"baseMsg":{"type":"can-show-terminal","data":{"command":"npm install -D concurrently"}},"destination":"host"} [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:38:32,644 [ 462767]   WARN - #c.i.e.wsl - Failed to get WSL mount root for Ubuntu: DOS working directory is expected, but got \\wsl.localhost\Ubuntu\home\oni\audio-transcriber
2025-09-23 09:38:37,295 [ 467418]   WARN - #com.augmentcode.intellij.chat.ChatWebviewMessageClient$Companion - Workspace guidelines are empty, not including in chat request. 
2025-09-23 09:38:40,199 [ 470322]   WARN - #c.i.e.wsl - Failed to get WSL mount root for Ubuntu: DOS working directory is expected, but got \\wsl.localhost\Ubuntu\home\oni\audio-transcriber
2025-09-23 09:38:40,773 [ 470896]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: No exchange with this request ID found. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:38:47,234 [ 477357]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: IntersectionDisplay context not found.  Call setIntersectionContext first. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:38:48,374 [ 478497]   WARN - #com.augmentcode.intellij.chat.ChatWebviewMessageClient$Companion - Workspace guidelines are empty, not including in chat request. 
2025-09-23 09:38:51,791 [ 481914]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: No exchange with this request ID found. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:38:55,931 [ 486054]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Non of the blobs were indexed, backing off and retrying...
2025-09-23 09:38:57,713 [ 487836]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: IntersectionDisplay context not found.  Call setIntersectionContext first. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:38:58,801 [ 488924]   WARN - #com.augmentcode.intellij.chat.ChatWebviewMessageClient$Companion - Workspace guidelines are empty, not including in chat request. 
2025-09-23 09:38:58,804 [ 488927]   WARN - #com.augmentcode.intellij.sentry.SentryMetadataCollector - Git ls-files command failed with exit code: 128
2025-09-23 09:39:01,847 [ 491970]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: No exchange with this request ID found. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:39:09,681 [ 499804]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: IntersectionDisplay context not found.  Call setIntersectionContext first. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:39:09,799 [ 499922]   WARN - #com.augmentcode.intellij.chat.ChatWebviewMessageClient$Companion - Workspace guidelines are empty, not including in chat request. 
2025-09-23 09:39:13,629 [ 503752]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: No exchange with this request ID found. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:39:15,357 [ 505480]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: IntersectionDisplay context not found.  Call setIntersectionContext first. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:39:16,432 [ 506555]   WARN - #com.augmentcode.intellij.api.AugmentHttpClient$Companion - Failed to call record-session-events (fa3be8ff-2f5e-4091-8f97-a330e357e4bc): 400 Bad Request
2025-09-23 09:39:16,433 [ 506556]   WARN - #com.augmentcode.intellij.api.AugmentAPI$Companion - Failed to record session events: 400 Bad Request
2025-09-23 09:39:18,236 [ 508359]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Non of the blobs were indexed, backing off and retrying...
2025-09-23 09:39:19,018 [ 509141]   WARN - #c.i.e.wsl - Failed to get WSL mount root for Ubuntu: DOS working directory is expected, but got \\wsl.localhost\Ubuntu\home\oni\audio-transcriber
2025-09-23 09:39:19,422 [ 509545]   WARN - #com.augmentcode.intellij.webviews.AbstractWebviewMessagingService - Failed to parse webview message as protobuf.
Exception: Missing type url when parsing: {"type":"can-show-terminal","data":{"command":"npm run server"}}
Message: {"type":"async-wrapper","requestId":"ee9af1d4-f914-4a60-ac25-1ab14dd2e717","error":null,"baseMsg":{"type":"can-show-terminal","data":{"command":"npm run server"}},"destination":"host"}
2025-09-23 09:39:19,423 [ 509546]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_ERROR]: Failed to check if terminal can be shown: Error: Unable to parse webview message: {"type":"async-wrapper","requestId":"ee9af1d4-f914-4a60-ac25-1ab14dd2e717","error":null,"baseMsg":{"type":"can-show-terminal","data":{"command":"npm run server"}},"destination":"host"} [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:39:20,631 [ 510754]   WARN - #com.augmentcode.intellij.webviews.AbstractWebviewMessagingService - Failed to parse webview message as protobuf.
Exception: Missing type url when parsing: {"type":"can-show-terminal","data":{"command":"npm run server"}}
Message: {"type":"async-wrapper","requestId":"016eeed7-fb12-409e-bb5b-70a6c3183929","error":null,"baseMsg":{"type":"can-show-terminal","data":{"command":"npm run server"}},"destination":"host"}
2025-09-23 09:39:20,664 [ 510787]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_ERROR]: Failed to check if terminal can be shown: Error: Unable to parse webview message: {"type":"async-wrapper","requestId":"016eeed7-fb12-409e-bb5b-70a6c3183929","error":null,"baseMsg":{"type":"can-show-terminal","data":{"command":"npm run server"}},"destination":"host"} [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:39:20,717 [ 510840]   WARN - #com.augmentcode.intellij.chat.ChatWebviewMessageClient$Companion - Workspace guidelines are empty, not including in chat request. 
2025-09-23 09:39:21,877 [ 512000]   WARN - #c.i.e.wsl - Failed to get WSL mount root for Ubuntu: DOS working directory is expected, but got \\wsl.localhost\Ubuntu\home\oni\audio-transcriber
2025-09-23 09:39:22,595 [ 512718]   WARN - #c.i.e.wsl - Failed to get WSL mount root for Ubuntu: DOS working directory is expected, but got \\wsl.localhost\Ubuntu\home\oni\audio-transcriber
2025-09-23 09:39:23,028 [ 513151]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 79aaf534e95cd53da8534afe6b9ff3318f93499a1ad6ccc45ebd6b47a3ea67e4 -> 008532cc34ed496447878e40aa19e11159814bd1dd06eeccbc20caf0f6d3da2b
2025-09-23 09:39:27,210 [ 517333]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: No exchange with this request ID found. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:39:27,285 [ 517408]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: IntersectionDisplay context not found.  Call setIntersectionContext first. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:39:27,363 [ 517486]   WARN - #com.augmentcode.intellij.chat.ChatWebviewMessageClient$Companion - Workspace guidelines are empty, not including in chat request. 
2025-09-23 09:39:27,561 [ 517684]   WARN - #com.augmentcode.intellij.webviews.AbstractWebviewMessagingService - Failed to parse webview message as protobuf.
Exception: Missing type url when parsing: {"type":"can-show-terminal","data":{"terminalId":8}}
Message: {"type":"async-wrapper","requestId":"09f840f5-60a8-4a71-8817-3fdd5b7cd29d","error":null,"baseMsg":{"type":"can-show-terminal","data":{"terminalId":8}},"destination":"host"}
2025-09-23 09:39:27,562 [ 517685]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_ERROR]: Failed to check if terminal can be shown: Error: Unable to parse webview message: {"type":"async-wrapper","requestId":"09f840f5-60a8-4a71-8817-3fdd5b7cd29d","error":null,"baseMsg":{"type":"can-show-terminal","data":{"terminalId":8}},"destination":"host"} [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:39:31,022 [ 521145]   WARN - #c.i.e.wsl - Failed to get WSL mount root for Ubuntu: DOS working directory is expected, but got \\wsl.localhost\Ubuntu\home\oni\audio-transcriber
2025-09-23 09:39:31,081 [ 521204]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: No exchange with this request ID found. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:39:32,579 [ 522702]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: IntersectionDisplay context not found.  Call setIntersectionContext first. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:39:33,053 [ 523176]   WARN - #com.augmentcode.intellij.webviews.AbstractWebviewMessagingService - Failed to parse webview message as protobuf.
Exception: Missing type url when parsing: {"type":"can-show-terminal","data":{"command":"curl http://localhost:5000/api/health"}}
Message: {"type":"async-wrapper","requestId":"995f1af0-f354-4e7d-88e0-610d79b1f03a","error":null,"baseMsg":{"type":"can-show-terminal","data":{"command":"curl http://localhost:5000/api/health"}},"destination":"host"}
2025-09-23 09:39:33,055 [ 523178]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_ERROR]: Failed to check if terminal can be shown: Error: Unable to parse webview message: {"type":"async-wrapper","requestId":"995f1af0-f354-4e7d-88e0-610d79b1f03a","error":null,"baseMsg":{"type":"can-show-terminal","data":{"command":"curl http://localhost:5000/api/health"}},"destination":"host"} [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:39:35,518 [ 525641]   WARN - #com.augmentcode.intellij.webviews.AbstractWebviewMessagingService - Failed to parse webview message as protobuf.
Exception: Missing type url when parsing: {"type":"can-show-terminal","data":{"command":"curl http://localhost:5000/api/health"}}
Message: {"type":"async-wrapper","requestId":"a0e8219f-02f8-48ba-bcd4-2c2f85c21f7c","error":null,"baseMsg":{"type":"can-show-terminal","data":{"command":"curl http://localhost:5000/api/health"}},"destination":"host"}
2025-09-23 09:39:35,542 [ 525665]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_ERROR]: Failed to check if terminal can be shown: Error: Unable to parse webview message: {"type":"async-wrapper","requestId":"a0e8219f-02f8-48ba-bcd4-2c2f85c21f7c","error":null,"baseMsg":{"type":"can-show-terminal","data":{"command":"curl http://localhost:5000/api/health"}},"destination":"host"} [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:39:35,603 [ 525726]   WARN - #com.augmentcode.intellij.chat.ChatWebviewMessageClient$Companion - Workspace guidelines are empty, not including in chat request. 
2025-09-23 09:39:36,209 [ 526332]   WARN - #c.i.e.wsl - Failed to get WSL mount root for Ubuntu: DOS working directory is expected, but got \\wsl.localhost\Ubuntu\home\oni\audio-transcriber
2025-09-23 09:39:38,473 [ 528596]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: No exchange with this request ID found. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:39:39,929 [ 530052]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: IntersectionDisplay context not found.  Call setIntersectionContext first. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:39:39,996 [ 530119]   WARN - #com.augmentcode.intellij.chat.ChatWebviewMessageClient$Companion - Workspace guidelines are empty, not including in chat request. 
2025-09-23 09:39:43,230 [ 533353]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: No exchange with this request ID found. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:39:43,494 [ 533617]   WARN - #c.i.e.wsl - Failed to get WSL mount root for Ubuntu: DOS working directory is expected, but got \\wsl.localhost\Ubuntu\home\oni\audio-transcriber
2025-09-23 09:39:43,786 [ 533909]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: IntersectionDisplay context not found.  Call setIntersectionContext first. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:39:43,833 [ 533956]   WARN - #com.augmentcode.intellij.chat.ChatWebviewMessageClient$Companion - Workspace guidelines are empty, not including in chat request. 
2025-09-23 09:39:44,100 [ 534223]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Non of the blobs were indexed, backing off and retrying...
2025-09-23 09:39:47,384 [ 537507]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: No exchange with this request ID found. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:39:48,849 [ 538972]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: IntersectionDisplay context not found.  Call setIntersectionContext first. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:39:50,852 [ 540975]   WARN - #c.i.e.wsl - Failed to get WSL mount root for Ubuntu: DOS working directory is expected, but got \\wsl.localhost\Ubuntu\home\oni\audio-transcriber
2025-09-23 09:39:51,021 [ 541144]   WARN - #com.augmentcode.intellij.webviews.AbstractWebviewMessagingService - Failed to parse webview message as protobuf.
Exception: Missing type url when parsing: {"type":"can-show-terminal","data":{"command":"node server/server.js"}}
Message: {"type":"async-wrapper","requestId":"d1a7283d-3fa4-41c5-88ba-dd6e26f4249e","error":null,"baseMsg":{"type":"can-show-terminal","data":{"command":"node server/server.js"}},"destination":"host"}
2025-09-23 09:39:51,023 [ 541146]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_ERROR]: Failed to check if terminal can be shown: Error: Unable to parse webview message: {"type":"async-wrapper","requestId":"d1a7283d-3fa4-41c5-88ba-dd6e26f4249e","error":null,"baseMsg":{"type":"can-show-terminal","data":{"command":"node server/server.js"}},"destination":"host"} [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:39:52,121 [ 542244]   WARN - #com.augmentcode.intellij.webviews.AbstractWebviewMessagingService - Failed to parse webview message as protobuf.
Exception: Missing type url when parsing: {"type":"can-show-terminal","data":{"command":"node server/server.js"}}
Message: {"type":"async-wrapper","requestId":"f84dea0b-c784-4889-9e44-f218432fbc16","error":null,"baseMsg":{"type":"can-show-terminal","data":{"command":"node server/server.js"}},"destination":"host"}
2025-09-23 09:39:52,142 [ 542265]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_ERROR]: Failed to check if terminal can be shown: Error: Unable to parse webview message: {"type":"async-wrapper","requestId":"f84dea0b-c784-4889-9e44-f218432fbc16","error":null,"baseMsg":{"type":"can-show-terminal","data":{"command":"node server/server.js"}},"destination":"host"} [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:39:52,202 [ 542325]   WARN - #com.augmentcode.intellij.chat.ChatWebviewMessageClient$Companion - Workspace guidelines are empty, not including in chat request. 
2025-09-23 09:39:52,392 [ 542515]   WARN - #com.augmentcode.intellij.webviews.AbstractWebviewMessagingService - Failed to parse webview message as protobuf.
Exception: Missing type url when parsing: {"type":"can-show-terminal","data":{"terminalId":8}}
Message: {"type":"async-wrapper","requestId":"3d5189af-d2c2-4cc0-85dd-670dee50600d","error":null,"baseMsg":{"type":"can-show-terminal","data":{"terminalId":8}},"destination":"host"}
2025-09-23 09:39:52,394 [ 542517]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_ERROR]: Failed to check if terminal can be shown: Error: Unable to parse webview message: {"type":"async-wrapper","requestId":"3d5189af-d2c2-4cc0-85dd-670dee50600d","error":null,"baseMsg":{"type":"can-show-terminal","data":{"terminalId":8}},"destination":"host"} [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:39:53,092 [ 543215]   WARN - #c.i.e.wsl - Failed to get WSL mount root for Ubuntu: DOS working directory is expected, but got \\wsl.localhost\Ubuntu\home\oni\audio-transcriber
2025-09-23 09:39:55,759 [ 545882]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: No exchange with this request ID found. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:39:56,179 [ 546302]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: IntersectionDisplay context not found.  Call setIntersectionContext first. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:39:56,228 [ 546351]   WARN - #com.augmentcode.intellij.chat.ChatWebviewMessageClient$Companion - Workspace guidelines are empty, not including in chat request. 
2025-09-23 09:39:56,459 [ 546582]   WARN - #com.augmentcode.intellij.webviews.AbstractWebviewMessagingService - Failed to parse webview message as protobuf.
Exception: Missing type url when parsing: {"type":"can-show-terminal","data":{"terminalId":10}}
Message: {"type":"async-wrapper","requestId":"c2c236b9-ee9d-4563-833f-b4b98c87909c","error":null,"baseMsg":{"type":"can-show-terminal","data":{"terminalId":10}},"destination":"host"}
2025-09-23 09:39:56,461 [ 546584]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_ERROR]: Failed to check if terminal can be shown: Error: Unable to parse webview message: {"type":"async-wrapper","requestId":"c2c236b9-ee9d-4563-833f-b4b98c87909c","error":null,"baseMsg":{"type":"can-show-terminal","data":{"terminalId":10}},"destination":"host"} [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:39:57,455 [ 547578]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 7520c862ab9d9cdb64bccca2a6b330336f94780405ba0948466f53493a57c4f5 -> 1c66770908e5ab077fb95b030438c16482da22eda565a8c4da931c38c754f2e0
2025-09-23 09:39:58,955 [ 549078]   WARN - #com.augmentcode.intellij.sentry.SentryMetadataCollector - Git ls-files command failed with exit code: 128
2025-09-23 09:40:00,098 [ 550221]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: No exchange with this request ID found. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:40:00,198 [ 550321]   WARN - #c.i.e.wsl - Failed to get WSL mount root for Ubuntu: DOS working directory is expected, but got \\wsl.localhost\Ubuntu\home\oni\audio-transcriber
2025-09-23 09:40:00,933 [ 551056]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Non of the blobs were indexed, backing off and retrying...
2025-09-23 09:40:01,472 [ 551595]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: IntersectionDisplay context not found.  Call setIntersectionContext first. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:40:01,757 [ 551880]   WARN - #com.augmentcode.intellij.webviews.AbstractWebviewMessagingService - Failed to parse webview message as protobuf.
Exception: Missing type url when parsing: {"type":"can-show-terminal","data":{"command":"curl -X GET http://localhost:5000/api/health"}}
Message: {"type":"async-wrapper","requestId":"3cf562dd-b6c6-4a29-84f5-dc030b7c43ec","error":null,"baseMsg":{"type":"can-show-terminal","data":{"command":"curl -X GET http://localhost:5000/api/health"}},"destination":"host"}
2025-09-23 09:40:01,759 [ 551882]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_ERROR]: Failed to check if terminal can be shown: Error: Unable to parse webview message: {"type":"async-wrapper","requestId":"3cf562dd-b6c6-4a29-84f5-dc030b7c43ec","error":null,"baseMsg":{"type":"can-show-terminal","data":{"command":"curl -X GET http://localhost:5000/api/health"}},"destination":"host"} [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:40:03,254 [ 553377]   WARN - #com.augmentcode.intellij.webviews.AbstractWebviewMessagingService - Failed to parse webview message as protobuf.
Exception: Missing type url when parsing: {"type":"can-show-terminal","data":{"command":"curl -X GET http://localhost:5000/api/health"}}
Message: {"type":"async-wrapper","requestId":"5c7730f7-41cf-4904-842e-ae945cfdc513","error":null,"baseMsg":{"type":"can-show-terminal","data":{"command":"curl -X GET http://localhost:5000/api/health"}},"destination":"host"}
2025-09-23 09:40:03,257 [ 553380]   WARN - #c.i.o.progress - Suppressed a frequent exception logged for the 10th time: This method is forbidden on EDT because it does not pump the event queue. Switch to a BGT, or use com.intellij.openapi.progress.TasksKt.runWithModalProgressBlocking. 
2025-09-23 09:40:03,257 [ 553380]   WARN - #c.i.o.progress - Suppressed a frequent exception logged for the 10th time: This method is forbidden on EDT because it does not pump the event queue. Switch to a BGT, or use com.intellij.openapi.progress.TasksKt.runWithModalProgressBlocking. 
2025-09-23 09:40:03,259 [ 553382]   WARN - #c.i.o.progress - Suppressed a frequent exception logged for the 10th time: This method is forbidden on EDT because it does not pump the event queue. Switch to a BGT, or use com.intellij.openapi.progress.TasksKt.runWithModalProgressBlocking. 
2025-09-23 09:40:03,259 [ 553382]   WARN - #c.i.o.progress - Suppressed a frequent exception logged for the 10th time: This method is forbidden on EDT because it does not pump the event queue. Switch to a BGT, or use com.intellij.openapi.progress.TasksKt.runWithModalProgressBlocking. 
2025-09-23 09:40:03,260 [ 553383]   WARN - #c.i.o.progress - Suppressed a frequent exception logged for the 10th time: This method is forbidden on EDT because it does not pump the event queue. Switch to a BGT, or use com.intellij.openapi.progress.TasksKt.runWithModalProgressBlocking. 
2025-09-23 09:40:03,274 [ 553397]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_ERROR]: Failed to check if terminal can be shown: Error: Unable to parse webview message: {"type":"async-wrapper","requestId":"5c7730f7-41cf-4904-842e-ae945cfdc513","error":null,"baseMsg":{"type":"can-show-terminal","data":{"command":"curl -X GET http://localhost:5000/api/health"}},"destination":"host"} [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:40:03,337 [ 553460]   WARN - #com.augmentcode.intellij.chat.ChatWebviewMessageClient$Companion - Workspace guidelines are empty, not including in chat request. 
2025-09-23 09:40:03,935 [ 554058]   WARN - #c.i.e.wsl - Failed to get WSL mount root for Ubuntu: DOS working directory is expected, but got \\wsl.localhost\Ubuntu\home\oni\audio-transcriber
2025-09-23 09:40:04,253 [ 554376]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 58a4c2d828dca4e2c72d2bc6631364af18b5d8817e1009796be56d67017ed298 -> cd7d80875934c274364b4bb38b2fd3dfd56a4a2c662aea8b2037beb8a53ad158
2025-09-23 09:40:06,366 [ 556489]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: No exchange with this request ID found. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:40:08,270 [ 558393]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: IntersectionDisplay context not found.  Call setIntersectionContext first. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:40:08,346 [ 558469]   WARN - #com.augmentcode.intellij.chat.ChatWebviewMessageClient$Companion - Workspace guidelines are empty, not including in chat request. 
2025-09-23 09:40:08,856 [ 558979]   WARN - #c.i.e.wsl - Failed to get WSL mount root for Ubuntu: DOS working directory is expected, but got \\wsl.localhost\Ubuntu\home\oni\audio-transcriber
2025-09-23 09:40:11,985 [ 562108]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: No exchange with this request ID found. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:40:15,860 [ 565983]   WARN - #c.i.e.wsl - Failed to get WSL mount root for Ubuntu: DOS working directory is expected, but got \\wsl.localhost\Ubuntu\home\oni\audio-transcriber
2025-09-23 09:40:16,317 [ 566440]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (cd7d80875934c274364b4bb38b2fd3dfd56a4a2c662aea8b2037beb8a53ad158) is unknown but expected to be uploaded
2025-09-23 09:40:16,317 [ 566440]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (cd7d80875934c274364b4bb38b2fd3dfd56a4a2c662aea8b2037beb8a53ad158) is unknown but expected to be uploaded
2025-09-23 09:40:16,936 [ 567059]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: cd7d80875934c274364b4bb38b2fd3dfd56a4a2c662aea8b2037beb8a53ad158 -> 81cdc7ada2b005fd5e289965f7482906cb35f40e4112ba6be455726e1711164b
2025-09-23 09:40:17,410 [ 567533]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: cd7d80875934c274364b4bb38b2fd3dfd56a4a2c662aea8b2037beb8a53ad158 -> 81cdc7ada2b005fd5e289965f7482906cb35f40e4112ba6be455726e1711164b
2025-09-23 09:40:18,232 [ 568355]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: IntersectionDisplay context not found.  Call setIntersectionContext first. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:40:18,549 [ 568672]   WARN - #c.i.e.wsl - Failed to get WSL mount root for Ubuntu: DOS working directory is expected, but got \\wsl.localhost\Ubuntu\home\oni\audio-transcriber
2025-09-23 09:40:19,283 [ 569406]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: Loading changes  for 1e6ef5b8-91b1-46e4-9859-40dbe7d4bc6c retry 0:  Error: No changes found [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:40:19,283 [ 569406]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: Loading changes  for 1e6ef5b8-91b1-46e4-9859-40dbe7d4bc6c retry 1:  Error: No changes found [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:40:19,295 [ 569418]   WARN - #com.augmentcode.intellij.chat.ChatWebviewMessageClient$Companion - Workspace guidelines are empty, not including in chat request. 
2025-09-23 09:40:19,768 [ 569891]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 81cdc7ada2b005fd5e289965f7482906cb35f40e4112ba6be455726e1711164b -> c6d2c67b11f72e27cd2a3cf26af526e9c4f91f80c9ba77f27386549d737bb151
2025-09-23 09:40:19,788 [ 569911]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: Loading changes  for 1e6ef5b8-91b1-46e4-9859-40dbe7d4bc6c retry 2:  Error: No changes found [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:40:19,788 [ 569911]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: Loading changes  for 1e6ef5b8-91b1-46e4-9859-40dbe7d4bc6c retry 3:  Error: No changes found [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:40:19,789 [ 569912]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: Loading changes failed for 1e6ef5b8-91b1-46e4-9859-40dbe7d4bc6c:  Error: No changes found [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:40:20,292 [ 570415]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: Loading changes  for 1e6ef5b8-91b1-46e4-9859-40dbe7d4bc6c retry 4:  Error: No changes found [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:40:20,292 [ 570415]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: Loading changes failed for 1e6ef5b8-91b1-46e4-9859-40dbe7d4bc6c:  Error: No changes found [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:40:22,534 [ 572657]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: No exchange with this request ID found. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:40:22,607 [ 572730]   WARN - #c.i.e.wsl - Failed to get WSL mount root for Ubuntu: DOS working directory is expected, but got \\wsl.localhost\Ubuntu\home\oni\audio-transcriber
2025-09-23 09:40:23,389 [ 573512]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: c6d2c67b11f72e27cd2a3cf26af526e9c4f91f80c9ba77f27386549d737bb151 -> a5f63ed1b802e58f122627aeb95e75b948543cec5b041f4afc887dc718940113
2025-09-23 09:40:23,945 [ 574068]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: IntersectionDisplay context not found.  Call setIntersectionContext first. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:40:24,327 [ 574450]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: IntersectionDisplay context not found.  Call setIntersectionContext first. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:40:26,053 [ 576176]   WARN - #com.augmentcode.intellij.webviews.AbstractWebviewMessagingService - Failed to parse webview message as protobuf.
Exception: Missing type url when parsing: {"type":"can-show-terminal","data":{"command":"node server/server.js"}}
Message: {"type":"async-wrapper","requestId":"2e99f7e6-4f69-4ca2-a4a6-7281d3b30298","error":null,"baseMsg":{"type":"can-show-terminal","data":{"command":"node server/server.js"}},"destination":"host"}
2025-09-23 09:40:26,054 [ 576177]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_ERROR]: Failed to check if terminal can be shown: Error: Unable to parse webview message: {"type":"async-wrapper","requestId":"2e99f7e6-4f69-4ca2-a4a6-7281d3b30298","error":null,"baseMsg":{"type":"can-show-terminal","data":{"command":"node server/server.js"}},"destination":"host"} [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:40:26,118 [ 576241]   WARN - #com.augmentcode.intellij.webviews.AbstractWebviewMessagingService - Failed to parse webview message as protobuf.
Exception: Missing type url when parsing: {"type":"can-show-terminal","data":{"terminalId":10}}
Message: {"type":"async-wrapper","requestId":"ca780589-1fb5-4166-8b7c-aee6e21755c3","error":null,"baseMsg":{"type":"can-show-terminal","data":{"terminalId":10}},"destination":"host"}
2025-09-23 09:40:26,119 [ 576242]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_ERROR]: Failed to check if terminal can be shown: Error: Unable to parse webview message: {"type":"async-wrapper","requestId":"ca780589-1fb5-4166-8b7c-aee6e21755c3","error":null,"baseMsg":{"type":"can-show-terminal","data":{"terminalId":10}},"destination":"host"} [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:40:26,824 [ 576947]   WARN - #com.augmentcode.intellij.webviews.AbstractWebviewMessagingService - Failed to parse webview message as protobuf.
Exception: Missing type url when parsing: {"type":"can-show-terminal","data":{"command":"node server/server.js"}}
Message: {"type":"async-wrapper","requestId":"6cfc7927-0799-43dc-9287-42f45d02013c","error":null,"baseMsg":{"type":"can-show-terminal","data":{"command":"node server/server.js"}},"destination":"host"}
2025-09-23 09:40:26,857 [ 576980]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_ERROR]: Failed to check if terminal can be shown: Error: Unable to parse webview message: {"type":"async-wrapper","requestId":"6cfc7927-0799-43dc-9287-42f45d02013c","error":null,"baseMsg":{"type":"can-show-terminal","data":{"command":"node server/server.js"}},"destination":"host"} [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:40:26,862 [ 576985]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (81cdc7ada2b005fd5e289965f7482906cb35f40e4112ba6be455726e1711164b) is unknown but expected to be uploaded
2025-09-23 09:40:26,862 [ 576985]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (81cdc7ada2b005fd5e289965f7482906cb35f40e4112ba6be455726e1711164b) is unknown but expected to be uploaded
2025-09-23 09:40:26,921 [ 577044]   WARN - #com.augmentcode.intellij.chat.ChatWebviewMessageClient$Companion - Workspace guidelines are empty, not including in chat request. 
2025-09-23 09:40:27,432 [ 577555]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 81cdc7ada2b005fd5e289965f7482906cb35f40e4112ba6be455726e1711164b -> a5f63ed1b802e58f122627aeb95e75b948543cec5b041f4afc887dc718940113
2025-09-23 09:40:27,661 [ 577784]   WARN - #c.i.e.wsl - Failed to get WSL mount root for Ubuntu: DOS working directory is expected, but got \\wsl.localhost\Ubuntu\home\oni\audio-transcriber
2025-09-23 09:40:30,505 [ 580628]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: No exchange with this request ID found. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:40:30,959 [ 581082]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: IntersectionDisplay context not found.  Call setIntersectionContext first. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:40:31,039 [ 581162]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 81cdc7ada2b005fd5e289965f7482906cb35f40e4112ba6be455726e1711164b -> 50e90b21bd026e053b0f279f03931ea6d13bae2fa913098c8e9da3bf07ebb6bd
2025-09-23 09:40:31,118 [ 581241]   WARN - #com.augmentcode.intellij.chat.ChatWebviewMessageClient$Companion - Workspace guidelines are empty, not including in chat request. 
2025-09-23 09:40:31,226 [ 581349]   WARN - #com.augmentcode.intellij.webviews.AbstractWebviewMessagingService - Failed to parse webview message as protobuf.
Exception: Missing type url when parsing: {"type":"can-show-terminal","data":{"terminalId":12}}
Message: {"type":"async-wrapper","requestId":"77ab47ac-3da7-4c70-afa6-225fedd1c695","error":null,"baseMsg":{"type":"can-show-terminal","data":{"terminalId":12}},"destination":"host"}
2025-09-23 09:40:31,228 [ 581351]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_ERROR]: Failed to check if terminal can be shown: Error: Unable to parse webview message: {"type":"async-wrapper","requestId":"77ab47ac-3da7-4c70-afa6-225fedd1c695","error":null,"baseMsg":{"type":"can-show-terminal","data":{"terminalId":12}},"destination":"host"} [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:40:34,505 [ 584628]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (a5f63ed1b802e58f122627aeb95e75b948543cec5b041f4afc887dc718940113) is unknown but expected to be uploaded
2025-09-23 09:40:34,506 [ 584629]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (a5f63ed1b802e58f122627aeb95e75b948543cec5b041f4afc887dc718940113) is unknown but expected to be uploaded
2025-09-23 09:40:34,506 [ 584629]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (a5f63ed1b802e58f122627aeb95e75b948543cec5b041f4afc887dc718940113) is unknown but expected to be uploaded
2025-09-23 09:40:34,506 [ 584629]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (a5f63ed1b802e58f122627aeb95e75b948543cec5b041f4afc887dc718940113) is unknown but expected to be uploaded
2025-09-23 09:40:34,596 [ 584719]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: No exchange with this request ID found. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:40:35,241 [ 585364]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: a5f63ed1b802e58f122627aeb95e75b948543cec5b041f4afc887dc718940113 -> 50e90b21bd026e053b0f279f03931ea6d13bae2fa913098c8e9da3bf07ebb6bd
2025-09-23 09:40:35,682 [ 585805]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: a5f63ed1b802e58f122627aeb95e75b948543cec5b041f4afc887dc718940113 -> 50e90b21bd026e053b0f279f03931ea6d13bae2fa913098c8e9da3bf07ebb6bd
2025-09-23 09:40:35,940 [ 586063]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: IntersectionDisplay context not found.  Call setIntersectionContext first. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:40:36,147 [ 586270]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: a5f63ed1b802e58f122627aeb95e75b948543cec5b041f4afc887dc718940113 -> 50e90b21bd026e053b0f279f03931ea6d13bae2fa913098c8e9da3bf07ebb6bd
2025-09-23 09:40:36,223 [ 586346]   WARN - #com.augmentcode.intellij.webviews.AbstractWebviewMessagingService - Failed to parse webview message as protobuf.
Exception: Missing type url when parsing: {"type":"can-show-terminal","data":{"command":"curl -X GET http://localhost:5000/api/health"}}
Message: {"type":"async-wrapper","requestId":"e83453c8-f306-4172-848f-e0ed1ba9fb49","error":null,"baseMsg":{"type":"can-show-terminal","data":{"command":"curl -X GET http://localhost:5000/api/health"}},"destination":"host"}
2025-09-23 09:40:36,225 [ 586348]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_ERROR]: Failed to check if terminal can be shown: Error: Unable to parse webview message: {"type":"async-wrapper","requestId":"e83453c8-f306-4172-848f-e0ed1ba9fb49","error":null,"baseMsg":{"type":"can-show-terminal","data":{"command":"curl -X GET http://localhost:5000/api/health"}},"destination":"host"} [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:40:38,057 [ 588180]   WARN - #com.augmentcode.intellij.webviews.AbstractWebviewMessagingService - Failed to parse webview message as protobuf.
Exception: Missing type url when parsing: {"type":"can-show-terminal","data":{"command":"curl -X GET http://localhost:5000/api/health"}}
Message: {"type":"async-wrapper","requestId":"3e617949-8ab7-47c7-9723-8866c7ac4e97","error":null,"baseMsg":{"type":"can-show-terminal","data":{"command":"curl -X GET http://localhost:5000/api/health"}},"destination":"host"}
2025-09-23 09:40:38,078 [ 588201]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_ERROR]: Failed to check if terminal can be shown: Error: Unable to parse webview message: {"type":"async-wrapper","requestId":"3e617949-8ab7-47c7-9723-8866c7ac4e97","error":null,"baseMsg":{"type":"can-show-terminal","data":{"command":"curl -X GET http://localhost:5000/api/health"}},"destination":"host"} [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:40:38,163 [ 588286]   WARN - #com.augmentcode.intellij.chat.ChatWebviewMessageClient$Companion - Workspace guidelines are empty, not including in chat request. 
2025-09-23 09:40:38,744 [ 588867]   WARN - #c.i.e.wsl - Failed to get WSL mount root for Ubuntu: DOS working directory is expected, but got \\wsl.localhost\Ubuntu\home\oni\audio-transcriber
2025-09-23 09:40:40,058 [ 590181]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: a5f63ed1b802e58f122627aeb95e75b948543cec5b041f4afc887dc718940113 -> d843b9d79326897927d7e4948692ba265e2d64f3b52c632e25569c4fb2f3cffb
2025-09-23 09:40:41,009 [ 591132]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: No exchange with this request ID found. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:40:42,979 [ 593102]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: IntersectionDisplay context not found.  Call setIntersectionContext first. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:40:43,262 [ 593385]   WARN - #com.augmentcode.intellij.webviews.AbstractWebviewMessagingService - Failed to parse webview message as protobuf.
Exception: Missing type url when parsing: {"type":"can-show-terminal","data":{"command":"lsof -i :5000"}}
Message: {"type":"async-wrapper","requestId":"0bb12b42-4eb6-4ecf-ba68-f66b8e1f185a","error":null,"baseMsg":{"type":"can-show-terminal","data":{"command":"lsof -i :5000"}},"destination":"host"}
2025-09-23 09:40:43,265 [ 593388]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_ERROR]: Failed to check if terminal can be shown: Error: Unable to parse webview message: {"type":"async-wrapper","requestId":"0bb12b42-4eb6-4ecf-ba68-f66b8e1f185a","error":null,"baseMsg":{"type":"can-show-terminal","data":{"command":"lsof -i :5000"}},"destination":"host"} [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:40:47,105 [ 597228]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (d843b9d79326897927d7e4948692ba265e2d64f3b52c632e25569c4fb2f3cffb) is unknown but expected to be uploaded
2025-09-23 09:40:47,105 [ 597228]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (d843b9d79326897927d7e4948692ba265e2d64f3b52c632e25569c4fb2f3cffb) is unknown but expected to be uploaded
2025-09-23 09:40:48,844 [ 598967]   WARN - #c.i.e.wsl - Failed to get WSL mount root for Ubuntu: DOS working directory is expected, but got \\wsl.localhost\Ubuntu\home\oni\audio-transcriber
2025-09-23 09:40:49,839 [ 599962]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: d843b9d79326897927d7e4948692ba265e2d64f3b52c632e25569c4fb2f3cffb -> 9b0464e962f6d23db0597786f533dac1506e31700d5dbada2a87aa1020e6e7e7
2025-09-23 09:40:52,787 [ 602910]   WARN - #c.i.e.wsl - Failed to get WSL mount root for Ubuntu: DOS working directory is expected, but got \\wsl.localhost\Ubuntu\home\oni\audio-transcriber
2025-09-23 09:40:53,441 [ 603564]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 9b0464e962f6d23db0597786f533dac1506e31700d5dbada2a87aa1020e6e7e7 -> af0aa139a64c4bc88572537d38b906f0817af15ff898818f5ae9eb763d5ca40a
2025-09-23 09:40:55,386 [ 605509]   WARN - #com.augmentcode.intellij.webviews.AbstractWebviewMessagingService - Failed to parse webview message as protobuf.
Exception: Missing type url when parsing: {"type":"can-show-terminal","data":{"command":"lsof -i :5000"}}
Message: {"type":"async-wrapper","requestId":"1b012c41-f91a-47fb-bb1f-b4fbb9a28c04","error":null,"baseMsg":{"type":"can-show-terminal","data":{"command":"lsof -i :5000"}},"destination":"host"}
2025-09-23 09:40:55,417 [ 605540]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_ERROR]: Failed to check if terminal can be shown: Error: Unable to parse webview message: {"type":"async-wrapper","requestId":"1b012c41-f91a-47fb-bb1f-b4fbb9a28c04","error":null,"baseMsg":{"type":"can-show-terminal","data":{"command":"lsof -i :5000"}},"destination":"host"} [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:40:55,504 [ 605627]   WARN - #com.augmentcode.intellij.chat.ChatWebviewMessageClient$Companion - Workspace guidelines are empty, not including in chat request. 
2025-09-23 09:40:56,034 [ 606157]   WARN - #c.i.u.i.p.ProjectIndexableFilesFilterHealthCheck - Following files are indexable but they were NOT found in filter. Errors count: 2. Examples:
file id=199049 path=//wsl.localhost/Ubuntu/home/<USER>/audio-transcriber/node_modules/.cache/default-development/0.pack
file id=201627 path=//wsl.localhost/Ubuntu/home/<USER>/audio-transcriber/node_modules/.cache/default-development/1.pack
2025-09-23 09:40:56,092 [ 606215]   WARN - #c.i.e.wsl - Failed to get WSL mount root for Ubuntu: DOS working directory is expected, but got \\wsl.localhost\Ubuntu\home\oni\audio-transcriber
2025-09-23 09:40:59,099 [ 609222]   WARN - #com.augmentcode.intellij.sentry.SentryMetadataCollector - Git ls-files command failed with exit code: 128
2025-09-23 09:40:59,803 [ 609926]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: No exchange with this request ID found. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:41:01,271 [ 611394]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: IntersectionDisplay context not found.  Call setIntersectionContext first. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:41:01,566 [ 611689]   WARN - #com.augmentcode.intellij.webviews.AbstractWebviewMessagingService - Failed to parse webview message as protobuf.
Exception: Missing type url when parsing: {"type":"can-show-terminal","data":{"command":"kill 5360"}}
Message: {"type":"async-wrapper","requestId":"b4eac85b-2b83-4dad-9e4d-966c08b85137","error":null,"baseMsg":{"type":"can-show-terminal","data":{"command":"kill 5360"}},"destination":"host"}
2025-09-23 09:41:01,568 [ 611691]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_ERROR]: Failed to check if terminal can be shown: Error: Unable to parse webview message: {"type":"async-wrapper","requestId":"b4eac85b-2b83-4dad-9e4d-966c08b85137","error":null,"baseMsg":{"type":"can-show-terminal","data":{"command":"kill 5360"}},"destination":"host"} [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:41:03,575 [ 613698]   WARN - #com.augmentcode.intellij.webviews.AbstractWebviewMessagingService - Failed to parse webview message as protobuf.
Exception: Missing type url when parsing: {"type":"can-show-terminal","data":{"command":"kill 5360"}}
Message: {"type":"async-wrapper","requestId":"e9570f40-cf71-4837-b858-0b442f20ca33","error":null,"baseMsg":{"type":"can-show-terminal","data":{"command":"kill 5360"}},"destination":"host"}
2025-09-23 09:41:03,586 [ 613709]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_ERROR]: Failed to check if terminal can be shown: Error: Unable to parse webview message: {"type":"async-wrapper","requestId":"e9570f40-cf71-4837-b858-0b442f20ca33","error":null,"baseMsg":{"type":"can-show-terminal","data":{"command":"kill 5360"}},"destination":"host"} [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:41:03,622 [ 613745]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_ERROR]: _validateAndCheckTool called while another tool is active [object Object] [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:41:03,670 [ 613793]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: IntersectionDisplay context not found.  Call setIntersectionContext first. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:41:03,848 [ 613971]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (af0aa139a64c4bc88572537d38b906f0817af15ff898818f5ae9eb763d5ca40a) is unknown but expected to be uploaded
2025-09-23 09:41:03,848 [ 613971]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (af0aa139a64c4bc88572537d38b906f0817af15ff898818f5ae9eb763d5ca40a) is unknown but expected to be uploaded
2025-09-23 09:41:03,958 [ 614081]   WARN - #com.augmentcode.intellij.webviews.AbstractWebviewMessagingService - Failed to parse webview message as protobuf.
Exception: Missing type url when parsing: {"type":"can-show-terminal","data":{"command":"node server/server.js"}}
Message: {"type":"async-wrapper","requestId":"4433e1aa-cb88-40f5-a74f-a7b34ac790e7","error":null,"baseMsg":{"type":"can-show-terminal","data":{"command":"node server/server.js"}},"destination":"host"}
2025-09-23 09:41:03,959 [ 614082]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_ERROR]: Failed to check if terminal can be shown: Error: Unable to parse webview message: {"type":"async-wrapper","requestId":"4433e1aa-cb88-40f5-a74f-a7b34ac790e7","error":null,"baseMsg":{"type":"can-show-terminal","data":{"command":"node server/server.js"}},"destination":"host"} [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:41:04,262 [ 614385]   WARN - #c.i.e.wsl - Failed to get WSL mount root for Ubuntu: DOS working directory is expected, but got \\wsl.localhost\Ubuntu\home\oni\audio-transcriber
2025-09-23 09:41:04,279 [ 614402]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: af0aa139a64c4bc88572537d38b906f0817af15ff898818f5ae9eb763d5ca40a -> 841b7c0c70a9a63e659ae69141fa06398bb71206ca035020cafb73c9cb654932
2025-09-23 09:41:05,946 [ 616069]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: af0aa139a64c4bc88572537d38b906f0817af15ff898818f5ae9eb763d5ca40a -> 75328b481f5ef5f69276fd16be2c631d8676b55b69e16e5fe02fe44004c456de
2025-09-23 09:41:09,418 [ 619541]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (841b7c0c70a9a63e659ae69141fa06398bb71206ca035020cafb73c9cb654932) is unknown but expected to be uploaded
2025-09-23 09:41:09,418 [ 619541]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (841b7c0c70a9a63e659ae69141fa06398bb71206ca035020cafb73c9cb654932) is unknown but expected to be uploaded
2025-09-23 09:41:09,418 [ 619541]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (841b7c0c70a9a63e659ae69141fa06398bb71206ca035020cafb73c9cb654932) is unknown but expected to be uploaded
2025-09-23 09:41:09,987 [ 620110]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 841b7c0c70a9a63e659ae69141fa06398bb71206ca035020cafb73c9cb654932 -> 75328b481f5ef5f69276fd16be2c631d8676b55b69e16e5fe02fe44004c456de
2025-09-23 09:41:10,433 [ 620556]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 841b7c0c70a9a63e659ae69141fa06398bb71206ca035020cafb73c9cb654932 -> 75328b481f5ef5f69276fd16be2c631d8676b55b69e16e5fe02fe44004c456de
2025-09-23 09:41:13,569 [ 623692]   WARN - #c.i.e.wsl - Failed to get WSL mount root for Ubuntu: DOS working directory is expected, but got \\wsl.localhost\Ubuntu\home\oni\audio-transcriber
2025-09-23 09:41:13,828 [ 623951]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (75328b481f5ef5f69276fd16be2c631d8676b55b69e16e5fe02fe44004c456de) is unknown but expected to be uploaded
2025-09-23 09:41:13,828 [ 623951]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (75328b481f5ef5f69276fd16be2c631d8676b55b69e16e5fe02fe44004c456de) is unknown but expected to be uploaded
2025-09-23 09:41:13,828 [ 623951]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (75328b481f5ef5f69276fd16be2c631d8676b55b69e16e5fe02fe44004c456de) is unknown but expected to be uploaded
2025-09-23 09:41:13,828 [ 623951]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (75328b481f5ef5f69276fd16be2c631d8676b55b69e16e5fe02fe44004c456de) is unknown but expected to be uploaded
2025-09-23 09:41:14,053 [ 624176]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 841b7c0c70a9a63e659ae69141fa06398bb71206ca035020cafb73c9cb654932 -> fb360b5251b57fc0cde66730aaf901124d2507aa7c242d24eff964241ef1bcde
2025-09-23 09:41:14,661 [ 624784]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 75328b481f5ef5f69276fd16be2c631d8676b55b69e16e5fe02fe44004c456de -> fb360b5251b57fc0cde66730aaf901124d2507aa7c242d24eff964241ef1bcde
2025-09-23 09:41:15,115 [ 625238]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 75328b481f5ef5f69276fd16be2c631d8676b55b69e16e5fe02fe44004c456de -> fb360b5251b57fc0cde66730aaf901124d2507aa7c242d24eff964241ef1bcde
2025-09-23 09:41:16,121 [ 626244]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 75328b481f5ef5f69276fd16be2c631d8676b55b69e16e5fe02fe44004c456de -> fb360b5251b57fc0cde66730aaf901124d2507aa7c242d24eff964241ef1bcde
2025-09-23 09:41:19,690 [ 629813]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 75328b481f5ef5f69276fd16be2c631d8676b55b69e16e5fe02fe44004c456de -> fb360b5251b57fc0cde66730aaf901124d2507aa7c242d24eff964241ef1bcde
2025-09-23 09:41:25,705 [ 635828]   WARN - #c.i.e.wsl - Failed to get WSL mount root for Ubuntu: DOS working directory is expected, but got \\wsl.localhost\Ubuntu\home\oni\audio-transcriber
2025-09-23 09:41:26,602 [ 636725]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (fb360b5251b57fc0cde66730aaf901124d2507aa7c242d24eff964241ef1bcde) is unknown but expected to be uploaded
2025-09-23 09:41:26,602 [ 636725]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (fb360b5251b57fc0cde66730aaf901124d2507aa7c242d24eff964241ef1bcde) is unknown but expected to be uploaded
2025-09-23 09:41:26,602 [ 636725]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (fb360b5251b57fc0cde66730aaf901124d2507aa7c242d24eff964241ef1bcde) is unknown but expected to be uploaded
2025-09-23 09:41:26,602 [ 636725]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (fb360b5251b57fc0cde66730aaf901124d2507aa7c242d24eff964241ef1bcde) is unknown but expected to be uploaded
2025-09-23 09:41:26,602 [ 636725]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (fb360b5251b57fc0cde66730aaf901124d2507aa7c242d24eff964241ef1bcde) is unknown but expected to be uploaded
2025-09-23 09:41:26,602 [ 636725]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (fb360b5251b57fc0cde66730aaf901124d2507aa7c242d24eff964241ef1bcde) is unknown but expected to be uploaded
2025-09-23 09:41:26,789 [ 636912]   WARN - #com.augmentcode.intellij.webviews.AbstractWebviewMessagingService - Failed to parse webview message as protobuf.
Exception: Missing type url when parsing: {"type":"can-show-terminal","data":{"command":"node server/server.js"}}
Message: {"type":"async-wrapper","requestId":"0c13729f-6835-4da2-8a02-360614a084bf","error":null,"baseMsg":{"type":"can-show-terminal","data":{"command":"node server/server.js"}},"destination":"host"}
2025-09-23 09:41:26,816 [ 636939]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_ERROR]: Failed to check if terminal can be shown: Error: Unable to parse webview message: {"type":"async-wrapper","requestId":"0c13729f-6835-4da2-8a02-360614a084bf","error":null,"baseMsg":{"type":"can-show-terminal","data":{"command":"node server/server.js"}},"destination":"host"} [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:41:26,866 [ 636989]   WARN - #com.augmentcode.intellij.chat.ChatWebviewMessageClient$Companion - Workspace guidelines are empty, not including in chat request. 
2025-09-23 09:41:27,333 [ 637456]   WARN - #c.i.e.wsl - Failed to get WSL mount root for Ubuntu: DOS working directory is expected, but got \\wsl.localhost\Ubuntu\home\oni\audio-transcriber
2025-09-23 09:41:27,846 [ 637969]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: fb360b5251b57fc0cde66730aaf901124d2507aa7c242d24eff964241ef1bcde -> a68bf4c6aa44940f448f4c63c22ebdff0d5fbee5c16c891280c6b13e5d888130
2025-09-23 09:41:28,325 [ 638448]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: fb360b5251b57fc0cde66730aaf901124d2507aa7c242d24eff964241ef1bcde -> a68bf4c6aa44940f448f4c63c22ebdff0d5fbee5c16c891280c6b13e5d888130
2025-09-23 09:41:28,845 [ 638968]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: fb360b5251b57fc0cde66730aaf901124d2507aa7c242d24eff964241ef1bcde -> a68bf4c6aa44940f448f4c63c22ebdff0d5fbee5c16c891280c6b13e5d888130
2025-09-23 09:41:29,294 [ 639417]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: fb360b5251b57fc0cde66730aaf901124d2507aa7c242d24eff964241ef1bcde -> a68bf4c6aa44940f448f4c63c22ebdff0d5fbee5c16c891280c6b13e5d888130
2025-09-23 09:41:29,722 [ 639845]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: fb360b5251b57fc0cde66730aaf901124d2507aa7c242d24eff964241ef1bcde -> a68bf4c6aa44940f448f4c63c22ebdff0d5fbee5c16c891280c6b13e5d888130
2025-09-23 09:41:30,906 [ 641029]   WARN - #c.i.e.wsl - Failed to get WSL mount root for Ubuntu: DOS working directory is expected, but got \\wsl.localhost\Ubuntu\home\oni\audio-transcriber
2025-09-23 09:41:31,734 [ 641857]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: No exchange with this request ID found. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:41:31,776 [ 641899]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: IntersectionDisplay context not found.  Call setIntersectionContext first. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:41:31,835 [ 641958]   WARN - #com.augmentcode.intellij.chat.ChatWebviewMessageClient$Companion - Workspace guidelines are empty, not including in chat request. 
2025-09-23 09:41:32,075 [ 642198]   WARN - #com.augmentcode.intellij.webviews.AbstractWebviewMessagingService - Failed to parse webview message as protobuf.
Exception: Missing type url when parsing: {"type":"can-show-terminal","data":{"terminalId":16}}
Message: {"type":"async-wrapper","requestId":"5a2ab37d-5713-464b-a427-87d1a4f92943","error":null,"baseMsg":{"type":"can-show-terminal","data":{"terminalId":16}},"destination":"host"}
2025-09-23 09:41:32,077 [ 642200]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_ERROR]: Failed to check if terminal can be shown: Error: Unable to parse webview message: {"type":"async-wrapper","requestId":"5a2ab37d-5713-464b-a427-87d1a4f92943","error":null,"baseMsg":{"type":"can-show-terminal","data":{"terminalId":16}},"destination":"host"} [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:41:33,163 [ 643286]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: fb360b5251b57fc0cde66730aaf901124d2507aa7c242d24eff964241ef1bcde -> a68bf4c6aa44940f448f4c63c22ebdff0d5fbee5c16c891280c6b13e5d888130
2025-09-23 09:41:37,477 [ 647600]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: No exchange with this request ID found. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:41:38,096 [ 648219]   WARN - #c.i.e.wsl - Failed to get WSL mount root for Ubuntu: DOS working directory is expected, but got \\wsl.localhost\Ubuntu\home\oni\audio-transcriber
2025-09-23 09:41:38,893 [ 649016]   WARN - #c.i.e.wsl - Failed to get WSL mount root for Ubuntu: DOS working directory is expected, but got \\wsl.localhost\Ubuntu\home\oni\audio-transcriber
2025-09-23 09:41:39,095 [ 649218]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: IntersectionDisplay context not found.  Call setIntersectionContext first. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:41:39,367 [ 649490]   WARN - #com.augmentcode.intellij.webviews.AbstractWebviewMessagingService - Failed to parse webview message as protobuf.
Exception: Missing type url when parsing: {"type":"can-show-terminal","data":{"command":"curl -X GET http://localhost:5000/api/health"}}
Message: {"type":"async-wrapper","requestId":"2018e0e7-37d7-4a80-9cb2-3eaa4e11b598","error":null,"baseMsg":{"type":"can-show-terminal","data":{"command":"curl -X GET http://localhost:5000/api/health"}},"destination":"host"}
2025-09-23 09:41:39,368 [ 649491]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_ERROR]: Failed to check if terminal can be shown: Error: Unable to parse webview message: {"type":"async-wrapper","requestId":"2018e0e7-37d7-4a80-9cb2-3eaa4e11b598","error":null,"baseMsg":{"type":"can-show-terminal","data":{"command":"curl -X GET http://localhost:5000/api/health"}},"destination":"host"} [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:41:40,079 [ 650202]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (a68bf4c6aa44940f448f4c63c22ebdff0d5fbee5c16c891280c6b13e5d888130) is unknown but expected to be uploaded
2025-09-23 09:41:40,079 [ 650202]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (a68bf4c6aa44940f448f4c63c22ebdff0d5fbee5c16c891280c6b13e5d888130) is unknown but expected to be uploaded
2025-09-23 09:41:40,079 [ 650202]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (a68bf4c6aa44940f448f4c63c22ebdff0d5fbee5c16c891280c6b13e5d888130) is unknown but expected to be uploaded
2025-09-23 09:41:40,079 [ 650202]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (a68bf4c6aa44940f448f4c63c22ebdff0d5fbee5c16c891280c6b13e5d888130) is unknown but expected to be uploaded
2025-09-23 09:41:40,079 [ 650202]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (a68bf4c6aa44940f448f4c63c22ebdff0d5fbee5c16c891280c6b13e5d888130) is unknown but expected to be uploaded
2025-09-23 09:41:40,079 [ 650202]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (a68bf4c6aa44940f448f4c63c22ebdff0d5fbee5c16c891280c6b13e5d888130) is unknown but expected to be uploaded
2025-09-23 09:41:40,079 [ 650202]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (a68bf4c6aa44940f448f4c63c22ebdff0d5fbee5c16c891280c6b13e5d888130) is unknown but expected to be uploaded
2025-09-23 09:41:40,629 [ 650752]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 4ee5b1641efe8a8bc51a6417ac3b45c7d97166a9cf841d977346c892a3e67fd9 -> 99f3022640b02b4cf762756a9a9b96673fc4b5f7c216214183542a16a9f1ab66
2025-09-23 09:41:41,194 [ 651317]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: a68bf4c6aa44940f448f4c63c22ebdff0d5fbee5c16c891280c6b13e5d888130 -> 99f3022640b02b4cf762756a9a9b96673fc4b5f7c216214183542a16a9f1ab66
2025-09-23 09:41:41,436 [ 651559]   WARN - #com.augmentcode.intellij.webviews.AbstractWebviewMessagingService - Failed to parse webview message as protobuf.
Exception: Missing type url when parsing: {"type":"can-show-terminal","data":{"command":"curl -X GET http://localhost:5000/api/health"}}
Message: {"type":"async-wrapper","requestId":"2f352bae-47ee-494d-99bf-9f103827fd0c","error":null,"baseMsg":{"type":"can-show-terminal","data":{"command":"curl -X GET http://localhost:5000/api/health"}},"destination":"host"}
2025-09-23 09:41:41,477 [ 651600]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_ERROR]: Failed to check if terminal can be shown: Error: Unable to parse webview message: {"type":"async-wrapper","requestId":"2f352bae-47ee-494d-99bf-9f103827fd0c","error":null,"baseMsg":{"type":"can-show-terminal","data":{"command":"curl -X GET http://localhost:5000/api/health"}},"destination":"host"} [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:41:41,550 [ 651673]   WARN - #com.augmentcode.intellij.chat.ChatWebviewMessageClient$Companion - Workspace guidelines are empty, not including in chat request. 
2025-09-23 09:41:41,662 [ 651785]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: a68bf4c6aa44940f448f4c63c22ebdff0d5fbee5c16c891280c6b13e5d888130 -> 99f3022640b02b4cf762756a9a9b96673fc4b5f7c216214183542a16a9f1ab66
2025-09-23 09:41:42,137 [ 652260]   WARN - #c.i.e.wsl - Failed to get WSL mount root for Ubuntu: DOS working directory is expected, but got \\wsl.localhost\Ubuntu\home\oni\audio-transcriber
2025-09-23 09:41:42,309 [ 652432]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: a68bf4c6aa44940f448f4c63c22ebdff0d5fbee5c16c891280c6b13e5d888130 -> 99f3022640b02b4cf762756a9a9b96673fc4b5f7c216214183542a16a9f1ab66
2025-09-23 09:41:42,856 [ 652979]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: a68bf4c6aa44940f448f4c63c22ebdff0d5fbee5c16c891280c6b13e5d888130 -> 5b3f50d9649f8eb19ba67951a1d3d013812c30282f81dec870f7c7d8f09689fc
2025-09-23 09:41:43,668 [ 653791]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: a68bf4c6aa44940f448f4c63c22ebdff0d5fbee5c16c891280c6b13e5d888130 -> 5b3f50d9649f8eb19ba67951a1d3d013812c30282f81dec870f7c7d8f09689fc
2025-09-23 09:41:44,121 [ 654244]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: a68bf4c6aa44940f448f4c63c22ebdff0d5fbee5c16c891280c6b13e5d888130 -> 5b3f50d9649f8eb19ba67951a1d3d013812c30282f81dec870f7c7d8f09689fc
2025-09-23 09:41:44,141 [ 654264]   WARN - #c.i.e.wsl - Failed to get WSL mount root for Ubuntu: DOS working directory is expected, but got \\wsl.localhost\Ubuntu\home\oni\audio-transcriber
2025-09-23 09:41:44,780 [ 654903]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: a68bf4c6aa44940f448f4c63c22ebdff0d5fbee5c16c891280c6b13e5d888130 -> 9b85664be9a21aeab7b9553a2e0fc05d3186899d3de584d7bedc9b87e8a92678
2025-09-23 09:41:44,965 [ 655088]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: No exchange with this request ID found. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:41:45,230 [ 655353]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 5b3f50d9649f8eb19ba67951a1d3d013812c30282f81dec870f7c7d8f09689fc -> 9b85664be9a21aeab7b9553a2e0fc05d3186899d3de584d7bedc9b87e8a92678
2025-09-23 09:41:48,671 [ 658794]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (99f3022640b02b4cf762756a9a9b96673fc4b5f7c216214183542a16a9f1ab66) is unknown but expected to be uploaded
2025-09-23 09:41:48,671 [ 658794]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (99f3022640b02b4cf762756a9a9b96673fc4b5f7c216214183542a16a9f1ab66) is unknown but expected to be uploaded
2025-09-23 09:41:48,671 [ 658794]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (99f3022640b02b4cf762756a9a9b96673fc4b5f7c216214183542a16a9f1ab66) is unknown but expected to be uploaded
2025-09-23 09:41:48,671 [ 658794]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (99f3022640b02b4cf762756a9a9b96673fc4b5f7c216214183542a16a9f1ab66) is unknown but expected to be uploaded
2025-09-23 09:41:48,671 [ 658794]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (99f3022640b02b4cf762756a9a9b96673fc4b5f7c216214183542a16a9f1ab66) is unknown but expected to be uploaded
2025-09-23 09:41:48,671 [ 658794]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (9b85664be9a21aeab7b9553a2e0fc05d3186899d3de584d7bedc9b87e8a92678) is unknown but expected to be uploaded
2025-09-23 09:41:48,671 [ 658794]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (9b85664be9a21aeab7b9553a2e0fc05d3186899d3de584d7bedc9b87e8a92678) is unknown but expected to be uploaded
2025-09-23 09:41:49,333 [ 659456]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 99f3022640b02b4cf762756a9a9b96673fc4b5f7c216214183542a16a9f1ab66 -> 9b85664be9a21aeab7b9553a2e0fc05d3186899d3de584d7bedc9b87e8a92678
2025-09-23 09:41:49,915 [ 660038]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 99f3022640b02b4cf762756a9a9b96673fc4b5f7c216214183542a16a9f1ab66 -> 9b85664be9a21aeab7b9553a2e0fc05d3186899d3de584d7bedc9b87e8a92678
2025-09-23 09:41:50,443 [ 660566]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 99f3022640b02b4cf762756a9a9b96673fc4b5f7c216214183542a16a9f1ab66 -> 9b85664be9a21aeab7b9553a2e0fc05d3186899d3de584d7bedc9b87e8a92678
2025-09-23 09:41:51,460 [ 661583]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 99f3022640b02b4cf762756a9a9b96673fc4b5f7c216214183542a16a9f1ab66 -> 9b85664be9a21aeab7b9553a2e0fc05d3186899d3de584d7bedc9b87e8a92678
2025-09-23 09:41:51,917 [ 662040]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 99f3022640b02b4cf762756a9a9b96673fc4b5f7c216214183542a16a9f1ab66 -> 9b85664be9a21aeab7b9553a2e0fc05d3186899d3de584d7bedc9b87e8a92678
2025-09-23 09:41:54,158 [ 664281]   WARN - #c.i.e.wsl - Failed to get WSL mount root for Ubuntu: DOS working directory is expected, but got \\wsl.localhost\Ubuntu\home\oni\audio-transcriber
2025-09-23 09:41:55,391 [ 665514]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (5b3f50d9649f8eb19ba67951a1d3d013812c30282f81dec870f7c7d8f09689fc) is unknown but expected to be uploaded
2025-09-23 09:41:55,391 [ 665514]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (5b3f50d9649f8eb19ba67951a1d3d013812c30282f81dec870f7c7d8f09689fc) is unknown but expected to be uploaded
2025-09-23 09:41:55,391 [ 665514]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (5b3f50d9649f8eb19ba67951a1d3d013812c30282f81dec870f7c7d8f09689fc) is unknown but expected to be uploaded
2025-09-23 09:41:55,391 [ 665514]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (9b85664be9a21aeab7b9553a2e0fc05d3186899d3de584d7bedc9b87e8a92678) is unknown but expected to be uploaded
2025-09-23 09:41:55,391 [ 665514]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (9b85664be9a21aeab7b9553a2e0fc05d3186899d3de584d7bedc9b87e8a92678) is unknown but expected to be uploaded
2025-09-23 09:41:55,391 [ 665514]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (9b85664be9a21aeab7b9553a2e0fc05d3186899d3de584d7bedc9b87e8a92678) is unknown but expected to be uploaded
2025-09-23 09:41:55,391 [ 665514]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (9b85664be9a21aeab7b9553a2e0fc05d3186899d3de584d7bedc9b87e8a92678) is unknown but expected to be uploaded
2025-09-23 09:41:55,391 [ 665514]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (9b85664be9a21aeab7b9553a2e0fc05d3186899d3de584d7bedc9b87e8a92678) is unknown but expected to be uploaded
2025-09-23 09:41:55,391 [ 665514]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (9b85664be9a21aeab7b9553a2e0fc05d3186899d3de584d7bedc9b87e8a92678) is unknown but expected to be uploaded
2025-09-23 09:41:55,391 [ 665514]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (9b85664be9a21aeab7b9553a2e0fc05d3186899d3de584d7bedc9b87e8a92678) is unknown but expected to be uploaded
2025-09-23 09:41:55,535 [ 665658]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 9b85664be9a21aeab7b9553a2e0fc05d3186899d3de584d7bedc9b87e8a92678 -> 18135d167dfe6035c61c0646c3e26e1e2e19f04056b81121bc9a4774a5943e24
2025-09-23 09:41:55,579 [ 665702]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: IntersectionDisplay context not found.  Call setIntersectionContext first. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:41:55,707 [ 665830]   WARN - #com.augmentcode.intellij.chat.ChatWebviewMessageClient$Companion - Workspace guidelines are empty, not including in chat request. 
2025-09-23 09:41:55,904 [ 666027]   WARN - #c.i.e.wsl - Failed to get WSL mount root for Ubuntu: DOS working directory is expected, but got \\wsl.localhost\Ubuntu\home\oni\audio-transcriber
2025-09-23 09:41:56,036 [ 666159]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 5b3f50d9649f8eb19ba67951a1d3d013812c30282f81dec870f7c7d8f09689fc -> 18135d167dfe6035c61c0646c3e26e1e2e19f04056b81121bc9a4774a5943e24
2025-09-23 09:41:56,550 [ 666673]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 5b3f50d9649f8eb19ba67951a1d3d013812c30282f81dec870f7c7d8f09689fc -> 18135d167dfe6035c61c0646c3e26e1e2e19f04056b81121bc9a4774a5943e24
2025-09-23 09:41:57,064 [ 667187]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 9b85664be9a21aeab7b9553a2e0fc05d3186899d3de584d7bedc9b87e8a92678 -> 18135d167dfe6035c61c0646c3e26e1e2e19f04056b81121bc9a4774a5943e24
2025-09-23 09:41:57,511 [ 667634]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 9b85664be9a21aeab7b9553a2e0fc05d3186899d3de584d7bedc9b87e8a92678 -> 18135d167dfe6035c61c0646c3e26e1e2e19f04056b81121bc9a4774a5943e24
2025-09-23 09:41:58,196 [ 668319]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 9b85664be9a21aeab7b9553a2e0fc05d3186899d3de584d7bedc9b87e8a92678 -> a90e89e0cc1c459a9d09743c1c3ae7be62a59ae71aa94c99f97734d5f4f33851
2025-09-23 09:41:58,642 [ 668765]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 5b3f50d9649f8eb19ba67951a1d3d013812c30282f81dec870f7c7d8f09689fc -> a90e89e0cc1c459a9d09743c1c3ae7be62a59ae71aa94c99f97734d5f4f33851
2025-09-23 09:41:59,262 [ 669385]   WARN - #com.augmentcode.intellij.sentry.SentryMetadataCollector - Git ls-files command failed with exit code: 128
2025-09-23 09:41:59,685 [ 669808]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 9b85664be9a21aeab7b9553a2e0fc05d3186899d3de584d7bedc9b87e8a92678 -> a90e89e0cc1c459a9d09743c1c3ae7be62a59ae71aa94c99f97734d5f4f33851
2025-09-23 09:41:59,844 [ 669967]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: No exchange with this request ID found. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:42:00,180 [ 670303]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 9b85664be9a21aeab7b9553a2e0fc05d3186899d3de584d7bedc9b87e8a92678 -> a90e89e0cc1c459a9d09743c1c3ae7be62a59ae71aa94c99f97734d5f4f33851
2025-09-23 09:42:00,653 [ 670776]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 9b85664be9a21aeab7b9553a2e0fc05d3186899d3de584d7bedc9b87e8a92678 -> a90e89e0cc1c459a9d09743c1c3ae7be62a59ae71aa94c99f97734d5f4f33851
2025-09-23 09:42:04,090 [ 674213]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (a90e89e0cc1c459a9d09743c1c3ae7be62a59ae71aa94c99f97734d5f4f33851) is unknown but expected to be uploaded
2025-09-23 09:42:04,091 [ 674214]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (a90e89e0cc1c459a9d09743c1c3ae7be62a59ae71aa94c99f97734d5f4f33851) is unknown but expected to be uploaded
2025-09-23 09:42:04,091 [ 674214]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (a90e89e0cc1c459a9d09743c1c3ae7be62a59ae71aa94c99f97734d5f4f33851) is unknown but expected to be uploaded
2025-09-23 09:42:04,091 [ 674214]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (a90e89e0cc1c459a9d09743c1c3ae7be62a59ae71aa94c99f97734d5f4f33851) is unknown but expected to be uploaded
2025-09-23 09:42:04,091 [ 674214]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (a90e89e0cc1c459a9d09743c1c3ae7be62a59ae71aa94c99f97734d5f4f33851) is unknown but expected to be uploaded
2025-09-23 09:42:04,147 [ 674270]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 9b85664be9a21aeab7b9553a2e0fc05d3186899d3de584d7bedc9b87e8a92678 -> a90e89e0cc1c459a9d09743c1c3ae7be62a59ae71aa94c99f97734d5f4f33851
2025-09-23 09:42:06,887 [ 677010]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: IntersectionDisplay context not found.  Call setIntersectionContext first. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:42:07,939 [ 678062]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: Loading changes  for 2d5e3a2a-fed3-4dbe-b1ec-d84785350992 retry 0:  Error: No changes found [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:42:07,940 [ 678063]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: Loading changes  for 2d5e3a2a-fed3-4dbe-b1ec-d84785350992 retry 1:  Error: No changes found [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:42:07,952 [ 678075]   WARN - #com.augmentcode.intellij.chat.ChatWebviewMessageClient$Companion - Workspace guidelines are empty, not including in chat request. 
2025-09-23 09:42:08,443 [ 678566]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: Loading changes  for 2d5e3a2a-fed3-4dbe-b1ec-d84785350992 retry 2:  Error: No changes found [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:42:08,443 [ 678566]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: Loading changes  for 2d5e3a2a-fed3-4dbe-b1ec-d84785350992 retry 3:  Error: No changes found [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:42:08,443 [ 678566]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: Loading changes failed for 2d5e3a2a-fed3-4dbe-b1ec-d84785350992:  Error: No changes found [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:42:08,947 [ 679070]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: Loading changes  for 2d5e3a2a-fed3-4dbe-b1ec-d84785350992 retry 4:  Error: No changes found [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:42:08,947 [ 679070]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: Loading changes failed for 2d5e3a2a-fed3-4dbe-b1ec-d84785350992:  Error: No changes found [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:42:09,638 [ 679761]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (a90e89e0cc1c459a9d09743c1c3ae7be62a59ae71aa94c99f97734d5f4f33851) is unknown but expected to be uploaded
2025-09-23 09:42:09,638 [ 679761]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (a90e89e0cc1c459a9d09743c1c3ae7be62a59ae71aa94c99f97734d5f4f33851) is unknown but expected to be uploaded
2025-09-23 09:42:09,638 [ 679761]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (a90e89e0cc1c459a9d09743c1c3ae7be62a59ae71aa94c99f97734d5f4f33851) is unknown but expected to be uploaded
2025-09-23 09:42:09,638 [ 679761]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (a90e89e0cc1c459a9d09743c1c3ae7be62a59ae71aa94c99f97734d5f4f33851) is unknown but expected to be uploaded
2025-09-23 09:42:09,638 [ 679761]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (a90e89e0cc1c459a9d09743c1c3ae7be62a59ae71aa94c99f97734d5f4f33851) is unknown but expected to be uploaded
2025-09-23 09:42:11,337 [ 681460]   WARN - #c.i.e.wsl - Failed to get WSL mount root for Ubuntu: DOS working directory is expected, but got \\wsl.localhost\Ubuntu\home\oni\audio-transcriber
2025-09-23 09:42:11,786 [ 681909]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: a90e89e0cc1c459a9d09743c1c3ae7be62a59ae71aa94c99f97734d5f4f33851 -> 747858e75d9582b8a4fe8ceb447b0109d40a3bfeb01c460f8b5aaf585051b019
2025-09-23 09:42:12,280 [ 682403]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: a90e89e0cc1c459a9d09743c1c3ae7be62a59ae71aa94c99f97734d5f4f33851 -> 747858e75d9582b8a4fe8ceb447b0109d40a3bfeb01c460f8b5aaf585051b019
2025-09-23 09:42:12,308 [ 682431]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: No exchange with this request ID found. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:42:13,957 [ 684080]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_WARNING]: IntersectionDisplay context not found.  Call setIntersectionContext first. [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:42:14,206 [ 684329]   WARN - #com.augmentcode.intellij.webviews.AbstractWebviewMessagingService - Failed to parse webview message as protobuf.
Exception: Missing type url when parsing: {"type":"can-show-terminal","data":{"command":"npm test -- --watchAll=false"}}
Message: {"type":"async-wrapper","requestId":"575a247d-a28a-4480-8ed0-f363d3ad736b","error":null,"baseMsg":{"type":"can-show-terminal","data":{"command":"npm test -- --watchAll=false"}},"destination":"host"}
2025-09-23 09:42:14,213 [ 684336]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_ERROR]: Failed to check if terminal can be shown: Error: Unable to parse webview message: {"type":"async-wrapper","requestId":"575a247d-a28a-4480-8ed0-f363d3ad736b","error":null,"baseMsg":{"type":"can-show-terminal","data":{"command":"npm test -- --watchAll=false"}},"destination":"host"} [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
2025-09-23 09:42:15,721 [ 685844]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (18135d167dfe6035c61c0646c3e26e1e2e19f04056b81121bc9a4774a5943e24) is unknown but expected to be uploaded
2025-09-23 09:42:15,721 [ 685844]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (18135d167dfe6035c61c0646c3e26e1e2e19f04056b81121bc9a4774a5943e24) is unknown but expected to be uploaded
2025-09-23 09:42:15,721 [ 685844]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (18135d167dfe6035c61c0646c3e26e1e2e19f04056b81121bc9a4774a5943e24) is unknown but expected to be uploaded
2025-09-23 09:42:15,721 [ 685844]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (18135d167dfe6035c61c0646c3e26e1e2e19f04056b81121bc9a4774a5943e24) is unknown but expected to be uploaded
2025-09-23 09:42:15,721 [ 685844]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (18135d167dfe6035c61c0646c3e26e1e2e19f04056b81121bc9a4774a5943e24) is unknown but expected to be uploaded
2025-09-23 09:42:15,721 [ 685844]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (18135d167dfe6035c61c0646c3e26e1e2e19f04056b81121bc9a4774a5943e24) is unknown but expected to be uploaded
2025-09-23 09:42:15,721 [ 685844]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep - Blob nohup.out (18135d167dfe6035c61c0646c3e26e1e2e19f04056b81121bc9a4774a5943e24) is unknown but expected to be uploaded
2025-09-23 09:42:15,771 [ 685894]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: a90e89e0cc1c459a9d09743c1c3ae7be62a59ae71aa94c99f97734d5f4f33851 -> 747858e75d9582b8a4fe8ceb447b0109d40a3bfeb01c460f8b5aaf585051b019
2025-09-23 09:42:16,289 [ 686412]   WARN - #com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader - Upload blob name mismatch: 18135d167dfe6035c61c0646c3e26e1e2e19f04056b81121bc9a4774a5943e24 -> 747858e75d9582b8a4fe8ceb447b0109d40a3bfeb01c460f8b5aaf585051b019
2025-09-23 09:42:16,509 [ 686632]   WARN - #com.augmentcode.intellij.webviews.AbstractWebviewMessagingService - Failed to parse webview message as protobuf.
Exception: Missing type url when parsing: {"type":"can-show-terminal","data":{"command":"npm test -- --watchAll=false"}}
Message: {"type":"async-wrapper","requestId":"9c4ff8ba-d93e-46ef-97e2-cf2a832d0229","error":null,"baseMsg":{"type":"can-show-terminal","data":{"command":"npm test -- --watchAll=false"}},"destination":"host"}
2025-09-23 09:42:16,538 [ 686661]   WARN - #com.augmentcode.intellij.webviews.WebviewFactory$create$1$consoleHandler$1 - Webview[CHAT_STATE][LOGSEVERITY_ERROR]: Failed to check if terminal can be shown: Error: Unable to parse webview message: {"type":"async-wrapper","requestId":"9c4ff8ba-d93e-46ef-97e2-cf2a832d0229","error":null,"baseMsg":{"type":"can-show-terminal","data":{"command":"npm test -- --watchAll=false"}},"destination":"host"} [http://augment.localhost/assets/initialize-DVX7iRDU.js:12]
